<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-ar="إدارة المهام والمتابعة - نظام إدارة العملاء والموردين" data-en="Task Management & Follow-up - Customer & Supplier Management System">إدارة المهام والمتابعة - نظام إدارة العملاء والموردين</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link rel="stylesheet" href="css/tasks.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-tasks"></i>
            <span data-ar="نظام الإدارة" data-en="Management System">نظام الإدارة</span>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span data-ar="لوحة التحكم" data-en="Dashboard">لوحة التحكم</span>
            </a>
            
            <a href="customers.html" class="nav-item">
                <i class="fas fa-users"></i>
                <span data-ar="العملاء" data-en="Customers">العملاء</span>
                <span class="nav-badge" id="customersCount">0</span>
            </a>
            
            <a href="suppliers.html" class="nav-item">
                <i class="fas fa-truck"></i>
                <span data-ar="الموردين" data-en="Suppliers">الموردين</span>
                <span class="nav-badge" id="suppliersCount">0</span>
            </a>
            
            <a href="quotations.html" class="nav-item">
                <i class="fas fa-file-invoice-dollar"></i>
                <span data-ar="عروض الأسعار" data-en="Quotations">عروض الأسعار</span>
                <span class="nav-badge" id="quotationsCount">0</span>
            </a>
            
            <a href="telesales.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span data-ar="فريق التلي سيلز" data-en="Telesales Team">فريق التلي سيلز</span>
                <span class="nav-badge" id="telesalesCount">0</span>
            </a>
            
            <a href="tasks.html" class="nav-item active">
                <i class="fas fa-tasks"></i>
                <span data-ar="إدارة المهام" data-en="Task Management">إدارة المهام</span>
                <span class="nav-badge" id="tasksCount">0</span>
            </a>
            
            <a href="reports.html" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span data-ar="التقارير" data-en="Reports">التقارير</span>
            </a>
            
            <a href="activities.html" class="nav-item">
                <i class="fas fa-history"></i>
                <span data-ar="سجل الأنشطة" data-en="Activity Log">سجل الأنشطة</span>
            </a>
        </nav>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- رأس الصفحة -->
        <header class="page-header">
            <div class="header-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 data-ar="إدارة المهام والمتابعة" data-en="Task Management & Follow-up">إدارة المهام والمتابعة</h1>
            </div>
            
            <div class="header-right">
                <button class="language-toggle" onclick="toggleLanguage()">
                    <i class="fas fa-globe"></i>
                    <span id="currentLang">العربية</span>
                </button>
                
                <div class="user-menu">
                    <button class="user-menu-toggle" onclick="toggleUserMenu()">
                        <i class="fas fa-user-circle"></i>
                        <span id="userName">المستخدم</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-menu-dropdown" id="userMenuDropdown">
                        <a href="#" onclick="showProfile()">
                            <i class="fas fa-user"></i>
                            <span data-ar="الملف الشخصي" data-en="Profile">الملف الشخصي</span>
                        </a>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span data-ar="الإعدادات" data-en="Settings">الإعدادات</span>
                        </a>
                        <a href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            <span data-ar="تسجيل الخروج" data-en="Logout">تسجيل الخروج</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- إحصائيات المهام السريعة -->
        <div class="tasks-stats-dashboard">
            <div class="task-stat-card total-tasks">
                <div class="stat-icon-wrapper">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalTasksCount">0</h3>
                    <p>إجمالي المهام</p>
                    <div class="stat-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="totalTasksProgress"></div>
                        </div>
                        <span class="progress-text">هذا الشهر</span>
                    </div>
                </div>
            </div>
            
            <div class="task-stat-card pending-tasks">
                <div class="stat-icon-wrapper">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3 id="pendingTasksCount">0</h3>
                    <p>مهام معلقة</p>
                    <div class="stat-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="pendingTasksProgress"></div>
                        </div>
                        <span class="progress-text">تحتاج متابعة</span>
                    </div>
                </div>
            </div>
            
            <div class="task-stat-card completed-tasks">
                <div class="stat-icon-wrapper">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3 id="completedTasksCount">0</h3>
                    <p>مهام مكتملة</p>
                    <div class="stat-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="completedTasksProgress"></div>
                        </div>
                        <span class="progress-text">معدل الإنجاز</span>
                    </div>
                </div>
            </div>
            
            <div class="task-stat-card overdue-tasks">
                <div class="stat-icon-wrapper">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <h3 id="overdueTasksCount">0</h3>
                    <p>مهام متأخرة</p>
                    <div class="stat-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="overdueTasksProgress"></div>
                        </div>
                        <span class="progress-text">تحتاج عناية</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم في المهام -->
        <div class="tasks-controls-section">
            <div class="controls-header">
                <h2 class="section-title">
                    <i class="fas fa-cogs"></i>
                    أدوات إدارة المهام
                </h2>
                <div class="quick-actions">
                    <button class="btn-primary-gradient" onclick="showAddTaskModal()">
                        <i class="fas fa-plus"></i>
                        إضافة مهمة جديدة
                    </button>
                    <button class="btn-secondary-outline" onclick="showBulkActionsModal()">
                        <i class="fas fa-list-check"></i>
                        إجراءات مجمعة
                    </button>
                    <button class="btn-info-outline" onclick="exportTasks()">
                        <i class="fas fa-download"></i>
                        تصدير المهام
                    </button>
                </div>
            </div>
            
            <div class="filters-and-views">
                <div class="filters-section">
                    <div class="filter-group">
                        <label class="filter-label">
                            <i class="fas fa-search"></i>
                            البحث في المهام
                        </label>
                        <div class="search-input-wrapper">
                            <input type="text" id="taskSearch" class="enhanced-search" placeholder="ابحث في المهام..." onkeyup="searchTasks()">
                            <button class="search-clear-btn" onclick="clearTaskSearch()" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">
                            <i class="fas fa-filter"></i>
                            حالة المهمة
                        </label>
                        <select id="statusFilter" class="enhanced-select" onchange="filterTasks()">
                            <option value="">جميع الحالات</option>
                            <option value="pending">معلقة</option>
                            <option value="in_progress">قيد التنفيذ</option>
                            <option value="completed">مكتملة</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">
                            <i class="fas fa-flag"></i>
                            الأولوية
                        </label>
                        <select id="priorityFilter" class="enhanced-select" onchange="filterTasks()">
                            <option value="">جميع الأولويات</option>
                            <option value="high">عالية</option>
                            <option value="medium">متوسطة</option>
                            <option value="low">منخفضة</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">
                            <i class="fas fa-user"></i>
                            المسؤول
                        </label>
                        <select id="assigneeFilter" class="enhanced-select" onchange="filterTasks()">
                            <option value="">جميع المسؤولين</option>
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">
                            <i class="fas fa-calendar"></i>
                            تاريخ الاستحقاق
                        </label>
                        <select id="dueDateFilter" class="enhanced-select" onchange="filterTasks()">
                            <option value="">جميع التواريخ</option>
                            <option value="today">اليوم</option>
                            <option value="tomorrow">غداً</option>
                            <option value="this_week">هذا الأسبوع</option>
                            <option value="next_week">الأسبوع القادم</option>
                            <option value="overdue">متأخرة</option>
                        </select>
                    </div>
                </div>
                
                <div class="view-options">
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="list" onclick="switchView('list')">
                            <i class="fas fa-list"></i>
                            قائمة
                        </button>
                        <button class="view-btn" data-view="kanban" onclick="switchView('kanban')">
                            <i class="fas fa-columns"></i>
                            كانبان
                        </button>
                        <button class="view-btn" data-view="calendar" onclick="switchView('calendar')">
                            <i class="fas fa-calendar"></i>
                            تقويم
                        </button>
                    </div>
                    
                    <div class="sort-options">
                        <select id="sortTasks" class="enhanced-select" onchange="sortTasks()">
                            <option value="due_date">ترتيب حسب تاريخ الاستحقاق</option>
                            <option value="priority">ترتيب حسب الأولوية</option>
                            <option value="created_date">ترتيب حسب تاريخ الإنشاء</option>
                            <option value="status">ترتيب حسب الحالة</option>
                            <option value="assignee">ترتيب حسب المسؤول</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض المهام - قائمة -->
        <div class="tasks-view-container">
            <div id="listView" class="tasks-list-view active">
                <div class="tasks-table-section">
                    <div class="table-header-controls">
                        <div class="table-title">
                            <h3>
                                <i class="fas fa-list"></i>
                                قائمة المهام
                            </h3>
                            <div class="table-actions">
                                <button class="btn-table-action" onclick="refreshTasks()" title="تحديث">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn-table-action" onclick="toggleTableView()" title="تبديل العرض">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="btn-table-action" onclick="showTableSettings()" title="إعدادات الجدول">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-pagination-top">
                            <div class="pagination-info">
                                <span>عرض</span>
                                <select id="itemsPerPage" class="items-per-page" onchange="changeItemsPerPage()">
                                    <option value="10">10</option>
                                    <option value="25" selected>25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span>عنصر في الصفحة</span>
                            </div>
                            
                            <div class="bulk-actions" id="bulkActions" style="display: none;">
                                <span class="selected-count">تم تحديد <span id="selectedTasksCount">0</span> مهمة</span>
                                <button class="btn-bulk-action" onclick="bulkMarkComplete()">
                                    <i class="fas fa-check"></i>
                                    تمييز كمكتملة
                                </button>
                                <button class="btn-bulk-action" onclick="bulkDelete()">
                                    <i class="fas fa-trash"></i>
                                    حذف المحدد
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="enhanced-table-container">
                        <table class="enhanced-tasks-table" id="tasksTable">
                            <thead>
                                <tr>
                                    <th class="checkbox-column">
                                        <input type="checkbox" id="selectAllTasks" onchange="toggleSelectAllTasks()">
                                    </th>
                                    <th class="sortable" onclick="sortTableBy('title')">
                                        عنوان المهمة
                                        <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" onclick="sortTableBy('assignee')">
                                        المسؤول
                                        <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" onclick="sortTableBy('priority')">
                                        الأولوية
                                        <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" onclick="sortTableBy('status')">
                                        الحالة
                                        <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" onclick="sortTableBy('due_date')">
                                        تاريخ الاستحقاق
                                        <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" onclick="sortTableBy('progress')">
                                        التقدم
                                        <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="actions-column">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="tasksTableBody">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                        
                        <!-- حالة الجدول الفارغ -->
                        <div id="emptyTasksState" class="empty-state" style="display: none;">
                            <div class="empty-state-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h3>لا توجد مهام</h3>
                            <p>لم يتم العثور على أي مهام تطابق المعايير المحددة</p>
                            <button class="btn-primary-gradient" onclick="showAddTaskModal()">
                                <i class="fas fa-plus"></i>
                                إضافة مهمة جديدة
                            </button>
                        </div>
                        
                        <!-- حالة التحميل -->
                        <div id="loadingTasksState" class="loading-state" style="display: none;">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                            <p>جاري تحميل المهام...</p>
                        </div>
                    </div>
                    
                    <div class="table-footer">
                        <div class="table-summary">
                            <span class="summary-text">
                                عرض <span id="currentPageStart">1</span> - <span id="currentPageEnd">25</span> 
                                من <span id="totalTasksCount">0</span> مهمة
                            </span>
                        </div>
                        
                        <div class="table-pagination" id="tasksPagination">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/tasks.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
    
    <script>
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من تسجيل الدخول
            if (!requireAuth()) return;
            
            // تحديث اللغة
            updateLanguage();
            
            // تحميل المهام
            loadTasks();
            
            // تهيئة الفلاتر
            initializeFilters();
            
            // تسجيل النشاط
            logActivity('tasks_view', 'عرض صفحة إدارة المهام');
        });
    </script>
</body>
</html>
