// ===== إدارة الموردين =====

let suppliers = [];
let filteredSuppliers = [];
let currentPage = 1;
let itemsPerPage = 10;
let sortBy = 'name';
let sortOrder = 'asc';

// تهيئة صفحة الموردين
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_suppliers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // تهيئة البيانات
    initializeSuppliers();
    
    // ربط الأحداث
    bindEvents();
    
    // تحديث العرض
    updateSuppliersDisplay();
    
    // تسجيل النشاط
    logActivity('suppliers_view', 'عرض صفحة إدارة الموردين');
});

// تهيئة بيانات الموردين
function initializeSuppliers() {
    // تحميل الموردين من التخزين المحلي
    suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
    
    // إضافة بيانات تجريبية إذا لم توجد بيانات
    if (suppliers.length === 0) {
        suppliers = [
            {
                id: generateId(),
                name: 'شركة التقنية المتقدمة',
                serviceType: 'خدمات تقنية',
                contactMethod: 'هاتف: 0112345678 - بريد: <EMAIL>',
                address: 'شارع الملك فهد، حي العليا',
                city: 'الرياض',
                notes: 'متخصصون في تطوير البرمجيات وأنظمة الإدارة',
                status: 'active',
                createdAt: '2024-01-10',
                createdBy: 'admin'
            },
            {
                id: generateId(),
                name: 'مؤسسة الخدمات اللوجستية',
                serviceType: 'خدمات شحن ونقل',
                contactMethod: 'جوال: 0501234567 - واتساب: 0501234567',
                address: 'المنطقة الصناعية الثانية',
                city: 'جدة',
                notes: 'خدمات شحن محلية ودولية',
                status: 'active',
                createdAt: '2024-01-08',
                createdBy: 'supervisor'
            },
            {
                id: generateId(),
                name: 'شركة التسويق الرقمي',
                serviceType: 'تسويق وإعلان',
                contactMethod: 'بريد: <EMAIL> - هاتف: 0138765432',
                address: 'برج الأعمال، الكورنيش',
                city: 'الدمام',
                notes: 'خدمات تسويق رقمي ووسائل التواصل الاجتماعي',
                status: 'active',
                createdAt: '2024-01-05',
                createdBy: 'admin'
            },
            {
                id: generateId(),
                name: 'مكتب الاستشارات القانونية',
                serviceType: 'استشارات قانونية',
                contactMethod: 'هاتف: 0114567890 - فاكس: 0114567891',
                address: 'حي الملز، شارع التحلية',
                city: 'الرياض',
                notes: 'استشارات قانونية للشركات والأفراد',
                status: 'inactive',
                createdAt: '2024-01-03',
                createdBy: 'supervisor'
            }
        ];
        
        saveSuppliers();
    }
    
    filteredSuppliers = [...suppliers];
}

// حفظ الموردين في التخزين المحلي
function saveSuppliers() {
    localStorage.setItem('suppliers', JSON.stringify(suppliers));
}

// ربط الأحداث
function bindEvents() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
    
    // الفلاتر
    const filters = ['serviceFilter', 'cityFilter', 'statusFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', handleFilter);
        }
    });
    
    // الترتيب
    const sortBy = document.getElementById('sortBy');
    if (sortBy) {
        sortBy.addEventListener('change', handleSort);
    }
}

// معالج البحث
function handleSearch(event) {
    const searchTerm = event.target.value.trim();
    
    if (searchTerm === '') {
        filteredSuppliers = [...suppliers];
    } else {
        filteredSuppliers = searchData(suppliers, searchTerm, [
            'name', 'serviceType', 'contactMethod', 'address', 'city', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateSuppliersDisplay();
}

// معالج الفلاتر
function handleFilter() {
    const serviceFilter = document.getElementById('serviceFilter').value;
    const cityFilter = document.getElementById('cityFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredSuppliers = filterData(suppliers, {
        serviceType: serviceFilter,
        city: cityFilter,
        status: statusFilter
    });
    
    // تطبيق البحث إذا كان موجوداً
    const searchTerm = document.getElementById('searchInput').value.trim();
    if (searchTerm) {
        filteredSuppliers = searchData(filteredSuppliers, searchTerm, [
            'name', 'serviceType', 'contactMethod', 'address', 'city', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateSuppliersDisplay();
}

// معالج الترتيب
function handleSort() {
    sortBy = document.getElementById('sortBy').value;
    filteredSuppliers = sortData(filteredSuppliers, sortBy, sortOrder);
    updateSuppliersDisplay();
}

// تبديل ترتيب الفرز
function toggleSortOrder() {
    sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    
    const sortOrderBtn = document.getElementById('sortOrderBtn');
    if (sortOrderBtn) {
        const icon = sortOrderBtn.querySelector('i');
        icon.className = sortOrder === 'asc' ? 'fas fa-sort-amount-down' : 'fas fa-sort-amount-up';
    }
    
    filteredSuppliers = sortData(filteredSuppliers, sortBy, sortOrder);
    updateSuppliersDisplay();
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('serviceFilter').value = 'all';
    document.getElementById('cityFilter').value = 'all';
    document.getElementById('statusFilter').value = 'all';
    
    filteredSuppliers = [...suppliers];
    currentPage = 1;
    updateSuppliersDisplay();
}

// تحديث عرض الموردين
function updateSuppliersDisplay() {
    updateSuppliersStats();
    updateFiltersOptions();
    updateSuppliersTable();
    updatePagination();
}

// تحديث إحصائيات الموردين
function updateSuppliersStats() {
    const totalSuppliers = suppliers.length;
    const activeSuppliers = suppliers.filter(s => s.status === 'active').length;
    const uniqueServices = [...new Set(suppliers.map(s => s.serviceType))].length;
    const uniqueCities = [...new Set(suppliers.map(s => s.city))].length;
    
    // تحديث العدادات
    updateCounter('totalSuppliersCount', totalSuppliers);
    updateCounter('activeSuppliersCount', activeSuppliers);
    updateCounter('servicesCount', uniqueServices);
    updateCounter('citiesCount', uniqueCities);
    
    // تحديث شارة التنقل
    updateNavBadge('suppliersBadge', totalSuppliers);
}

// تحديث خيارات الفلاتر
function updateFiltersOptions() {
    // تحديث فلتر الخدمات
    const services = [...new Set(suppliers.map(s => s.serviceType))].sort();
    updateFilterOptions('serviceFilter', services);
    
    // تحديث فلتر المدن
    const cities = [...new Set(suppliers.map(s => s.city))].sort();
    updateFilterOptions('cityFilter', cities);
}

// تحديث خيارات فلتر معين
function updateFilterOptions(filterId, options) {
    const filter = document.getElementById(filterId);
    if (!filter) return;
    
    const currentValue = filter.value;
    const firstOption = filter.querySelector('option[value="all"]');
    
    // مسح الخيارات الحالية (عدا الخيار الأول)
    filter.innerHTML = '';
    filter.appendChild(firstOption);
    
    // إضافة الخيارات الجديدة
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option;
        optionElement.textContent = option;
        filter.appendChild(optionElement);
    });
    
    // استعادة القيمة المحددة
    filter.value = currentValue;
}

// تحديث جدول الموردين
function updateSuppliersTable() {
    const tbody = document.getElementById('suppliersTableBody');
    if (!tbody) return;
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageSuppliers = filteredSuppliers.slice(startIndex, endIndex);
    
    if (pageSuppliers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 40px; color: var(--gray);">
                    <i class="fas fa-truck" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p data-ar="لا توجد موردين" data-en="No suppliers found">لا توجد موردين</p>
                </td>
            </tr>
        `;
        updateLanguage();
        return;
    }
    
    tbody.innerHTML = pageSuppliers.map(supplier => `
        <tr>
            <td><strong>${supplier.name}</strong></td>
            <td>${supplier.serviceType}</td>
            <td>${supplier.contactMethod}</td>
            <td>${supplier.address}</td>
            <td>${supplier.city}</td>
            <td>
                <span class="status-badge status-${supplier.status}">
                    ${getStatusDisplayName(supplier.status)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewSupplier('${supplier.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${hasPermission('edit_suppliers') || hasPermission('all') ? `
                        <button class="action-btn edit" onclick="editSupplier('${supplier.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : ''}
                    ${hasPermission('all') ? `
                        <button class="action-btn delete" onclick="deleteSupplier('${supplier.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// الحصول على اسم الحالة للعرض
function getStatusDisplayName(status) {
    const statuses = {
        'active': currentLanguage === 'ar' ? 'نشط' : 'Active',
        'inactive': currentLanguage === 'ar' ? 'غير نشط' : 'Inactive'
    };
    
    return statuses[status] || status;
}

// تحديث ترقيم الصفحات
function updatePagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;
    
    const totalPages = Math.ceil(filteredSuppliers.length / itemsPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = `
        <button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // إضافة أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-dots">...</span>';
        }
    }
    
    paginationHTML += `
        <button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="pagination-info">
            ${currentPage} من ${totalPages} (${filteredSuppliers.length} مورد)
        </div>
    `;
    
    pagination.innerHTML = paginationHTML;
}

// تغيير الصفحة
function changePage(page) {
    const totalPages = Math.ceil(filteredSuppliers.length / itemsPerPage);
    
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    updateSuppliersTable();
    updatePagination();
    
    // التمرير إلى أعلى الجدول
    document.querySelector('.table-section').scrollIntoView({ behavior: 'smooth' });
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تصدير الموردين
function exportSuppliers(format) {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'serviceType', label: 'نوع الخدمة' },
        { key: 'contactMethod', label: 'وسيلة التواصل' },
        { key: 'address', label: 'العنوان' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' },
        { key: 'notes', label: 'الملاحظات' }
    ];
    
    if (format === 'csv') {
        exportToCSV(filteredSuppliers, 'suppliers', headers);
    } else if (format === 'json') {
        exportToJSON(filteredSuppliers, 'suppliers');
    }
    
    logActivity('export_suppliers', `تصدير بيانات الموردين بصيغة ${format.toUpperCase()}`);
}

// طباعة الموردين
function printSuppliers() {
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'serviceType', label: 'نوع الخدمة' },
        { key: 'contactMethod', label: 'وسيلة التواصل' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' }
    ];
    
    printData('قائمة الموردين', filteredSuppliers, headers);
    logActivity('print_suppliers', 'طباعة قائمة الموردين');
}

// استيراد الموردين
function importSuppliers() {
    if (!hasPermission('add_suppliers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لاستيراد البيانات');
        return;
    }
    
    const fileInput = document.getElementById('importFileInput');
    fileInput.click();
}

// معالجة ملف الاستيراد
function handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const fileName = file.name.toLowerCase();
    
    if (fileName.endsWith('.csv')) {
        importFromCSV(file);
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        alert('استيراد ملفات Excel قيد التطوير. يرجى استخدام ملف CSV حالياً.');
    } else {
        alert('نوع الملف غير مدعوم. يرجى اختيار ملف CSV أو Excel.');
    }
    
    // مسح قيمة input
    event.target.value = '';
}

// استيراد من CSV
function importFromCSV(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
            
            const importedSuppliers = [];
            
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;
                
                const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
                
                if (values.length >= 5) {
                    const supplier = {
                        id: generateId(),
                        name: values[0] || '',
                        serviceType: values[1] || '',
                        contactMethod: values[2] || '',
                        address: values[3] || '',
                        city: values[4] || '',
                        status: values[5] || 'active',
                        notes: values[6] || '',
                        createdAt: new Date().toISOString(),
                        createdBy: getCurrentUser().username
                    };
                    
                    importedSuppliers.push(supplier);
                }
            }
            
            if (importedSuppliers.length > 0) {
                suppliers.push(...importedSuppliers);
                saveSuppliers();
                filteredSuppliers = [...suppliers];
                updateSuppliersDisplay();
                
                alert(`تم استيراد ${importedSuppliers.length} مورد بنجاح`);
                logActivity('import_suppliers', `استيراد ${importedSuppliers.length} مورد من ملف CSV`);
            } else {
                alert('لم يتم العثور على بيانات صالحة في الملف');
            }
            
        } catch (error) {
            console.error('خطأ في استيراد الملف:', error);
            alert('حدث خطأ أثناء استيراد الملف. يرجى التأكد من تنسيق الملف.');
        }
    };
    
    reader.readAsText(file, 'UTF-8');
}

// عرض نافذة إضافة مورد جديد
function showAddSupplierModal() {
    if (!hasPermission('add_suppliers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لإضافة موردين جدد');
        return;
    }

    showSupplierModal();
}

// عرض نافذة المورد (إضافة أو تعديل)
function showSupplierModal(supplierId = null) {
    const isEdit = supplierId !== null;
    const supplier = isEdit ? suppliers.find(s => s.id === supplierId) : null;

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content supplier-modal">
            <div class="modal-header">
                <h3 data-ar="${isEdit ? 'تعديل المورد' : 'إضافة مورد جديد'}" data-en="${isEdit ? 'Edit Supplier' : 'Add New Supplier'}">${isEdit ? 'تعديل المورد' : 'إضافة مورد جديد'}</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="supplierForm" class="supplier-form">
                    <!-- المعلومات الأساسية -->
                    <div class="form-section">
                        <h4><i class="fas fa-info-circle"></i> المعلومات الأساسية</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierName" data-ar="اسم المورد *" data-en="Supplier Name *">اسم المورد *</label>
                                <input type="text" id="supplierName" name="name" required value="${supplier?.name || ''}" placeholder="أدخل اسم المورد">
                            </div>
                            <div class="form-group">
                                <label for="supplierServiceType" data-ar="نوع الخدمة *" data-en="Service Type *">نوع الخدمة *</label>
                                <select id="supplierServiceType" name="serviceType" required>
                                    <option value="">اختر نوع الخدمة</option>
                                    <option value="خدمات تقنية" ${supplier?.serviceType === 'خدمات تقنية' ? 'selected' : ''}>خدمات تقنية</option>
                                    <option value="خدمات شحن ونقل" ${supplier?.serviceType === 'خدمات شحن ونقل' ? 'selected' : ''}>خدمات شحن ونقل</option>
                                    <option value="تسويق وإعلان" ${supplier?.serviceType === 'تسويق وإعلان' ? 'selected' : ''}>تسويق وإعلان</option>
                                    <option value="استشارات قانونية" ${supplier?.serviceType === 'استشارات قانونية' ? 'selected' : ''}>استشارات قانونية</option>
                                    <option value="خدمات مالية" ${supplier?.serviceType === 'خدمات مالية' ? 'selected' : ''}>خدمات مالية</option>
                                    <option value="خدمات طبية" ${supplier?.serviceType === 'خدمات طبية' ? 'selected' : ''}>خدمات طبية</option>
                                    <option value="تعليم وتدريب" ${supplier?.serviceType === 'تعليم وتدريب' ? 'selected' : ''}>تعليم وتدريب</option>
                                    <option value="خدمات عقارية" ${supplier?.serviceType === 'خدمات عقارية' ? 'selected' : ''}>خدمات عقارية</option>
                                    <option value="خدمات أمنية" ${supplier?.serviceType === 'خدمات أمنية' ? 'selected' : ''}>خدمات أمنية</option>
                                    <option value="خدمات نظافة" ${supplier?.serviceType === 'خدمات نظافة' ? 'selected' : ''}>خدمات نظافة</option>
                                    <option value="أخرى" ${supplier?.serviceType === 'أخرى' ? 'selected' : ''}>أخرى</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- وسائل التواصل -->
                    <div class="form-section">
                        <h4><i class="fas fa-phone"></i> وسائل التواصل</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierPhone" data-ar="رقم الهاتف *" data-en="Phone Number *">رقم الهاتف *</label>
                                <input type="tel" id="supplierPhone" name="phone" required value="${supplier?.phone || ''}" placeholder="011xxxxxxx">
                            </div>
                            <div class="form-group">
                                <label for="supplierEmail" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</label>
                                <input type="email" id="supplierEmail" name="email" value="${supplier?.email || ''}" placeholder="<EMAIL>">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierWhatsapp" data-ar="رقم الواتساب" data-en="WhatsApp Number">رقم الواتساب</label>
                                <input type="tel" id="supplierWhatsapp" name="whatsapp" value="${supplier?.whatsapp || ''}" placeholder="05xxxxxxxx">
                            </div>
                            <div class="form-group">
                                <label for="supplierWebsite" data-ar="الموقع الإلكتروني" data-en="Website">الموقع الإلكتروني</label>
                                <input type="url" id="supplierWebsite" name="website" value="${supplier?.website || ''}" placeholder="www.example.com">
                            </div>
                        </div>
                    </div>

                    <!-- العنوان والموقع -->
                    <div class="form-section">
                        <h4><i class="fas fa-map-marker-alt"></i> العنوان والموقع</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierAddress" data-ar="العنوان التفصيلي *" data-en="Detailed Address *">العنوان التفصيلي *</label>
                                <input type="text" id="supplierAddress" name="address" required value="${supplier?.address || ''}" placeholder="الشارع، الحي، رقم المبنى">
                            </div>
                            <div class="form-group">
                                <label for="supplierCity" data-ar="المدينة *" data-en="City *">المدينة *</label>
                                <select id="supplierCity" name="city" required>
                                    <option value="">اختر المدينة</option>
                                    <option value="الرياض" ${supplier?.city === 'الرياض' ? 'selected' : ''}>الرياض</option>
                                    <option value="جدة" ${supplier?.city === 'جدة' ? 'selected' : ''}>جدة</option>
                                    <option value="الدمام" ${supplier?.city === 'الدمام' ? 'selected' : ''}>الدمام</option>
                                    <option value="مكة المكرمة" ${supplier?.city === 'مكة المكرمة' ? 'selected' : ''}>مكة المكرمة</option>
                                    <option value="المدينة المنورة" ${supplier?.city === 'المدينة المنورة' ? 'selected' : ''}>المدينة المنورة</option>
                                    <option value="الطائف" ${supplier?.city === 'الطائف' ? 'selected' : ''}>الطائف</option>
                                    <option value="تبوك" ${supplier?.city === 'تبوك' ? 'selected' : ''}>تبوك</option>
                                    <option value="أبها" ${supplier?.city === 'أبها' ? 'selected' : ''}>أبها</option>
                                    <option value="حائل" ${supplier?.city === 'حائل' ? 'selected' : ''}>حائل</option>
                                    <option value="الخبر" ${supplier?.city === 'الخبر' ? 'selected' : ''}>الخبر</option>
                                    <option value="القطيف" ${supplier?.city === 'القطيف' ? 'selected' : ''}>القطيف</option>
                                    <option value="الجبيل" ${supplier?.city === 'الجبيل' ? 'selected' : ''}>الجبيل</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- التقييم والحالة -->
                    <div class="form-section">
                        <h4><i class="fas fa-star"></i> التقييم والحالة</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierRating" data-ar="تقييم المورد" data-en="Supplier Rating">تقييم المورد</label>
                                <div class="rating-input">
                                    ${[1,2,3,4,5].map(star => `
                                        <input type="radio" id="star${star}" name="rating" value="${star}" ${supplier?.rating == star ? 'checked' : ''}>
                                        <label for="star${star}" class="star-label">
                                            <i class="fas fa-star"></i>
                                        </label>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="supplierStatus" data-ar="حالة التعاون *" data-en="Cooperation Status *">حالة التعاون *</label>
                                <select id="supplierStatus" name="status" required>
                                    <option value="active" ${supplier?.status === 'active' ? 'selected' : ''}>نشط</option>
                                    <option value="inactive" ${supplier?.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                    <option value="pending" ${supplier?.status === 'pending' ? 'selected' : ''}>قيد المراجعة</option>
                                    <option value="suspended" ${supplier?.status === 'suspended' ? 'selected' : ''}>معلق</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    <div class="form-section">
                        <h4><i class="fas fa-sticky-note"></i> الملاحظات</h4>
                        <div class="form-group">
                            <label for="supplierNotes" data-ar="ملاحظات إضافية" data-en="Additional Notes">ملاحظات إضافية</label>
                            <textarea id="supplierNotes" name="notes" rows="4" placeholder="أدخل أي ملاحظات إضافية حول المورد...">${supplier?.notes || ''}</textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                    <span data-ar="إلغاء" data-en="Cancel">إلغاء</span>
                </button>
                <button type="button" class="btn-primary" onclick="saveSupplier('${supplierId || ''}')">
                    <i class="fas fa-save"></i>
                    <span data-ar="${isEdit ? 'حفظ التغييرات' : 'إضافة المورد'}" data-en="${isEdit ? 'Save Changes' : 'Add Supplier'}">${isEdit ? 'حفظ التغييرات' : 'إضافة المورد'}</span>
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    updateLanguage();

    // التركيز على أول حقل
    setTimeout(() => {
        document.getElementById('supplierName').focus();
    }, 100);
}

// حفظ المورد (إضافة أو تعديل)
function saveSupplier(supplierId = '') {
    const form = document.getElementById('supplierForm');
    const formData = new FormData(form);

    // التحقق من صحة البيانات
    if (!validateSupplierForm(formData)) {
        return;
    }

    const supplierData = {
        name: formData.get('name').trim(),
        serviceType: formData.get('serviceType'),
        phone: formData.get('phone').trim(),
        email: formData.get('email').trim(),
        whatsapp: formData.get('whatsapp').trim(),
        website: formData.get('website').trim(),
        address: formData.get('address').trim(),
        city: formData.get('city'),
        rating: parseInt(formData.get('rating')) || 0,
        status: formData.get('status'),
        notes: formData.get('notes').trim()
    };

    // تحديث contactMethod للتوافق مع النظام الحالي
    const contactMethods = [];
    if (supplierData.phone) contactMethods.push(`هاتف: ${supplierData.phone}`);
    if (supplierData.email) contactMethods.push(`بريد: ${supplierData.email}`);
    if (supplierData.whatsapp) contactMethods.push(`واتساب: ${supplierData.whatsapp}`);
    supplierData.contactMethod = contactMethods.join(' - ');

    const currentUser = getCurrentUser();

    if (supplierId) {
        // تعديل مورد موجود
        const supplierIndex = suppliers.findIndex(s => s.id === supplierId);
        if (supplierIndex !== -1) {
            suppliers[supplierIndex] = {
                ...suppliers[supplierIndex],
                ...supplierData,
                updatedAt: new Date().toISOString(),
                updatedBy: currentUser.username
            };

            logActivity('supplier_edit', `تعديل المورد: ${supplierData.name}`);
            showNotification('تم تحديث بيانات المورد بنجاح', 'success');
        }
    } else {
        // إضافة مورد جديد
        const newSupplier = {
            id: generateId(),
            ...supplierData,
            createdAt: new Date().toISOString(),
            createdBy: currentUser.username
        };

        suppliers.push(newSupplier);
        logActivity('supplier_add', `إضافة مورد جديد: ${supplierData.name}`);
        showNotification('تم إضافة المورد بنجاح', 'success');
    }

    saveSuppliers();
    filteredSuppliers = [...suppliers];
    updateSuppliersDisplay();

    // إغلاق النافذة المنبثقة
    document.querySelector('.modal-overlay').remove();
}

// التحقق من صحة نموذج المورد
function validateSupplierForm(formData) {
    const name = formData.get('name').trim();
    const serviceType = formData.get('serviceType');
    const phone = formData.get('phone').trim();
    const address = formData.get('address').trim();
    const city = formData.get('city');
    const email = formData.get('email').trim();

    // التحقق من الحقول المطلوبة
    if (!name) {
        showNotification('يرجى إدخال اسم المورد', 'error');
        document.getElementById('supplierName').focus();
        return false;
    }

    if (!serviceType) {
        showNotification('يرجى اختيار نوع الخدمة', 'error');
        document.getElementById('supplierServiceType').focus();
        return false;
    }

    if (!phone) {
        showNotification('يرجى إدخال رقم الهاتف', 'error');
        document.getElementById('supplierPhone').focus();
        return false;
    }

    if (!address) {
        showNotification('يرجى إدخال العنوان', 'error');
        document.getElementById('supplierAddress').focus();
        return false;
    }

    if (!city) {
        showNotification('يرجى اختيار المدينة', 'error');
        document.getElementById('supplierCity').focus();
        return false;
    }

    // التحقق من صحة البريد الإلكتروني
    if (email && !validateEmail(email)) {
        showNotification('البريد الإلكتروني غير صحيح', 'error');
        document.getElementById('supplierEmail').focus();
        return false;
    }

    // التحقق من صحة رقم الهاتف
    if (!validatePhone(phone)) {
        showNotification('رقم الهاتف غير صحيح', 'error');
        document.getElementById('supplierPhone').focus();
        return false;
    }

    return true;
}

// عرض تفاصيل المورد
function viewSupplier(supplierId) {
    const supplier = suppliers.find(s => s.id === supplierId);
    if (!supplier) {
        showNotification('المورد غير موجود', 'error');
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content supplier-details-modal">
            <div class="modal-header">
                <h3><i class="fas fa-truck"></i> تفاصيل المورد</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="supplier-details">
                    <!-- رأس المورد -->
                    <div class="supplier-header">
                        <div class="supplier-info">
                            <h2>${supplier.name}</h2>
                            <p class="service-type">${supplier.serviceType}</p>
                            <div class="rating-display">
                                ${[1,2,3,4,5].map(star => `
                                    <i class="fas fa-star ${star <= (supplier.rating || 0) ? 'active' : ''}"></i>
                                `).join('')}
                                <span class="rating-text">(${supplier.rating || 0}/5)</span>
                            </div>
                        </div>
                        <div class="supplier-status">
                            <span class="status-badge status-${supplier.status}">
                                ${getStatusDisplayName(supplier.status)}
                            </span>
                        </div>
                    </div>

                    <!-- معلومات التواصل -->
                    <div class="details-section">
                        <h4><i class="fas fa-phone"></i> معلومات التواصل</h4>
                        <div class="contact-grid">
                            ${supplier.phone ? `
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span>الهاتف:</span>
                                    <a href="tel:${supplier.phone}">${supplier.phone}</a>
                                </div>
                            ` : ''}
                            ${supplier.email ? `
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span>البريد الإلكتروني:</span>
                                    <a href="mailto:${supplier.email}">${supplier.email}</a>
                                </div>
                            ` : ''}
                            ${supplier.whatsapp ? `
                                <div class="contact-item">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>واتساب:</span>
                                    <a href="https://wa.me/${supplier.whatsapp.replace(/[^0-9]/g, '')}" target="_blank">${supplier.whatsapp}</a>
                                </div>
                            ` : ''}
                            ${supplier.website ? `
                                <div class="contact-item">
                                    <i class="fas fa-globe"></i>
                                    <span>الموقع الإلكتروني:</span>
                                    <a href="${supplier.website.startsWith('http') ? supplier.website : 'https://' + supplier.website}" target="_blank">${supplier.website}</a>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- معلومات العنوان -->
                    <div class="details-section">
                        <h4><i class="fas fa-map-marker-alt"></i> العنوان</h4>
                        <div class="address-info">
                            <p><strong>العنوان:</strong> ${supplier.address}</p>
                            <p><strong>المدينة:</strong> ${supplier.city}</p>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    ${supplier.notes ? `
                        <div class="details-section">
                            <h4><i class="fas fa-sticky-note"></i> الملاحظات</h4>
                            <div class="notes-content">
                                <p>${supplier.notes}</p>
                            </div>
                        </div>
                    ` : ''}

                    <!-- معلومات إضافية -->
                    <div class="details-section">
                        <h4><i class="fas fa-info-circle"></i> معلومات إضافية</h4>
                        <div class="additional-info">
                            <p><strong>تاريخ الإضافة:</strong> ${formatDate(supplier.createdAt, true)}</p>
                            <p><strong>أضيف بواسطة:</strong> ${supplier.createdBy}</p>
                            ${supplier.updatedAt ? `
                                <p><strong>آخر تحديث:</strong> ${formatDate(supplier.updatedAt, true)}</p>
                                <p><strong>حدث بواسطة:</strong> ${supplier.updatedBy}</p>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
                ${hasPermission('edit_suppliers') || hasPermission('all') ? `
                    <button type="button" class="btn-primary" onclick="this.closest('.modal-overlay').remove(); editSupplier('${supplier.id}')">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                ` : ''}
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    updateLanguage();
}

// تعديل المورد
function editSupplier(supplierId) {
    if (!hasPermission('edit_suppliers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتعديل الموردين');
        return;
    }

    showSupplierModal(supplierId);
}

// حذف المورد
function deleteSupplier(supplierId) {
    if (!hasPermission('all')) {
        alert('ليس لديك صلاحية لحذف الموردين');
        return;
    }

    const supplier = suppliers.find(s => s.id === supplierId);
    if (!supplier) {
        showNotification('المورد غير موجود', 'error');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف المورد "${supplier.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        suppliers = suppliers.filter(s => s.id !== supplierId);
        saveSuppliers();
        filteredSuppliers = [...suppliers];
        updateSuppliersDisplay();

        logActivity('supplier_delete', `حذف المورد: ${supplier.name}`);
        showNotification('تم حذف المورد بنجاح', 'success');
    }
}

// تصدير الموردين إلى Excel
async function exportSuppliersExcel() {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }

    if (filteredSuppliers.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    // إظهار مؤشر التحميل
    const exportBtn = document.querySelector('.excel-btn');
    const originalContent = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري التصدير...</span>';
    exportBtn.disabled = true;

    try {
        const headers = [
            { key: 'name', label: 'اسم المورد' },
            { key: 'serviceType', label: 'نوع الخدمة' },
            { key: 'phone', label: 'رقم الهاتف' },
            { key: 'email', label: 'البريد الإلكتروني' },
            { key: 'whatsapp', label: 'رقم الواتساب' },
            { key: 'website', label: 'الموقع الإلكتروني' },
            { key: 'address', label: 'العنوان' },
            { key: 'city', label: 'المدينة' },
            { key: 'rating', label: 'التقييم' },
            { key: 'status', label: 'حالة التعاون' },
            { key: 'notes', label: 'الملاحظات' },
            { key: 'createdAt', label: 'تاريخ الإضافة' }
        ];

        // تحضير البيانات مع تنسيق
        const formattedSuppliers = filteredSuppliers.map(supplier => ({
            ...supplier,
            phone: supplier.phone || 'غير محدد',
            email: supplier.email || 'غير محدد',
            whatsapp: supplier.whatsapp || 'غير محدد',
            website: supplier.website || 'غير محدد',
            rating: supplier.rating ? `${supplier.rating}/5` : 'غير مقيم',
            status: getStatusDisplayName(supplier.status),
            notes: supplier.notes || 'لا توجد ملاحظات',
            createdAt: supplier.createdAt ? formatDate(supplier.createdAt, true) : 'غير محدد'
        }));

        const success = await exportToExcel(
            formattedSuppliers,
            'بيانات_الموردين',
            headers,
            {
                sheetName: 'الموردين',
                title: 'تقرير الموردين'
            }
        );

        if (success) {
            showNotification('تم تصدير بيانات الموردين بنجاح', 'success');
        }

    } catch (error) {
        console.error('خطأ في تصدير Excel:', error);
        showNotification('حدث خطأ أثناء تصدير البيانات', 'error');
    } finally {
        // إعادة تعيين الزر
        exportBtn.innerHTML = originalContent;
        exportBtn.disabled = false;
    }
}

// تصدير الموردين إلى PDF
async function exportSuppliersPDF() {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }

    if (filteredSuppliers.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    // إظهار مؤشر التحميل
    const exportBtn = document.querySelector('.pdf-btn');
    const originalContent = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري التصدير...</span>';
    exportBtn.disabled = true;

    try {
        const headers = [
            { key: 'name', label: 'الاسم' },
            { key: 'serviceType', label: 'الخدمة' },
            { key: 'phone', label: 'الهاتف' },
            { key: 'city', label: 'المدينة' },
            { key: 'status', label: 'الحالة' }
        ];

        // تحضير البيانات مع تنسيق مبسط للـ PDF
        const formattedSuppliers = filteredSuppliers.map(supplier => ({
            ...supplier,
            phone: supplier.phone || 'غير محدد',
            status: getStatusDisplayName(supplier.status)
        }));

        const success = await exportToPDF(
            formattedSuppliers,
            'تقرير_الموردين',
            headers,
            {
                title: 'تقرير الموردين'
            }
        );

        if (success) {
            showNotification('تم تصدير تقرير الموردين بنجاح', 'success');
        }

    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        showNotification('حدث خطأ أثناء تصدير التقرير', 'error');
    } finally {
        // إعادة تعيين الزر
        exportBtn.innerHTML = originalContent;
        exportBtn.disabled = false;
    }
}

// عرض الإشعارات (إذا لم تكن موجودة)
function showNotification(message, type = 'info') {
    // التحقق من وجود الدالة في dashboard.js
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }

    // إنشاء إشعار بسيط
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#2563eb'};
    `;

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"
               style="color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#2563eb'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}
