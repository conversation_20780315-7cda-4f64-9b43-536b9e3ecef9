// ===== إدارة الموردين =====

let suppliers = [];
let filteredSuppliers = [];
let currentPage = 1;
let itemsPerPage = 10;
let sortBy = 'name';
let sortOrder = 'asc';

// تهيئة صفحة الموردين
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_suppliers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // تهيئة البيانات
    initializeSuppliers();
    
    // ربط الأحداث
    bindEvents();
    
    // تحديث العرض
    updateSuppliersDisplay();
    
    // تسجيل النشاط
    logActivity('suppliers_view', 'عرض صفحة إدارة الموردين');
});

// تهيئة بيانات الموردين
function initializeSuppliers() {
    // تحميل الموردين من التخزين المحلي
    suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
    
    // إضافة بيانات تجريبية إذا لم توجد بيانات
    if (suppliers.length === 0) {
        suppliers = [
            {
                id: generateId(),
                name: 'شركة التقنية المتقدمة',
                serviceType: 'خدمات تقنية',
                contactMethod: 'هاتف: 0112345678 - بريد: <EMAIL>',
                address: 'شارع الملك فهد، حي العليا',
                city: 'الرياض',
                notes: 'متخصصون في تطوير البرمجيات وأنظمة الإدارة',
                status: 'active',
                createdAt: '2024-01-10',
                createdBy: 'admin'
            },
            {
                id: generateId(),
                name: 'مؤسسة الخدمات اللوجستية',
                serviceType: 'خدمات شحن ونقل',
                contactMethod: 'جوال: 0501234567 - واتساب: 0501234567',
                address: 'المنطقة الصناعية الثانية',
                city: 'جدة',
                notes: 'خدمات شحن محلية ودولية',
                status: 'active',
                createdAt: '2024-01-08',
                createdBy: 'supervisor'
            },
            {
                id: generateId(),
                name: 'شركة التسويق الرقمي',
                serviceType: 'تسويق وإعلان',
                contactMethod: 'بريد: <EMAIL> - هاتف: 0138765432',
                address: 'برج الأعمال، الكورنيش',
                city: 'الدمام',
                notes: 'خدمات تسويق رقمي ووسائل التواصل الاجتماعي',
                status: 'active',
                createdAt: '2024-01-05',
                createdBy: 'admin'
            },
            {
                id: generateId(),
                name: 'مكتب الاستشارات القانونية',
                serviceType: 'استشارات قانونية',
                contactMethod: 'هاتف: 0114567890 - فاكس: 0114567891',
                address: 'حي الملز، شارع التحلية',
                city: 'الرياض',
                notes: 'استشارات قانونية للشركات والأفراد',
                status: 'inactive',
                createdAt: '2024-01-03',
                createdBy: 'supervisor'
            }
        ];
        
        saveSuppliers();
    }
    
    filteredSuppliers = [...suppliers];
}

// حفظ الموردين في التخزين المحلي
function saveSuppliers() {
    localStorage.setItem('suppliers', JSON.stringify(suppliers));
}

// ربط الأحداث
function bindEvents() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
    
    // الفلاتر
    const filters = ['serviceFilter', 'cityFilter', 'statusFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', handleFilter);
        }
    });
    
    // الترتيب
    const sortBy = document.getElementById('sortBy');
    if (sortBy) {
        sortBy.addEventListener('change', handleSort);
    }
}

// معالج البحث
function handleSearch(event) {
    const searchTerm = event.target.value.trim();
    
    if (searchTerm === '') {
        filteredSuppliers = [...suppliers];
    } else {
        filteredSuppliers = searchData(suppliers, searchTerm, [
            'name', 'serviceType', 'contactMethod', 'address', 'city', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateSuppliersDisplay();
}

// معالج الفلاتر
function handleFilter() {
    const serviceFilter = document.getElementById('serviceFilter').value;
    const cityFilter = document.getElementById('cityFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredSuppliers = filterData(suppliers, {
        serviceType: serviceFilter,
        city: cityFilter,
        status: statusFilter
    });
    
    // تطبيق البحث إذا كان موجوداً
    const searchTerm = document.getElementById('searchInput').value.trim();
    if (searchTerm) {
        filteredSuppliers = searchData(filteredSuppliers, searchTerm, [
            'name', 'serviceType', 'contactMethod', 'address', 'city', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateSuppliersDisplay();
}

// معالج الترتيب
function handleSort() {
    sortBy = document.getElementById('sortBy').value;
    filteredSuppliers = sortData(filteredSuppliers, sortBy, sortOrder);
    updateSuppliersDisplay();
}

// تبديل ترتيب الفرز
function toggleSortOrder() {
    sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    
    const sortOrderBtn = document.getElementById('sortOrderBtn');
    if (sortOrderBtn) {
        const icon = sortOrderBtn.querySelector('i');
        icon.className = sortOrder === 'asc' ? 'fas fa-sort-amount-down' : 'fas fa-sort-amount-up';
    }
    
    filteredSuppliers = sortData(filteredSuppliers, sortBy, sortOrder);
    updateSuppliersDisplay();
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('serviceFilter').value = 'all';
    document.getElementById('cityFilter').value = 'all';
    document.getElementById('statusFilter').value = 'all';
    
    filteredSuppliers = [...suppliers];
    currentPage = 1;
    updateSuppliersDisplay();
}

// تحديث عرض الموردين
function updateSuppliersDisplay() {
    updateSuppliersStats();
    updateFiltersOptions();
    updateSuppliersTable();
    updatePagination();
}

// تحديث إحصائيات الموردين
function updateSuppliersStats() {
    const totalSuppliers = suppliers.length;
    const activeSuppliers = suppliers.filter(s => s.status === 'active').length;
    const uniqueServices = [...new Set(suppliers.map(s => s.serviceType))].length;
    const uniqueCities = [...new Set(suppliers.map(s => s.city))].length;
    
    // تحديث العدادات
    updateCounter('totalSuppliersCount', totalSuppliers);
    updateCounter('activeSuppliersCount', activeSuppliers);
    updateCounter('servicesCount', uniqueServices);
    updateCounter('citiesCount', uniqueCities);
    
    // تحديث شارة التنقل
    updateNavBadge('suppliersBadge', totalSuppliers);
}

// تحديث خيارات الفلاتر
function updateFiltersOptions() {
    // تحديث فلتر الخدمات
    const services = [...new Set(suppliers.map(s => s.serviceType))].sort();
    updateFilterOptions('serviceFilter', services);
    
    // تحديث فلتر المدن
    const cities = [...new Set(suppliers.map(s => s.city))].sort();
    updateFilterOptions('cityFilter', cities);
}

// تحديث خيارات فلتر معين
function updateFilterOptions(filterId, options) {
    const filter = document.getElementById(filterId);
    if (!filter) return;
    
    const currentValue = filter.value;
    const firstOption = filter.querySelector('option[value="all"]');
    
    // مسح الخيارات الحالية (عدا الخيار الأول)
    filter.innerHTML = '';
    filter.appendChild(firstOption);
    
    // إضافة الخيارات الجديدة
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option;
        optionElement.textContent = option;
        filter.appendChild(optionElement);
    });
    
    // استعادة القيمة المحددة
    filter.value = currentValue;
}

// تحديث جدول الموردين
function updateSuppliersTable() {
    const tbody = document.getElementById('suppliersTableBody');
    if (!tbody) return;
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageSuppliers = filteredSuppliers.slice(startIndex, endIndex);
    
    if (pageSuppliers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 40px; color: var(--gray);">
                    <i class="fas fa-truck" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p data-ar="لا توجد موردين" data-en="No suppliers found">لا توجد موردين</p>
                </td>
            </tr>
        `;
        updateLanguage();
        return;
    }
    
    tbody.innerHTML = pageSuppliers.map(supplier => `
        <tr>
            <td><strong>${supplier.name}</strong></td>
            <td>${supplier.serviceType}</td>
            <td>${supplier.contactMethod}</td>
            <td>${supplier.address}</td>
            <td>${supplier.city}</td>
            <td>
                <span class="status-badge status-${supplier.status}">
                    ${getStatusDisplayName(supplier.status)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewSupplier('${supplier.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${hasPermission('edit_suppliers') || hasPermission('all') ? `
                        <button class="action-btn edit" onclick="editSupplier('${supplier.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : ''}
                    ${hasPermission('all') ? `
                        <button class="action-btn delete" onclick="deleteSupplier('${supplier.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// الحصول على اسم الحالة للعرض
function getStatusDisplayName(status) {
    const statuses = {
        'active': currentLanguage === 'ar' ? 'نشط' : 'Active',
        'inactive': currentLanguage === 'ar' ? 'غير نشط' : 'Inactive'
    };
    
    return statuses[status] || status;
}

// تحديث ترقيم الصفحات
function updatePagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;
    
    const totalPages = Math.ceil(filteredSuppliers.length / itemsPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = `
        <button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // إضافة أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-dots">...</span>';
        }
    }
    
    paginationHTML += `
        <button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="pagination-info">
            ${currentPage} من ${totalPages} (${filteredSuppliers.length} مورد)
        </div>
    `;
    
    pagination.innerHTML = paginationHTML;
}

// تغيير الصفحة
function changePage(page) {
    const totalPages = Math.ceil(filteredSuppliers.length / itemsPerPage);
    
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    updateSuppliersTable();
    updatePagination();
    
    // التمرير إلى أعلى الجدول
    document.querySelector('.table-section').scrollIntoView({ behavior: 'smooth' });
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تصدير الموردين
function exportSuppliers(format) {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'serviceType', label: 'نوع الخدمة' },
        { key: 'contactMethod', label: 'وسيلة التواصل' },
        { key: 'address', label: 'العنوان' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' },
        { key: 'notes', label: 'الملاحظات' }
    ];
    
    if (format === 'csv') {
        exportToCSV(filteredSuppliers, 'suppliers', headers);
    } else if (format === 'json') {
        exportToJSON(filteredSuppliers, 'suppliers');
    }
    
    logActivity('export_suppliers', `تصدير بيانات الموردين بصيغة ${format.toUpperCase()}`);
}

// طباعة الموردين
function printSuppliers() {
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'serviceType', label: 'نوع الخدمة' },
        { key: 'contactMethod', label: 'وسيلة التواصل' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' }
    ];
    
    printData('قائمة الموردين', filteredSuppliers, headers);
    logActivity('print_suppliers', 'طباعة قائمة الموردين');
}

// استيراد الموردين
function importSuppliers() {
    if (!hasPermission('add_suppliers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لاستيراد البيانات');
        return;
    }
    
    const fileInput = document.getElementById('importFileInput');
    fileInput.click();
}

// معالجة ملف الاستيراد
function handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const fileName = file.name.toLowerCase();
    
    if (fileName.endsWith('.csv')) {
        importFromCSV(file);
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        alert('استيراد ملفات Excel قيد التطوير. يرجى استخدام ملف CSV حالياً.');
    } else {
        alert('نوع الملف غير مدعوم. يرجى اختيار ملف CSV أو Excel.');
    }
    
    // مسح قيمة input
    event.target.value = '';
}

// استيراد من CSV
function importFromCSV(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
            
            const importedSuppliers = [];
            
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;
                
                const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
                
                if (values.length >= 5) {
                    const supplier = {
                        id: generateId(),
                        name: values[0] || '',
                        serviceType: values[1] || '',
                        contactMethod: values[2] || '',
                        address: values[3] || '',
                        city: values[4] || '',
                        status: values[5] || 'active',
                        notes: values[6] || '',
                        createdAt: new Date().toISOString(),
                        createdBy: getCurrentUser().username
                    };
                    
                    importedSuppliers.push(supplier);
                }
            }
            
            if (importedSuppliers.length > 0) {
                suppliers.push(...importedSuppliers);
                saveSuppliers();
                filteredSuppliers = [...suppliers];
                updateSuppliersDisplay();
                
                alert(`تم استيراد ${importedSuppliers.length} مورد بنجاح`);
                logActivity('import_suppliers', `استيراد ${importedSuppliers.length} مورد من ملف CSV`);
            } else {
                alert('لم يتم العثور على بيانات صالحة في الملف');
            }
            
        } catch (error) {
            console.error('خطأ في استيراد الملف:', error);
            alert('حدث خطأ أثناء استيراد الملف. يرجى التأكد من تنسيق الملف.');
        }
    };
    
    reader.readAsText(file, 'UTF-8');
}

// عرض نافذة إضافة مورد جديد
function showAddSupplierModal() {
    if (!hasPermission('add_suppliers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لإضافة موردين جدد');
        return;
    }
    
    // سيتم تنفيذها في الجزء التالي
    alert('نافذة إضافة مورد جديد قيد التطوير');
}

// عرض تفاصيل المورد
function viewSupplier(supplierId) {
    // سيتم تنفيذها في الجزء التالي
    alert('عرض تفاصيل المورد قيد التطوير');
}

// تعديل المورد
function editSupplier(supplierId) {
    if (!hasPermission('edit_suppliers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتعديل الموردين');
        return;
    }
    
    // سيتم تنفيذها في الجزء التالي
    alert('تعديل المورد قيد التطوير');
}

// حذف المورد
function deleteSupplier(supplierId) {
    if (!hasPermission('all')) {
        alert('ليس لديك صلاحية لحذف الموردين');
        return;
    }
    
    // سيتم تنفيذها في الجزء التالي
    alert('حذف المورد قيد التطوير');
}
