// ===== لوحة تحكم التلي سيلز =====

let currentTelesalesData = null;
let callsData = [];

// تحميل بيانات لوحة التحكم
function loadTelesalesDashboard() {
    const currentUser = getCurrentUser();
    if (!currentUser || currentUser.role !== 'telesales') {
        redirectToHomePage();
        return;
    }
    
    // تحديث معلومات المستخدم
    updateUserInfo(currentUser);
    
    // تحميل بيانات موظف التلي سيلز
    loadTelesalesData(currentUser.telesalesId);
    
    // تحميل الإحصائيات
    loadStatistics();
    
    // تحميل العملاء الحديثين
    loadRecentCustomers();
    
    // تحميل عروض الأسعار الحديثة
    loadRecentQuotations();
    
    // تحميل بيانات المكالمات
    loadCallsData();
    
    // تحديث العدادات في الشريط الجانبي
    updateSidebarBadges();
}

// تحديث معلومات المستخدم
function updateUserInfo(user) {
    document.getElementById('currentUserName').textContent = user.name;
    document.getElementById('currentUserRole').textContent = 'موظف تلي سيلز';
    document.getElementById('welcomeMessage').textContent = `مرحباً ${user.name}، إليك ملخص أدائك اليوم`;
}

// تحميل بيانات موظف التلي سيلز
function loadTelesalesData(telesalesId) {
    const telesales = JSON.parse(localStorage.getItem('telesales')) || [];
    currentTelesalesData = telesales.find(t => t.id === telesalesId);
    
    if (!currentTelesalesData) {
        console.error('لم يتم العثور على بيانات موظف التلي سيلز');
        return;
    }
}

// تحميل الإحصائيات
function loadStatistics() {
    const customers = getAccessibleCustomers();
    const quotations = getAccessibleQuotations();
    const calls = getTelesalesCalls();
    
    // إحصائيات العملاء
    document.getElementById('totalCustomers').textContent = customers.length;
    
    // إحصائيات عروض الأسعار
    document.getElementById('totalQuotations').textContent = quotations.length;
    
    // إحصائيات المكالمات
    const totalCalls = calls.length;
    const successfulCalls = calls.filter(c => c.status === 'successful').length;
    const unansweredCalls = calls.filter(c => c.status === 'unanswered').length;
    const quotationsFromCalls = calls.filter(c => c.generatedQuotation).length;
    
    document.getElementById('totalCalls').textContent = totalCalls;
    document.getElementById('successfulCalls').textContent = successfulCalls;
    document.getElementById('unansweredCalls').textContent = unansweredCalls;
    document.getElementById('quotationsFromCalls').textContent = quotationsFromCalls;
    
    // حساب معدل النجاح
    const successRate = totalCalls > 0 ? Math.round((successfulCalls / totalCalls) * 100) : 0;
    document.getElementById('successRate').textContent = successRate + '%';
    
    // حساب معدل التحويل
    const conversionRate = totalCalls > 0 ? Math.round((quotationsFromCalls / totalCalls) * 100) : 0;
    document.getElementById('conversionRate').textContent = conversionRate + '%';
}

// الحصول على عروض الأسعار المسموح بالوصول إليها
function getAccessibleQuotations() {
    const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
    const accessibleCustomers = getAccessibleCustomers();
    const customerIds = accessibleCustomers.map(c => c.id);
    
    return quotations.filter(q => customerIds.includes(q.customerId));
}

// الحصول على مكالمات موظف التلي سيلز
function getTelesalesCalls() {
    const currentUser = getCurrentUser();
    const calls = JSON.parse(localStorage.getItem('calls')) || [];
    
    return calls.filter(call => call.telesalesId === currentUser.telesalesId);
}

// تحميل العملاء الحديثين
function loadRecentCustomers() {
    const customers = getAccessibleCustomers();
    const recentCustomers = customers
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5);
    
    const container = document.getElementById('recentCustomers');
    
    if (recentCustomers.length === 0) {
        container.innerHTML = '<p class="no-data">لا توجد عملاء حديثين</p>';
        return;
    }
    
    container.innerHTML = recentCustomers.map(customer => `
        <div class="customer-card">
            <div class="customer-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="customer-info">
                <h4>${customer.name}</h4>
                <p>${customer.phone}</p>
                <span class="customer-date">${formatDate(customer.createdAt)}</span>
            </div>
            <div class="customer-actions">
                <button class="btn-action" onclick="viewCustomer('${customer.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-action" onclick="callCustomer('${customer.id}')" title="اتصال">
                    <i class="fas fa-phone"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// تحميل عروض الأسعار الحديثة
function loadRecentQuotations() {
    const quotations = getAccessibleQuotations();
    const recentQuotations = quotations
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5);
    
    const container = document.getElementById('recentQuotations');
    
    if (recentQuotations.length === 0) {
        container.innerHTML = '<p class="no-data">لا توجد عروض أسعار حديثة</p>';
        return;
    }
    
    container.innerHTML = recentQuotations.map(quotation => `
        <div class="quotation-card">
            <div class="quotation-info">
                <h4>${quotation.quoteNumber}</h4>
                <p>${quotation.customerName}</p>
                <span class="quotation-amount">${formatCurrencyShort(quotation.totalAmount)}</span>
            </div>
            <div class="quotation-status">
                <span class="status-badge status-${quotation.status}">${getStatusDisplayName(quotation.status)}</span>
            </div>
            <div class="quotation-actions">
                <button class="btn-action" onclick="viewQuotation('${quotation.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-action" onclick="previewQuotation('${quotation.id}')" title="معاينة">
                    <i class="fas fa-file-alt"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// تحميل بيانات المكالمات
function loadCallsData() {
    callsData = getTelesalesCalls();
    
    // تحديث قائمة العملاء في نموذج إضافة المكالمة
    updateCustomersList();
}

// تحديث قائمة العملاء في النموذج
function updateCustomersList() {
    const customers = getAccessibleCustomers();
    const select = document.getElementById('callCustomerId');
    
    select.innerHTML = '<option value="">اختر العميل</option>' +
        customers.map(customer => `
            <option value="${customer.id}">${customer.name} - ${customer.phone}</option>
        `).join('');
}

// تحديث العدادات في الشريط الجانبي
function updateSidebarBadges() {
    const customers = getAccessibleCustomers();
    const quotations = getAccessibleQuotations();
    
    document.getElementById('customersBadge').textContent = customers.length;
    document.getElementById('quotationsBadge').textContent = quotations.length;
}

// إضافة مكالمة جديدة
function addNewCall() {
    const modal = document.getElementById('addCallModal');
    modal.classList.add('show');
    
    // إعادة تعيين النموذج
    document.getElementById('addCallForm').reset();
    
    // تحديث قائمة العملاء
    updateCustomersList();
}

// إغلاق نافذة إضافة المكالمة
function closeAddCallModal() {
    const modal = document.getElementById('addCallModal');
    modal.classList.remove('show');
}

// حفظ المكالمة
function saveCall() {
    const form = document.getElementById('addCallForm');
    const formData = new FormData(form);
    
    const customerId = document.getElementById('callCustomerId').value;
    const callType = document.getElementById('callType').value;
    const callStatus = document.getElementById('callStatus').value;
    const callDuration = parseFloat(document.getElementById('callDuration').value) || 0;
    const callNotes = document.getElementById('callNotes').value.trim();
    const generateQuotation = document.getElementById('generateQuotation').checked;
    
    if (!customerId || !callType || !callStatus) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    const currentUser = getCurrentUser();
    const customers = getAccessibleCustomers();
    const customer = customers.find(c => c.id === customerId);
    
    if (!customer) {
        showNotification('العميل المحدد غير صحيح', 'error');
        return;
    }
    
    const newCall = {
        id: 'call_' + Date.now(),
        customerId: customerId,
        customerName: customer.name,
        telesalesId: currentUser.telesalesId,
        telesalesName: currentUser.name,
        type: callType,
        status: callStatus,
        duration: callDuration,
        notes: callNotes,
        generatedQuotation: generateQuotation,
        createdAt: new Date().toISOString(),
        date: new Date().toLocaleDateString('ar-SA'),
        time: new Date().toLocaleTimeString('ar-SA')
    };
    
    // حفظ المكالمة
    const calls = JSON.parse(localStorage.getItem('calls')) || [];
    calls.unshift(newCall);
    localStorage.setItem('calls', JSON.stringify(calls));
    
    // تحديث الإحصائيات
    loadStatistics();
    
    // إغلاق النافذة
    closeAddCallModal();
    
    showNotification('تم حفظ المكالمة بنجاح', 'success');
    logActivity('call_added', `إضافة مكالمة جديدة للعميل: ${customer.name}`);
}

// عرض العميل
function viewCustomer(customerId) {
    window.location.href = `customers.html?view=${customerId}`;
}

// الاتصال بالعميل
function callCustomer(customerId) {
    const customers = getAccessibleCustomers();
    const customer = customers.find(c => c.id === customerId);
    
    if (customer) {
        // فتح نافذة إضافة المكالمة مع تحديد العميل مسبقاً
        addNewCall();
        setTimeout(() => {
            document.getElementById('callCustomerId').value = customerId;
        }, 100);
    }
}

// عرض عرض السعر
function viewQuotation(quotationId) {
    window.location.href = `quotations.html?view=${quotationId}`;
}

// معاينة عرض السعر
function previewQuotation(quotationId) {
    const quotations = getAccessibleQuotations();
    const quotation = quotations.find(q => q.id === quotationId);
    
    if (quotation) {
        window.open(`quotation-preview.html?id=${quotation.quoteNumber}`, '_blank');
    }
}

// الحصول على اسم حالة العرض
function getStatusDisplayName(status) {
    const statuses = {
        'draft': 'مسودة',
        'sent': 'مرسل',
        'accepted': 'مقبول',
        'rejected': 'مرفوض',
        'expired': 'منتهي الصلاحية'
    };
    return statuses[status] || status;
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// إغلاق النوافذ المنبثقة عند النقر خارجها
document.addEventListener('click', function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.classList.remove('show');
        }
    });
});

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النظام
    initializeSystem();
    
    // التحقق من تسجيل الدخول
    if (!requireAuth()) return;
    
    // التحقق من الصلاحيات
    const currentUser = getCurrentUser();
    if (currentUser.role !== 'telesales') {
        alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
        redirectToHomePage();
        return;
    }
    
    // تحميل لوحة التحكم
    loadTelesalesDashboard();
});
