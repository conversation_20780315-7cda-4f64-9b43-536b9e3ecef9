<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العملاء والموردين</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-users-cog"></i>
                <h1 data-ar="نظام إدارة العملاء والموردين" data-en="Customer & Supplier Management System">نظام إدارة العملاء والموردين</h1>
                <p data-ar="مرحباً بك في نظام الإدارة المتكامل" data-en="Welcome to the integrated management system">مرحباً بك في نظام الإدارة المتكامل</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username" data-ar="اسم المستخدم" data-en="Username">اسم المستخدم</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" data-ar="كلمة المرور" data-en="Password">كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <button type="submit" class="login-btn" data-ar="تسجيل الدخول" data-en="Login">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="login-footer">
                <div class="language-toggle">
                    <button onclick="toggleLanguage()" class="lang-btn">
                        <i class="fas fa-globe"></i>
                        <span id="langText">English</span>
                    </button>
                </div>
                
                <div class="demo-accounts">
                    <h4 data-ar="حسابات تجريبية:" data-en="Demo Accounts:">حسابات تجريبية:</h4>
                    <div class="demo-list">
                        <div class="demo-item">
                            <strong data-ar="مدير:" data-en="Manager:">مدير:</strong> admin / admin123
                        </div>
                        <div class="demo-item">
                            <strong data-ar="مشرف:" data-en="Supervisor:">مشرف:</strong> supervisor / super123
                        </div>
                        <div class="demo-item">
                            <strong data-ar="تلي سيلز:" data-en="Telesales:">تلي سيلز:</strong> telesales / tele123
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Initialize login page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in
            if (localStorage.getItem('currentUser')) {
                window.location.href = 'dashboard.html';
            }
        });
        
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }
    </script>
</body>
</html>
