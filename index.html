<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - شركة نخبة الإعلان للدعاية والإعلان</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/users.js"></script>
    <script src="js/utils.js"></script>

    <script>
        // تهيئة الصفحة الرئيسية
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة النظام
            if (typeof initializeSystem === 'function') {
                initializeSystem();
            }

            // التحقق من تسجيل الدخول
            const currentUser = getCurrentUser();

            if (!currentUser) {
                // إذا لم يكن المستخدم مسجل دخول، توجيه إلى صفحة تسجيل الدخول
                window.location.href = 'login.html';
                return;
            }

            // توجيه المستخدم إلى لوحة التحكم المناسبة حسب دوره
            redirectToHomePage();
        });

        // إعادة التوجيه إلى الصفحة المناسبة حسب دور المستخدم
        function redirectToHomePage() {
            const currentUser = getCurrentUser();

            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }

            // توجيه حسب الدور
            switch (currentUser.role) {
                case 'telesales':
                    window.location.href = 'telesales-dashboard.html';
                    break;
                case 'admin':
                case 'supervisor':
                    window.location.href = 'dashboard.html';
                    break;
                case 'supplier_manager':
                    window.location.href = 'suppliers.html';
                    break;
                default:
                    window.location.href = 'dashboard.html';
                    break;
            }
        }
    </script>
</body>
</html>
