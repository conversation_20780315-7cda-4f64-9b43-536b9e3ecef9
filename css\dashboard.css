/* ===== تصميم لوحة التحكم ===== */

.dashboard-page {
    background: var(--light-gray);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== شريط التنقل العلوي ===== */
.top-navbar {
    background: var(--white);
    box-shadow: 0 2px 4px var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 20px;
    max-width: 100%;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--dark-gray);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: var(--transition);
}

.menu-toggle:hover {
    background: var(--light-blue);
    color: var(--primary-blue);
}

.nav-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin: 0;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.lang-toggle {
    background: var(--light-blue);
    border: none;
    color: var(--primary-blue);
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.lang-toggle:hover {
    background: var(--primary-blue);
    color: var(--white);
}

/* ===== قائمة المستخدم ===== */
.user-menu {
    position: relative;
}

.user-btn {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--dark-gray);
    font-weight: 600;
}

.user-btn:hover {
    background: var(--light-blue);
    color: var(--primary-blue);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 25px var(--shadow);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1001;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: var(--dark-gray);
    text-decoration: none;
    transition: var(--transition);
}

.user-dropdown a:hover {
    background: var(--light-blue);
    color: var(--primary-blue);
}

.user-dropdown hr {
    margin: 8px 0;
    border: none;
    border-top: 1px solid #e2e8f0;
}

.logout-btn {
    color: var(--danger) !important;
}

.logout-btn:hover {
    background: #fee2e2 !important;
    color: var(--danger) !important;
}

/* ===== الشريط الجانبي ===== */
.sidebar {
    position: fixed;
    top: 70px;
    right: 0;
    width: 280px;
    height: calc(100vh - 70px);
    background: var(--white);
    box-shadow: -2px 0 4px var(--shadow);
    z-index: 999;
    transition: var(--transition);
    overflow-y: auto;
}

.sidebar.collapsed {
    transform: translateX(100%);
}

.sidebar-header {
    padding: 25px 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 700;
    color: var(--primary-blue);
    font-size: 1.1rem;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    color: var(--dark-gray);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.nav-item:hover {
    background: var(--light-blue);
    color: var(--primary-blue);
}

.nav-item.active {
    background: var(--light-blue);
    color: var(--primary-blue);
    border-left: 4px solid var(--primary-blue);
}

.nav-item i {
    width: 20px;
    text-align: center;
}

.nav-badge {
    background: var(--primary-blue);
    color: var(--white);
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 12px;
    margin-right: auto;
    font-weight: 600;
}

.nav-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 15px 20px;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    margin-top: 70px;
    margin-right: 280px;
    padding: 30px;
    transition: var(--transition);
}

.main-content.expanded {
    margin-right: 0;
}

.content-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* ===== قسم الترحيب ===== */
.welcome-section {
    margin-bottom: 30px;
}

.welcome-section h2 {
    font-size: 2rem;
    color: var(--dark-gray);
    margin-bottom: 8px;
}

.welcome-section p {
    color: var(--gray);
    font-size: 1.1rem;
}

/* ===== شبكة الإحصائيات ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: 0 2px 4px var(--shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-blue);
}

.stat-card.customers::before { background: var(--primary-blue); }
.stat-card.suppliers::before { background: var(--success); }
.stat-card.telesales::before { background: var(--warning); }
.stat-card.activities::before { background: var(--accent-blue); }

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-card.customers .stat-icon { background: var(--primary-blue); }
.stat-card.suppliers .stat-icon { background: var(--success); }
.stat-card.telesales .stat-icon { background: var(--warning); }
.stat-card.activities .stat-icon { background: var(--accent-blue); }

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 5px;
}

.stat-content p {
    color: var(--gray);
    font-weight: 600;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--success);
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== أقسام لوحة التحكم ===== */
.dashboard-section {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px var(--shadow);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
}

.section-header h3 {
    font-size: 1.3rem;
    color: var(--dark-gray);
    font-weight: 700;
}

.btn-secondary {
    background: var(--light-blue);
    color: var(--primary-blue);
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary:hover {
    background: var(--primary-blue);
    color: var(--white);
}

/* ===== قائمة الأنشطة ===== */
.activities-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: var(--light-gray);
    border-radius: 8px;
    transition: var(--transition);
}

.activity-item:hover {
    background: var(--light-blue);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-blue);
    color: var(--white);
    font-size: 0.9rem;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    color: var(--dark-gray);
    margin-bottom: 5px;
    font-weight: 600;
}

.activity-content p {
    color: var(--gray);
    font-size: 0.9rem;
}

.activity-time {
    color: var(--gray);
    font-size: 0.85rem;
    font-weight: 600;
}

/* ===== الإجراءات السريعة ===== */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    background: var(--white);
    border: 2px solid var(--light-blue);
    color: var(--primary-blue);
    padding: 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    text-align: center;
    font-weight: 600;
}

.action-btn:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow);
}

.action-btn i {
    font-size: 2rem;
}

.action-btn.customers:hover { background: var(--primary-blue); }
.action-btn.suppliers:hover { background: var(--success); }
.action-btn.telesales:hover { background: var(--warning); }
.action-btn.reports:hover { background: var(--accent-blue); }

/* ===== النوافذ المنبثقة ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-gray);
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--gray);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--light-gray);
    color: var(--danger);
}

.modal-body {
    padding: 25px;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.profile-avatar {
    font-size: 4rem;
    color: var(--primary-blue);
}

.profile-details h4 {
    margin: 0 0 10px 0;
    color: var(--dark-gray);
    font-size: 1.3rem;
}

.profile-details .role {
    background: var(--light-blue);
    color: var(--primary-blue);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 10px;
}

.profile-details .email {
    color: var(--gray);
    margin-bottom: 5px;
}

.profile-details .login-time {
    color: var(--gray);
    font-size: 0.9rem;
}

.no-activities {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray);
}

.no-activities i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        transform: translateX(100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
        padding: 20px 15px;
    }

    .nav-container {
        padding: 0 15px;
    }

    .nav-title {
        font-size: 1.2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .profile-info {
        flex-direction: column;
        text-align: center;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }
}

/* ===== أنماط لوحة تحكم التلي سيلز ===== */

/* إحصائيات المكالمات */
.calls-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.call-stat-card {
    background: var(--white);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 15px;
}

.call-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.call-stat-card.successful {
    border-left: 5px solid #10b981;
}

.call-stat-card.unanswered {
    border-left: 5px solid #ef4444;
}

.call-stat-card.quotations-generated {
    border-left: 5px solid #8b5cf6;
}

.call-stat-card.conversion-rate {
    border-left: 5px solid #f59e0b;
}

.call-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.call-stat-card.successful .call-stat-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.call-stat-card.unanswered .call-stat-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.call-stat-card.quotations-generated .call-stat-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.call-stat-card.conversion-rate .call-stat-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.call-stat-content h4 {
    margin: 0 0 5px 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-gray);
}

.call-stat-content p {
    margin: 0;
    color: var(--gray);
    font-size: 0.9rem;
}

/* بطاقات العملاء والعروض الحديثة */
.customer-card,
.quotation-card {
    background: var(--white);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 15px;
}

.customer-card:hover,
.quotation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.customer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
}

.customer-info,
.quotation-info {
    flex: 1;
}

.customer-info h4,
.quotation-info h4 {
    margin: 0 0 5px 0;
    color: var(--dark-gray);
    font-weight: 600;
}

.customer-info p,
.quotation-info p {
    margin: 0 0 5px 0;
    color: var(--gray);
    font-size: 0.9rem;
}

.customer-date {
    font-size: 0.8rem;
    color: var(--light-gray);
}

.quotation-amount {
    font-weight: 700;
    color: var(--primary-blue);
    font-size: 1rem;
}

.quotation-status {
    margin: 0 15px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.status-draft {
    background: #f3f4f6;
    color: #6b7280;
}

.status-badge.status-sent {
    background: #dbeafe;
    color: #1d4ed8;
}

.status-badge.status-accepted {
    background: #d1fae5;
    color: #065f46;
}

.status-badge.status-rejected {
    background: #fee2e2;
    color: #dc2626;
}

.status-badge.status-expired {
    background: #fef3c7;
    color: #d97706;
}

.customer-actions,
.quotation-actions {
    display: flex;
    gap: 8px;
}

.no-data {
    text-align: center;
    color: var(--gray);
    padding: 40px 20px;
    font-style: italic;
}
