/* ===== أنماط صفحة معاينة عرض السعر ===== */

/* إعدادات عامة للصفحة */
.preview-page {
    background: #f8fafc;
    min-height: 100vh;
    font-family: 'Cairo', sans-serif;
}

/* شريط الأدوات العلوي */
.preview-toolbar {
    background: var(--white);
    border-bottom: 1px solid #e2e8f0;
    padding: 15px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.btn-back {
    background: transparent;
    border: 2px solid var(--primary-blue);
    color: var(--primary-blue);
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.btn-back:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.quotation-info h2 {
    margin: 0;
    color: var(--dark-gray);
    font-size: 1.3rem;
    font-weight: 700;
}

.quotation-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    background: #dbeafe;
    color: #1d4ed8;
    margin-top: 5px;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-action {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 0.9rem;
}

.btn-action.edit {
    background: #fef3c7;
    color: #d97706;
}

.btn-action.edit:hover {
    background: #d97706;
    color: var(--white);
}

.btn-action.print {
    background: #e0f2fe;
    color: #0891b2;
}

.btn-action.print:hover {
    background: #0891b2;
    color: var(--white);
}

.btn-action.download {
    background: #ecfdf5;
    color: #059669;
}

.btn-action.download:hover {
    background: #059669;
    color: var(--white);
}

.btn-action.email {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-action.email:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.btn-action.more {
    background: #f1f5f9;
    color: var(--gray);
}

.btn-action.more:hover {
    background: #e2e8f0;
    color: var(--dark-gray);
}

/* قائمة المزيد */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    display: none;
    overflow: hidden;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: var(--dark-gray);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
}

.dropdown-menu a:hover {
    background: #f8fafc;
}

.dropdown-menu a.danger {
    color: #ef4444;
}

.dropdown-menu a.danger:hover {
    background: #fef2f2;
}

.dropdown-menu hr {
    margin: 5px 0;
    border: none;
    border-top: 1px solid #e2e8f0;
}

/* حاوي المعاينة */
.preview-container {
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 20px;
}

/* وثيقة العرض */
.quotation-document {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

/* رأس الوثيقة */
.document-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white);
    padding: 40px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.company-info {
    display: flex;
    gap: 25px;
    align-items: flex-start;
}

.company-logo {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    backdrop-filter: blur(10px);
}

.company-details h1 {
    margin: 0 0 5px 0;
    font-size: 2rem;
    font-weight: 700;
}

.company-tagline {
    margin: 0 0 20px 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.company-contact {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.contact-item i {
    width: 16px;
    text-align: center;
}

.quotation-header-info {
    text-align: left;
}

.document-title {
    margin: 0 0 20px 0;
    font-size: 2.5rem;
    font-weight: 700;
    text-align: right;
}

.quotation-meta {
    display: grid;
    gap: 12px;
}

.meta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    min-width: 280px;
}

.meta-item label {
    font-weight: 600;
    opacity: 0.9;
}

.meta-item span {
    font-weight: 700;
}

/* أقسام المحتوى */
.customer-section,
.items-section,
.calculations-section,
.terms-section {
    padding: 30px 40px;
    border-bottom: 1px solid #f1f5f9;
}

.section-title {
    color: var(--primary-blue);
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0 0 25px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
}

/* قسم العميل */
.customer-details {
    display: flex;
    justify-content: space-between;
    gap: 40px;
}

.customer-info h4 {
    color: var(--dark-gray);
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0 0 15px 0;
}

.customer-contact {
    display: grid;
    gap: 8px;
}

.contact-row {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--gray);
    font-size: 0.95rem;
}

.contact-row i {
    width: 18px;
    color: var(--primary-blue);
}

.quotation-summary {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 25px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    min-width: 300px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e2e8f0;
}

.summary-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-top: 8px;
    border-top: 2px solid var(--primary-blue);
}

.summary-item .label {
    font-weight: 600;
    color: var(--gray);
}

.summary-item .value {
    font-weight: 700;
    color: var(--dark-gray);
}

.summary-item .value.amount {
    color: var(--primary-blue);
    font-size: 1.1rem;
}

.summary-item .value.status {
    background: #d1fae5;
    color: #065f46;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
}

/* جدول البنود */
.items-table-container {
    overflow-x: auto;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.items-table th {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    border-bottom: 2px solid #1e40af;
}

.items-table th.item-description {
    text-align: right;
    min-width: 300px;
}

.items-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: top;
}

.items-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.item-no {
    text-align: center;
    font-weight: 700;
    color: var(--primary-blue);
    width: 60px;
}

.item-description {
    text-align: right;
}

.item-title {
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 5px;
}

.item-details {
    color: var(--gray);
    font-size: 0.9rem;
    line-height: 1.4;
}

.item-quantity,
.item-price,
.item-total {
    text-align: center;
    font-weight: 600;
}

.item-price,
.item-total {
    color: var(--primary-blue);
}

/* قسم الحسابات */
.calculations-container {
    display: flex;
    gap: 40px;
    align-items: flex-start;
}

.calculations-left {
    flex: 1;
}

.notes-section h4 {
    color: var(--dark-gray);
    font-weight: 700;
    margin: 0 0 15px 0;
}

.notes-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.notes-section li {
    padding: 8px 0;
    padding-right: 20px;
    position: relative;
    color: var(--gray);
    line-height: 1.5;
}

.notes-section li::before {
    content: '•';
    color: var(--primary-blue);
    font-weight: bold;
    position: absolute;
    right: 0;
}

.calculations-right {
    min-width: 350px;
}

.totals-table {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e2e8f0;
}

.total-row:last-child {
    border-bottom: none;
}

.total-row.grand-total {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    font-weight: 700;
    font-size: 1.1rem;
}

.total-row .label {
    font-weight: 600;
}

.total-row .value {
    font-weight: 700;
    font-size: 1.05rem;
}

.total-row.discount .value {
    color: #ef4444;
}

.total-row.tax .value {
    color: #f59e0b;
}

/* قسم الشروط والأحكام */
.terms-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.terms-column h4 {
    color: var(--primary-blue);
    font-weight: 700;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
}

.terms-column ul {
    list-style: none;
    padding: 0;
    margin: 0 0 25px 0;
}

.terms-column li {
    padding: 6px 0;
    padding-right: 20px;
    position: relative;
    color: var(--gray);
    line-height: 1.5;
}

.terms-column li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    position: absolute;
    right: 0;
}

/* تذييل الوثيقة */
.document-footer {
    padding: 40px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-top: 3px solid var(--primary-blue);
}

.signature-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    gap: 40px;
}

.signature-box {
    text-align: center;
    flex: 1;
}

.signature-line {
    width: 200px;
    height: 2px;
    background: var(--primary-blue);
    margin: 0 auto 15px auto;
}

.signature-label {
    font-weight: 600;
    color: var(--gray);
    margin-bottom: 8px;
}

.signature-name {
    font-weight: 700;
    color: var(--dark-gray);
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.signature-title {
    color: var(--gray);
    font-size: 0.9rem;
}

.footer-info {
    text-align: center;
    border-top: 1px solid #e2e8f0;
    padding-top: 20px;
}

.footer-info p {
    color: var(--gray);
    margin: 0 0 10px 0;
    font-size: 1.05rem;
}

.footer-contact {
    color: var(--gray);
    font-size: 0.9rem;
}

.footer-contact span {
    margin: 0 10px;
}

/* نافذة البريد الإلكتروني */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--white);
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-gray);
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background: #f1f5f9;
    color: var(--dark-gray);
}

.modal-body {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-gray);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: var(--transition);
    font-family: 'Cairo', sans-serif;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn-secondary {
    background: #f1f5f9;
    color: var(--gray);
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.btn-secondary:hover {
    background: #e2e8f0;
    color: var(--dark-gray);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

/* تصميم متجاوب */
@media (max-width: 1200px) {
    .preview-container {
        max-width: 100%;
        padding: 0 15px;
    }

    .document-header {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }

    .company-contact {
        grid-template-columns: 1fr;
    }

    .quotation-header-info {
        text-align: center;
    }

    .customer-details {
        flex-direction: column;
        gap: 25px;
    }

    .quotation-summary {
        min-width: auto;
    }

    .calculations-container {
        flex-direction: column;
        gap: 25px;
    }

    .terms-content {
        grid-template-columns: 1fr;
        gap: 25px;
    }
}

@media (max-width: 768px) {
    .preview-toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
        padding: 15px 20px;
    }

    .toolbar-left {
        justify-content: center;
    }

    .toolbar-right {
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-action {
        flex: 1;
        justify-content: center;
        min-width: 120px;
    }

    .quotation-document {
        margin: 15px -10px;
        border-radius: 0;
    }

    .document-header {
        padding: 25px 20px;
    }

    .company-info {
        flex-direction: column;
        align-items: center;
        gap: 15px;
        text-align: center;
    }

    .company-logo {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .company-details h1 {
        font-size: 1.5rem;
    }

    .document-title {
        font-size: 2rem;
    }

    .meta-item {
        min-width: auto;
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .customer-section,
    .items-section,
    .calculations-section,
    .terms-section {
        padding: 20px;
    }

    .section-title {
        font-size: 1.2rem;
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .items-table {
        font-size: 0.85rem;
    }

    .items-table th,
    .items-table td {
        padding: 10px 8px;
    }

    .item-description {
        min-width: 200px;
    }

    .item-title {
        font-size: 0.9rem;
    }

    .item-details {
        font-size: 0.8rem;
    }

    .totals-table {
        min-width: auto;
    }

    .total-row {
        padding: 12px 15px;
        font-size: 0.9rem;
    }

    .signature-section {
        flex-direction: column;
        gap: 25px;
    }

    .signature-line {
        width: 150px;
    }

    .document-footer {
        padding: 25px 20px;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .btn-secondary,
    .btn-primary {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .preview-toolbar {
        padding: 10px 15px;
    }

    .toolbar-right {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .btn-action {
        padding: 8px 12px;
        font-size: 0.8rem;
        min-width: auto;
    }

    .document-header {
        padding: 20px 15px;
    }

    .company-details h1 {
        font-size: 1.3rem;
    }

    .company-tagline {
        font-size: 1rem;
    }

    .document-title {
        font-size: 1.8rem;
    }

    .customer-section,
    .items-section,
    .calculations-section,
    .terms-section {
        padding: 15px;
    }

    .section-title {
        font-size: 1.1rem;
    }

    .items-table-container {
        overflow-x: scroll;
    }

    .items-table {
        min-width: 600px;
        font-size: 0.8rem;
    }

    .items-table th,
    .items-table td {
        padding: 8px 6px;
    }

    .item-description {
        min-width: 180px;
    }

    .quotation-summary {
        padding: 20px;
    }

    .summary-item {
        font-size: 0.9rem;
    }

    .totals-table {
        font-size: 0.9rem;
    }

    .total-row {
        padding: 10px 12px;
    }

    .notes-section li {
        font-size: 0.9rem;
        padding: 6px 0;
    }

    .terms-column li {
        font-size: 0.9rem;
        padding: 5px 0;
    }

    .document-footer {
        padding: 20px 15px;
    }

    .footer-contact {
        font-size: 0.8rem;
    }

    .footer-contact span {
        margin: 0 5px;
        display: block;
        margin-bottom: 5px;
    }
}

/* أنماط الطباعة */
@media print {
    .preview-toolbar {
        display: none;
    }

    .preview-container {
        margin: 0;
        padding: 0;
        max-width: none;
    }

    .quotation-document {
        box-shadow: none;
        border-radius: 0;
        margin: 0;
    }

    .document-header {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .btn-action,
    .dropdown-menu {
        display: none;
    }

    .modal {
        display: none !important;
    }

    body {
        background: white !important;
    }

    .section-title {
        color: #2563eb !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .items-table th {
        background: #2563eb !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .total-row.grand-total {
        background: #2563eb !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .document-footer {
        background: #f8fafc !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .page-break {
        page-break-before: always;
    }
}
