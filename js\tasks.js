// ===== إدارة المهام والمتابعة =====

let tasks = [];
let filteredTasks = [];
let currentView = 'list';
let currentPage = 1;
let itemsPerPage = 25;
let sortBy = 'due_date';
let sortOrder = 'asc';

// تهيئة صفحة المهام
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_tasks') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // تهيئة البيانات
    initializeTasksData();
    
    // تحميل المهام
    loadTasks();
    
    // تهيئة الفلاتر
    initializeFilters();
    
    // ربط الأحداث
    bindTaskEvents();
    
    // تسجيل النشاط
    logActivity('tasks_view', 'عرض صفحة إدارة المهام');
});

// تهيئة بيانات المهام
function initializeTasksData() {
    // تحميل المهام من التخزين المحلي أو إنشاء بيانات تجريبية
    const savedTasks = localStorage.getItem('tasks');
    
    if (savedTasks) {
        tasks = JSON.parse(savedTasks);
    } else {
        // إنشاء بيانات تجريبية
        tasks = generateSampleTasks();
        localStorage.setItem('tasks', JSON.stringify(tasks));
    }
    
    filteredTasks = [...tasks];
}

// إنشاء بيانات تجريبية للمهام
function generateSampleTasks() {
    const sampleTasks = [
        {
            id: generateId(),
            title: 'متابعة عرض سعر شركة الأمل',
            description: 'متابعة عرض السعر المرسل لشركة الأمل للخدمات اللوجستية',
            assignee: 'أحمد محمد',
            assigneeRole: 'مندوب مبيعات',
            priority: 'high',
            status: 'pending',
            dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
            createdDate: new Date().toISOString(),
            progress: 25,
            customerId: 'customer_1',
            customerName: 'شركة الأمل للخدمات اللوجستية',
            tags: ['متابعة', 'عرض سعر'],
            notes: 'تم إرسال العرض منذ 3 أيام، يحتاج متابعة'
        },
        {
            id: generateId(),
            title: 'إعداد تقرير المبيعات الشهري',
            description: 'إعداد تقرير شامل لمبيعات شهر ديسمبر',
            assignee: 'فاطمة أحمد',
            assigneeRole: 'محلل بيانات',
            priority: 'medium',
            status: 'in_progress',
            dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
            createdDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            progress: 60,
            tags: ['تقرير', 'مبيعات'],
            notes: 'تم جمع البيانات، جاري التحليل'
        },
        {
            id: generateId(),
            title: 'اتصال بعميل جديد - مؤسسة النور',
            description: 'اتصال أولي بمؤسسة النور لتقديم خدماتنا',
            assignee: 'خالد سعد',
            assigneeRole: 'أخصائي تلي سيلز',
            priority: 'high',
            status: 'pending',
            dueDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
            createdDate: new Date().toISOString(),
            progress: 0,
            customerId: 'customer_2',
            customerName: 'مؤسسة النور التجارية',
            tags: ['اتصال', 'عميل جديد'],
            notes: 'عميل محتمل من خلال الموقع الإلكتروني'
        },
        {
            id: generateId(),
            title: 'تحديث قاعدة بيانات العملاء',
            description: 'تحديث معلومات الاتصال لجميع العملاء',
            assignee: 'سارة علي',
            assigneeRole: 'منسق بيانات',
            priority: 'low',
            status: 'completed',
            dueDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            createdDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            progress: 100,
            tags: ['قاعدة بيانات', 'تحديث'],
            notes: 'تم تحديث 150 سجل عميل'
        },
        {
            id: generateId(),
            title: 'مراجعة عقد شركة التقنية المتقدمة',
            description: 'مراجعة قانونية لعقد الخدمات مع شركة التقنية المتقدمة',
            assignee: 'محمد حسن',
            assigneeRole: 'مستشار قانوني',
            priority: 'medium',
            status: 'in_progress',
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            createdDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            progress: 40,
            customerId: 'customer_3',
            customerName: 'شركة التقنية المتقدمة',
            tags: ['عقد', 'مراجعة قانونية'],
            notes: 'تحتاج مراجعة البنود المالية'
        }
    ];
    
    return sampleTasks;
}

// تحميل المهام
function loadTasks() {
    updateTasksStats();
    updateTasksDisplay();
    updateNavBadges();
}

// تحديث إحصائيات المهام
function updateTasksStats() {
    const totalTasks = tasks.length;
    const pendingTasks = tasks.filter(task => task.status === 'pending').length;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    const overdueTasks = tasks.filter(task => isTaskOverdue(task)).length;
    
    // تحديث العدادات
    updateElement('totalTasksCount', totalTasks);
    updateElement('pendingTasksCount', pendingTasks);
    updateElement('completedTasksCount', completedTasks);
    updateElement('overdueTasksCount', overdueTasks);
    
    // تحديث شرائط التقدم
    const totalProgress = totalTasks > 0 ? (completedTasks / totalTasks * 100) : 0;
    const pendingProgress = totalTasks > 0 ? (pendingTasks / totalTasks * 100) : 0;
    const completedProgress = totalTasks > 0 ? (completedTasks / totalTasks * 100) : 0;
    const overdueProgress = totalTasks > 0 ? (overdueTasks / totalTasks * 100) : 0;
    
    updateProgressBar('totalTasksProgress', totalProgress);
    updateProgressBar('pendingTasksProgress', pendingProgress);
    updateProgressBar('completedTasksProgress', completedProgress);
    updateProgressBar('overdueTasksProgress', overdueProgress);
}

// تحديث عرض المهام
function updateTasksDisplay() {
    if (currentView === 'list') {
        updateTasksTable();
    } else if (currentView === 'kanban') {
        updateKanbanView();
    } else if (currentView === 'calendar') {
        updateCalendarView();
    }
}

// تحديث جدول المهام
function updateTasksTable() {
    const tbody = document.getElementById('tasksTableBody');
    const emptyState = document.getElementById('emptyTasksState');
    
    if (!tbody) return;
    
    // إخفاء حالة الفراغ
    if (emptyState) emptyState.style.display = 'none';
    
    if (filteredTasks.length === 0) {
        tbody.innerHTML = '';
        if (emptyState) emptyState.style.display = 'block';
        return;
    }
    
    // حساب الصفحات
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredTasks.length);
    const pageTasks = filteredTasks.slice(startIndex, endIndex);
    
    tbody.innerHTML = pageTasks.map(task => `
        <tr>
            <td class="checkbox-column">
                <input type="checkbox" class="task-checkbox" value="${task.id}" onchange="updateSelectedTasks()">
            </td>
            <td>
                <div class="task-title-cell">
                    <div class="task-title">${task.title}</div>
                    <div class="task-description">${task.description}</div>
                </div>
            </td>
            <td>
                <div class="assignee-cell">
                    <div class="assignee-avatar">
                        ${task.assignee.charAt(0)}
                    </div>
                    <div class="assignee-info">
                        <div class="assignee-name">${task.assignee}</div>
                        <div class="assignee-role">${task.assigneeRole}</div>
                    </div>
                </div>
            </td>
            <td>
                <span class="priority-badge priority-${task.priority}">
                    <i class="fas fa-${getPriorityIcon(task.priority)}"></i>
                    ${getPriorityDisplayName(task.priority)}
                </span>
            </td>
            <td>
                <span class="task-status-badge status-${task.status}">
                    <i class="fas fa-${getStatusIcon(task.status)}"></i>
                    ${getStatusDisplayName(task.status)}
                </span>
            </td>
            <td>
                <div class="due-date-cell">
                    <div class="due-date ${getDueDateClass(task.dueDate)}">${formatDate(task.dueDate)}</div>
                    <div class="due-time-remaining">${getTimeRemaining(task.dueDate)}</div>
                </div>
            </td>
            <td>
                <div class="task-progress-cell">
                    <div class="task-progress-bar">
                        <div class="task-progress-fill progress-${getProgressClass(task.progress)}" style="width: ${task.progress}%"></div>
                    </div>
                    <div class="task-progress-text">${task.progress}%</div>
                </div>
            </td>
            <td class="actions-column">
                <div class="task-actions">
                    <button class="task-action-btn view" onclick="viewTask('${task.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${hasPermission('edit_tasks') || hasPermission('all') ? `
                        <button class="task-action-btn edit" onclick="editTask('${task.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : ''}
                    ${task.status !== 'completed' ? `
                        <button class="task-action-btn complete" onclick="markTaskComplete('${task.id}')" title="تمييز كمكتملة">
                            <i class="fas fa-check"></i>
                        </button>
                    ` : ''}
                    ${hasPermission('delete_tasks') || hasPermission('all') ? `
                        <button class="task-action-btn delete" onclick="deleteTask('${task.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
    
    // تحديث معلومات الصفحة
    updatePaginationInfo();
    updatePagination();
}

// تهيئة الفلاتر
function initializeFilters() {
    // ملء قائمة المسؤولين
    const assigneeFilter = document.getElementById('assigneeFilter');
    if (assigneeFilter) {
        const assignees = [...new Set(tasks.map(task => task.assignee))];
        assigneeFilter.innerHTML = '<option value="">جميع المسؤولين</option>' +
            assignees.map(assignee => `<option value="${assignee}">${assignee}</option>`).join('');
    }
}

// البحث في المهام
function searchTasks() {
    const searchInput = document.getElementById('taskSearch');
    const clearBtn = document.querySelector('.search-clear-btn');
    const searchTerm = searchInput.value.trim().toLowerCase();
    
    // إظهار/إخفاء زر المسح
    if (clearBtn) {
        clearBtn.style.display = searchTerm ? 'block' : 'none';
    }
    
    applyFilters();
}

// مسح البحث
function clearTaskSearch() {
    const searchInput = document.getElementById('taskSearch');
    const clearBtn = document.querySelector('.search-clear-btn');
    
    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }
    
    if (clearBtn) {
        clearBtn.style.display = 'none';
    }
    
    applyFilters();
}

// تطبيق الفلاتر
function applyFilters() {
    const searchTerm = document.getElementById('taskSearch').value.trim().toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    const assigneeFilter = document.getElementById('assigneeFilter').value;
    const dueDateFilter = document.getElementById('dueDateFilter').value;
    
    filteredTasks = tasks.filter(task => {
        // فلتر البحث
        const matchesSearch = !searchTerm || 
            task.title.toLowerCase().includes(searchTerm) ||
            task.description.toLowerCase().includes(searchTerm) ||
            task.assignee.toLowerCase().includes(searchTerm) ||
            (task.customerName && task.customerName.toLowerCase().includes(searchTerm));
        
        // فلتر الحالة
        const matchesStatus = !statusFilter || task.status === statusFilter;
        
        // فلتر الأولوية
        const matchesPriority = !priorityFilter || task.priority === priorityFilter;
        
        // فلتر المسؤول
        const matchesAssignee = !assigneeFilter || task.assignee === assigneeFilter;
        
        // فلتر تاريخ الاستحقاق
        const matchesDueDate = !dueDateFilter || checkDueDateFilter(task, dueDateFilter);
        
        return matchesSearch && matchesStatus && matchesPriority && matchesAssignee && matchesDueDate;
    });
    
    // إعادة تعيين الصفحة الحالية
    currentPage = 1;
    
    // تحديث العرض
    updateTasksDisplay();
}

// فلترة المهام
function filterTasks() {
    applyFilters();
}

// تبديل العرض
function switchView(view) {
    currentView = view;
    
    // تحديث أزرار العرض
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    // إظهار/إخفاء العروض
    document.querySelectorAll('.tasks-view-container > div').forEach(viewDiv => {
        viewDiv.classList.remove('active');
    });
    
    const targetView = document.getElementById(view + 'View');
    if (targetView) {
        targetView.classList.add('active');
    }
    
    // تحديث العرض
    updateTasksDisplay();
    
    showNotification(`تم التبديل إلى عرض ${getViewDisplayName(view)}`, 'info');
}

// دوال مساعدة
function isTaskOverdue(task) {
    return new Date(task.dueDate) < new Date() && task.status !== 'completed';
}

function checkDueDateFilter(task, filter) {
    const now = new Date();
    const dueDate = new Date(task.dueDate);
    
    switch (filter) {
        case 'today':
            return dueDate.toDateString() === now.toDateString();
        case 'tomorrow':
            const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
            return dueDate.toDateString() === tomorrow.toDateString();
        case 'this_week':
            const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
            const weekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 6));
            return dueDate >= weekStart && dueDate <= weekEnd;
        case 'next_week':
            const nextWeekStart = new Date(now.setDate(now.getDate() - now.getDay() + 7));
            const nextWeekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 13));
            return dueDate >= nextWeekStart && dueDate <= nextWeekEnd;
        case 'overdue':
            return isTaskOverdue(task);
        default:
            return true;
    }
}

function getDueDateClass(dueDate) {
    const now = new Date();
    const due = new Date(dueDate);
    
    if (due < now) return 'overdue';
    if (due.toDateString() === now.toDateString()) return 'today';
    if (due.getTime() - now.getTime() <= 7 * 24 * 60 * 60 * 1000) return 'upcoming';
    return '';
}

function getTimeRemaining(dueDate) {
    const now = new Date();
    const due = new Date(dueDate);
    const diff = due.getTime() - now.getTime();
    
    if (diff < 0) {
        const days = Math.floor(Math.abs(diff) / (24 * 60 * 60 * 1000));
        return `متأخر ${days} يوم`;
    }
    
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    if (days === 0) return 'اليوم';
    if (days === 1) return 'غداً';
    return `خلال ${days} أيام`;
}

function getProgressClass(progress) {
    if (progress === 0) return '0';
    if (progress <= 25) return '25';
    if (progress <= 50) return '50';
    if (progress <= 75) return '75';
    return '100';
}

function getPriorityIcon(priority) {
    const icons = {
        'high': 'exclamation-triangle',
        'medium': 'minus-circle',
        'low': 'arrow-down'
    };
    return icons[priority] || 'minus-circle';
}

function getPriorityDisplayName(priority) {
    const names = {
        'high': 'عالية',
        'medium': 'متوسطة',
        'low': 'منخفضة'
    };
    return names[priority] || priority;
}

function getStatusIcon(status) {
    const icons = {
        'pending': 'clock',
        'in_progress': 'spinner',
        'completed': 'check-circle',
        'cancelled': 'times-circle'
    };
    return icons[status] || 'clock';
}

function getStatusDisplayName(status) {
    const names = {
        'pending': 'معلقة',
        'in_progress': 'قيد التنفيذ',
        'completed': 'مكتملة',
        'cancelled': 'ملغية'
    };
    return names[status] || status;
}

function getViewDisplayName(view) {
    const names = {
        'list': 'القائمة',
        'kanban': 'كانبان',
        'calendar': 'التقويم'
    };
    return names[view] || view;
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

function updateProgressBar(id, percentage) {
    const element = document.getElementById(id);
    if (element) {
        element.style.width = percentage + '%';
    }
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-SA');
}

function generateId() {
    return 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// ربط الأحداث
function bindTaskEvents() {
    // فلاتر المهام
    const statusFilter = document.getElementById('statusFilter');
    const priorityFilter = document.getElementById('priorityFilter');
    const assigneeFilter = document.getElementById('assigneeFilter');
    const dueDateFilter = document.getElementById('dueDateFilter');

    if (statusFilter) statusFilter.addEventListener('change', filterTasks);
    if (priorityFilter) priorityFilter.addEventListener('change', filterTasks);
    if (assigneeFilter) assigneeFilter.addEventListener('change', filterTasks);
    if (dueDateFilter) dueDateFilter.addEventListener('change', filterTasks);
}

// دوال إدارة المهام
function showAddTaskModal() {
    showNotification('نافذة إضافة مهمة جديدة قيد التطوير', 'info');
}

function viewTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        showNotification(`عرض تفاصيل المهمة: ${task.title}`, 'info');
        // يمكن إضافة نافذة منبثقة لعرض التفاصيل
    }
}

function editTask(taskId) {
    if (!hasPermission('edit_tasks') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتعديل المهام');
        return;
    }

    const task = tasks.find(t => t.id === taskId);
    if (task) {
        showNotification(`تعديل المهمة: ${task.title}`, 'info');
        // يمكن إضافة نافذة تعديل المهمة
    }
}

function markTaskComplete(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.status = 'completed';
        task.progress = 100;

        // حفظ التغييرات
        localStorage.setItem('tasks', JSON.stringify(tasks));

        // تحديث العرض
        loadTasks();

        // تسجيل النشاط
        logActivity('task_completed', `تم إكمال المهمة: ${task.title}`);

        showNotification(`تم تمييز المهمة "${task.title}" كمكتملة`, 'success');
    }
}

function deleteTask(taskId) {
    if (!hasPermission('delete_tasks') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لحذف المهام');
        return;
    }

    const task = tasks.find(t => t.id === taskId);
    if (task && confirm(`هل أنت متأكد من حذف المهمة "${task.title}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // حذف المهمة
        tasks = tasks.filter(t => t.id !== taskId);
        filteredTasks = filteredTasks.filter(t => t.id !== taskId);

        // حفظ التغييرات
        localStorage.setItem('tasks', JSON.stringify(tasks));

        // تحديث العرض
        loadTasks();

        // تسجيل النشاط
        logActivity('task_deleted', `تم حذف المهمة: ${task.title}`);

        showNotification(`تم حذف المهمة "${task.title}" بنجاح`, 'success');
    }
}

// دوال التحديد المتعدد
function toggleSelectAllTasks() {
    const selectAllCheckbox = document.getElementById('selectAllTasks');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');

    taskCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedTasks();
}

function updateSelectedTasks() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedTasksCount');

    if (selectedCheckboxes.length > 0) {
        if (bulkActions) bulkActions.style.display = 'flex';
        if (selectedCount) selectedCount.textContent = selectedCheckboxes.length;
    } else {
        if (bulkActions) bulkActions.style.display = 'none';
    }
}

function bulkMarkComplete() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showNotification('يرجى تحديد مهام للتمييز كمكتملة', 'warning');
        return;
    }

    let completedCount = 0;
    selectedCheckboxes.forEach(checkbox => {
        const taskId = checkbox.value;
        const task = tasks.find(t => t.id === taskId);
        if (task && task.status !== 'completed') {
            task.status = 'completed';
            task.progress = 100;
            completedCount++;
        }
    });

    if (completedCount > 0) {
        // حفظ التغييرات
        localStorage.setItem('tasks', JSON.stringify(tasks));

        // تحديث العرض
        loadTasks();

        // إلغاء التحديد
        document.querySelectorAll('.task-checkbox').forEach(cb => cb.checked = false);
        updateSelectedTasks();

        // تسجيل النشاط
        logActivity('bulk_tasks_completed', `تم إكمال ${completedCount} مهمة`);

        showNotification(`تم تمييز ${completedCount} مهمة كمكتملة`, 'success');
    }
}

function bulkDelete() {
    if (!hasPermission('delete_tasks') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لحذف المهام');
        return;
    }

    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showNotification('يرجى تحديد مهام للحذف', 'warning');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${selectedCheckboxes.length} مهمة؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        const deletedTasks = [];
        selectedCheckboxes.forEach(checkbox => {
            const taskId = checkbox.value;
            const task = tasks.find(t => t.id === taskId);
            if (task) {
                deletedTasks.push(task.title);
            }
        });

        // حذف المهام
        const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);
        tasks = tasks.filter(t => !selectedIds.includes(t.id));
        filteredTasks = filteredTasks.filter(t => !selectedIds.includes(t.id));

        // حفظ التغييرات
        localStorage.setItem('tasks', JSON.stringify(tasks));

        // تحديث العرض
        loadTasks();

        // تسجيل النشاط
        logActivity('bulk_tasks_deleted', `تم حذف ${deletedTasks.length} مهمة`);

        showNotification(`تم حذف ${deletedTasks.length} مهمة بنجاح`, 'success');
    }
}

// دوال أخرى
function refreshTasks() {
    const refreshBtn = document.querySelector('.btn-table-action i.fa-sync-alt');
    if (refreshBtn) {
        refreshBtn.classList.add('fa-spin');
    }

    // محاكاة التحديث
    setTimeout(() => {
        loadTasks();
        if (refreshBtn) {
            refreshBtn.classList.remove('fa-spin');
        }
        showNotification('تم تحديث المهام', 'success');
    }, 1000);
}

function toggleTableView() {
    const table = document.getElementById('tasksTable');
    const isCompact = table.classList.contains('compact-view');

    if (isCompact) {
        table.classList.remove('compact-view');
        showNotification('تم التبديل إلى العرض العادي', 'info');
    } else {
        table.classList.add('compact-view');
        showNotification('تم التبديل إلى العرض المضغوط', 'info');
    }
}

function showTableSettings() {
    showNotification('إعدادات الجدول قيد التطوير', 'info');
}

function changeItemsPerPage() {
    itemsPerPage = parseInt(document.getElementById('itemsPerPage').value);
    currentPage = 1;
    updateTasksDisplay();
    showNotification(`تم تغيير العرض إلى ${itemsPerPage} عنصر في الصفحة`, 'info');
}

function sortTasks() {
    const sortValue = document.getElementById('sortTasks').value;
    sortBy = sortValue;

    // ترتيب المهام
    filteredTasks.sort((a, b) => {
        let aValue, bValue;

        switch (sortBy) {
            case 'due_date':
                aValue = new Date(a.dueDate);
                bValue = new Date(b.dueDate);
                break;
            case 'priority':
                const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
                aValue = priorityOrder[a.priority];
                bValue = priorityOrder[b.priority];
                break;
            case 'created_date':
                aValue = new Date(a.createdDate);
                bValue = new Date(b.createdDate);
                break;
            case 'status':
                const statusOrder = { 'pending': 1, 'in_progress': 2, 'completed': 3, 'cancelled': 4 };
                aValue = statusOrder[a.status];
                bValue = statusOrder[b.status];
                break;
            case 'assignee':
                aValue = a.assignee.toLowerCase();
                bValue = b.assignee.toLowerCase();
                break;
            default:
                aValue = a.title.toLowerCase();
                bValue = b.title.toLowerCase();
        }

        if (sortOrder === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });

    updateTasksDisplay();
}

function sortTableBy(column) {
    if (sortBy === column) {
        sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
        sortBy = column;
        sortOrder = 'asc';
    }

    // تحديث أيقونات الترتيب
    document.querySelectorAll('.sort-icon').forEach(icon => {
        icon.className = 'fas fa-sort sort-icon';
    });

    const currentHeader = document.querySelector(`th[onclick="sortTableBy('${column}')"]`);
    if (currentHeader) {
        const icon = currentHeader.querySelector('.sort-icon');
        if (icon) {
            icon.className = `fas fa-sort-${sortOrder === 'asc' ? 'up' : 'down'} sort-icon`;
        }
        currentHeader.classList.add('sorted');
    }

    sortTasks();
}

function updatePaginationInfo() {
    const startIndex = (currentPage - 1) * itemsPerPage + 1;
    const endIndex = Math.min(currentPage * itemsPerPage, filteredTasks.length);

    updateElement('currentPageStart', startIndex);
    updateElement('currentPageEnd', endIndex);
    updateElement('totalTasksCount', filteredTasks.length);
}

function updatePagination() {
    const totalPages = Math.ceil(filteredTasks.length / itemsPerPage);
    const pagination = document.getElementById('tasksPagination');

    if (!pagination) return;

    let paginationHTML = '';

    // زر السابق
    paginationHTML += `
        <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
            <i class="fas fa-chevron-right"></i>
        </button>
    `;

    // أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 2 || i === currentPage + 2) {
            paginationHTML += '<span class="pagination-dots">...</span>';
        }
    }

    // زر التالي
    paginationHTML += `
        <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
            <i class="fas fa-chevron-left"></i>
        </button>
    `;

    pagination.innerHTML = paginationHTML;
}

function changePage(page) {
    const totalPages = Math.ceil(filteredTasks.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        updateTasksDisplay();
    }
}

function showBulkActionsModal() {
    showNotification('نافذة الإجراءات المجمعة قيد التطوير', 'info');
}

function exportTasks() {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }

    showNotification('جاري تصدير المهام...', 'info');

    // إنشاء CSV
    const csvContent = generateTasksCSV();
    downloadFile(csvContent, 'tasks_export.csv', 'text/csv');

    logActivity('tasks_exported', 'تصدير قائمة المهام');
    showNotification('تم تصدير المهام بنجاح', 'success');
}

function generateTasksCSV() {
    const headers = ['العنوان', 'الوصف', 'المسؤول', 'الأولوية', 'الحالة', 'تاريخ الاستحقاق', 'التقدم'];
    const csvRows = [headers.join(',')];

    filteredTasks.forEach(task => {
        const row = [
            `"${task.title}"`,
            `"${task.description}"`,
            `"${task.assignee}"`,
            `"${getPriorityDisplayName(task.priority)}"`,
            `"${getStatusDisplayName(task.status)}"`,
            `"${formatDate(task.dueDate)}"`,
            `"${task.progress}%"`
        ];
        csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
}

function downloadFile(content, fileName, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    URL.revokeObjectURL(url);
}

// تحديث شارات التنقل
function updateNavBadges() {
    const tasksCount = document.getElementById('tasksCount');
    if (tasksCount) {
        tasksCount.textContent = tasks.length;
    }
}

// عروض أخرى (كانبان وتقويم)
function updateKanbanView() {
    showNotification('عرض كانبان قيد التطوير', 'info');
}

function updateCalendarView() {
    showNotification('عرض التقويم قيد التطوير', 'info');
}
