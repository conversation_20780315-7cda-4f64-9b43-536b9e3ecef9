/* ===== أنماط صفحة إدارة المهام والمتابعة ===== */

/* إحصائيات المهام السريعة */
.tasks-stats-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.task-stat-card {
    background: var(--white);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
}

.task-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.total-tasks::before {
    background: linear-gradient(90deg, #2563eb, #1d4ed8);
}

.pending-tasks::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.completed-tasks::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.overdue-tasks::before {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.task-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon-wrapper {
    width: 70px;
    height: 70px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--white);
    flex-shrink: 0;
}

.total-tasks .stat-icon-wrapper {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

.pending-tasks .stat-icon-wrapper {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.completed-tasks .stat-icon-wrapper {
    background: linear-gradient(135deg, #10b981, #059669);
}

.overdue-tasks .stat-icon-wrapper {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin: 0 0 5px 0;
}

.stat-content p {
    font-size: 1.1rem;
    color: var(--gray);
    margin: 0 0 15px 0;
    font-weight: 600;
}

.stat-progress {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f1f5f9;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.85rem;
    color: var(--gray);
    font-weight: 500;
}

/* أدوات التحكم في المهام */
.tasks-controls-section {
    background: var(--white);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f5f9;
}

.section-title {
    color: var(--primary-blue);
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.quick-actions {
    display: flex;
    gap: 15px;
}

.btn-primary-gradient {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-primary-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.btn-secondary-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary-outline:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
}

.btn-info-outline {
    background: transparent;
    color: #0ea5e9;
    border: 2px solid #0ea5e9;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-info-outline:hover {
    background: #0ea5e9;
    color: var(--white);
    transform: translateY(-2px);
}

/* الفلاتر وخيارات العرض */
.filters-and-views {
    display: flex;
    justify-content: space-between;
    align-items: end;
    gap: 30px;
}

.filters-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    flex: 1;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-input-wrapper {
    position: relative;
}

.enhanced-search {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: var(--transition);
    background: var(--white);
}

.enhanced-search:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-clear-btn {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.search-clear-btn:hover {
    background: #f1f5f9;
    color: var(--dark-gray);
}

.enhanced-select {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    background: var(--white);
    cursor: pointer;
    transition: var(--transition);
}

.enhanced-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* خيارات العرض */
.view-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: end;
}

.view-toggle {
    display: flex;
    background: #f1f5f9;
    border-radius: 10px;
    padding: 4px;
    gap: 2px;
}

.view-btn {
    padding: 10px 15px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--gray);
}

.view-btn:hover {
    background: #e2e8f0;
    color: var(--primary-blue);
}

.view-btn.active {
    background: var(--primary-blue);
    color: var(--white);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.sort-options select {
    min-width: 200px;
}

/* جدول المهام المحسن */
.tasks-table-section {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.table-header-controls {
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
}

.table-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.table-title h3 {
    color: var(--primary-blue);
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.btn-table-action {
    width: 40px;
    height: 40px;
    border: 2px solid #e2e8f0;
    background: var(--white);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--gray);
}

.btn-table-action:hover {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
    background: #f0f9ff;
}

.table-pagination-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--gray);
    font-size: 0.95rem;
}

.items-per-page {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: var(--white);
    cursor: pointer;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 15px;
    background: #dbeafe;
    border-radius: 8px;
}

.selected-count {
    color: var(--primary-blue);
    font-weight: 600;
    font-size: 0.9rem;
}

.btn-bulk-action {
    padding: 8px 15px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
}

.btn-bulk-action:first-of-type {
    background: var(--primary-blue);
    color: var(--white);
}

.btn-bulk-action:first-of-type:hover {
    background: #1e40af;
}

.btn-bulk-action:last-of-type {
    background: #ef4444;
    color: var(--white);
}

.btn-bulk-action:last-of-type:hover {
    background: #dc2626;
}

/* الجدول المحسن */
.enhanced-table-container {
    position: relative;
    overflow-x: auto;
}

.enhanced-tasks-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.enhanced-tasks-table th {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    position: relative;
    border-bottom: 2px solid #1e40af;
}

.enhanced-tasks-table th.sortable {
    cursor: pointer;
    user-select: none;
}

.enhanced-tasks-table th.sortable:hover {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
}

.enhanced-tasks-table th span {
    display: inline-block;
    margin-left: 8px;
}

.sort-icon {
    opacity: 0.6;
    transition: var(--transition);
}

.enhanced-tasks-table th.sortable:hover .sort-icon {
    opacity: 1;
}

.enhanced-tasks-table th.sorted .sort-icon {
    opacity: 1;
    color: #fbbf24;
}

.checkbox-column {
    width: 50px;
    text-align: center !important;
}

.actions-column {
    width: 200px;
    text-align: center !important;
}

.enhanced-tasks-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.enhanced-tasks-table tbody tr {
    transition: var(--transition);
}

.enhanced-tasks-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.enhanced-tasks-table tbody tr.selected {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

/* حالات الجدول */
.empty-state,
.loading-state {
    padding: 60px 20px;
    text-align: center;
    color: var(--gray);
}

.empty-state-icon,
.loading-spinner {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.loading-spinner i {
    color: var(--primary-blue);
}

.empty-state h3 {
    color: var(--dark-gray);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 25px;
}

/* تذييل الجدول */
.table-footer {
    padding: 20px 25px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-summary {
    display: flex;
    align-items: center;
    gap: 20px;
}

.summary-text {
    color: var(--gray);
    font-size: 0.95rem;
}

.table-pagination {
    display: flex;
    gap: 5px;
}

.pagination-btn {
    width: 35px;
    height: 35px;
    border: 1px solid #e2e8f0;
    background: var(--white);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    color: var(--gray);
}

.pagination-btn:hover {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
}

.pagination-btn.active {
    background: var(--primary-blue);
    color: var(--white);
    border-color: var(--primary-blue);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn:disabled:hover {
    border-color: #e2e8f0;
    color: var(--gray);
}

/* شارات الحالة والأولوية */
.task-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-in_progress {
    background: #dbeafe;
    color: #1d4ed8;
}

.status-completed {
    background: #d1fae5;
    color: #065f46;
}

.status-cancelled {
    background: #fee2e2;
    color: #991b1b;
}

.priority-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-high {
    background: #fee2e2;
    color: #991b1b;
}

.priority-medium {
    background: #fef3c7;
    color: #92400e;
}

.priority-low {
    background: #f0f9ff;
    color: #0284c7;
}

/* خلايا الجدول المحسنة */
.task-title-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.task-title {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 1rem;
}

.task-description {
    font-size: 0.85rem;
    color: var(--gray);
    opacity: 0.8;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.assignee-cell {
    display: flex;
    align-items: center;
    gap: 10px;
}

.assignee-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    font-size: 0.9rem;
}

.assignee-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.assignee-name {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 0.9rem;
}

.assignee-role {
    font-size: 0.8rem;
    color: var(--gray);
    opacity: 0.8;
}

.due-date-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.due-date {
    font-weight: 600;
    color: var(--dark-gray);
}

.due-date.overdue {
    color: #ef4444;
}

.due-date.today {
    color: #f59e0b;
}

.due-date.upcoming {
    color: #10b981;
}

.due-time-remaining {
    font-size: 0.8rem;
    color: var(--gray);
    opacity: 0.8;
}

.task-progress-cell {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.task-progress-bar {
    width: 80px;
    height: 8px;
    background: #f1f5f9;
    border-radius: 4px;
    overflow: hidden;
}

.task-progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-0 { background: #ef4444; }
.progress-25 { background: #f59e0b; }
.progress-50 { background: #3b82f6; }
.progress-75 { background: #8b5cf6; }
.progress-100 { background: #10b981; }

.task-progress-text {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--dark-gray);
}

/* أزرار الإجراءات */
.task-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
    flex-wrap: wrap;
}

.task-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.task-action-btn.view {
    background: #e0f2fe;
    color: #0891b2;
}

.task-action-btn.view:hover {
    background: #0891b2;
    color: var(--white);
    transform: scale(1.1);
}

.task-action-btn.edit {
    background: #fef3c7;
    color: #d97706;
}

.task-action-btn.edit:hover {
    background: #d97706;
    color: var(--white);
    transform: scale(1.1);
}

.task-action-btn.complete {
    background: #ecfdf5;
    color: #059669;
}

.task-action-btn.complete:hover {
    background: #059669;
    color: var(--white);
    transform: scale(1.1);
}

.task-action-btn.delete {
    background: #fee2e2;
    color: #dc2626;
}

.task-action-btn.delete:hover {
    background: #dc2626;
    color: var(--white);
    transform: scale(1.1);
}

/* تصميم متجاوب للمهام */
@media (max-width: 1200px) {
    .tasks-stats-dashboard {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .filters-section {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .controls-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .quick-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .filters-and-views {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .filters-section {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .view-options {
        align-items: stretch;
    }

    .view-toggle {
        justify-content: center;
    }

    .tasks-stats-dashboard {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .task-stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .stat-content h3 {
        font-size: 2rem;
    }

    .table-header-controls {
        padding: 15px 20px;
    }

    .table-title {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .table-title h3 {
        text-align: center;
    }

    .table-actions {
        justify-content: center;
    }

    .table-pagination-top {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .pagination-info {
        justify-content: center;
    }

    .enhanced-tasks-table {
        font-size: 0.85rem;
    }

    .enhanced-tasks-table th,
    .enhanced-tasks-table td {
        padding: 10px 8px;
    }

    .table-footer {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .table-summary {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }

    .table-pagination {
        justify-content: center;
    }

    .task-description {
        max-width: 150px;
    }

    .assignee-cell {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .tasks-controls-section,
    .tasks-table-section {
        margin-left: -10px;
        margin-right: -10px;
        border-radius: 0;
    }

    .stat-icon-wrapper {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stat-content h3 {
        font-size: 1.8rem;
    }

    .enhanced-tasks-table {
        font-size: 0.8rem;
    }

    .enhanced-tasks-table th,
    .enhanced-tasks-table td {
        padding: 8px 6px;
    }

    .checkbox-column {
        width: 40px;
    }

    .actions-column {
        width: 120px;
    }

    .task-actions {
        flex-direction: column;
        gap: 5px;
    }

    .task-action-btn {
        width: 100%;
        justify-content: center;
    }

    .task-description {
        max-width: 100px;
    }
}
