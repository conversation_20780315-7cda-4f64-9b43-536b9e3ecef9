# نظام إدارة العملاء والموردين وفريق التلي سيلز

نظام إدارة شامل ومتكامل لإدارة العملاء والموردين وفريق التلي سيلز مع واجهة عربية عصرية وميزات متقدمة.

## 🌟 الميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- **ثلاثة مستويات صلاحيات:**
  - **مدير**: صلاحية كاملة على جميع الوظائف
  - **مشرف**: إشراف وتعديل البيانات
  - **تلي سيلز**: عرض محدود وإدخال بيانات فقط

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- تخزين البيانات الشاملة (الاسم، الجوال، البريد، المدينة، النشاط، إلخ)
- بحث وفلترة متقدمة
- تتبع آخر تواصل وحالة العميل
- استيراد وتصدير البيانات

### 🚛 إدارة الموردين
- إدارة بيانات الموردين الكاملة
- تصنيف حسب نوع الخدمة والمدينة
- بحث وفلترة ذكية
- استيراد وتصدير البيانات

### 👨‍💼 إدارة فريق التلي سيلز
- إدارة بيانات الموظفين
- تتبع الأداء والإنجازات
- إدارة المهام اليومية
- تقييم الأداء الأسبوعي

### 📊 التقارير والإحصائيات
- تقارير شاملة لجميع الأقسام
- إحصائيات في الوقت الفعلي
- تقارير مخصصة قابلة للتصدير
- رسوم بيانية تفاعلية

### 📱 واجهة المستخدم
- **تصميم عصري** بالألوان الزرقاء
- **واجهة ثنائية اللغة** (عربي/إنجليزي)
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **تجربة مستخدم سلسة** وبديهية

### ⚙️ الإعدادات والتخصيص
- **صفحة إعدادات شاملة** مع تبويبات منظمة
- **إعدادات عامة**: اللغة، التاريخ، عدد العناصر، حجم الخط
- **إعدادات الحساب**: تغيير كلمة المرور، البيانات الشخصية، الأمان
- **إعدادات الإشعارات**: تحكم كامل في التنبيهات والأصوات
- **إعدادات المظهر**: ثيمات ألوان، وضع مضغوط، كثافة البيانات

### 👤 الملف الشخصي المحسن
- **ثلاثة تبويبات**: المعلومات الأساسية، الإحصائيات، تعديل البيانات
- **رفع وتغيير صورة المستخدم** مع معاينة فورية
- **إحصائيات شخصية مفصلة** للأنشطة والإنجازات
- **تعديل البيانات الشخصية** مع التحقق من الصحة

## 🚀 كيفية الاستخدام

### 1. تشغيل النظام
1. افتح ملف `index.html` في المتصفح
2. استخدم أحد الحسابات التجريبية للدخول

### 2. الحسابات التجريبية

| الدور | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|--------------|-------------|-----------|
| مدير | `admin` | `admin123` | صلاحية كاملة |
| مشرف | `supervisor` | `super123` | إشراف وتعديل |
| تلي سيلز | `telesales` | `tele123` | عرض محدود |

### 3. التنقل في النظام
- **لوحة التحكم**: نظرة عامة وإحصائيات سريعة
- **إدارة العملاء**: إضافة وتعديل ومتابعة العملاء
- **إدارة الموردين**: إدارة بيانات الموردين والخدمات
- **فريق التلي سيلز**: إدارة الموظفين والمهام
- **التقارير**: تقارير شاملة وإحصائيات
- **سجل الأنشطة**: تتبع جميع العمليات
- **الإعدادات**: تخصيص النظام والحساب الشخصي

### 4. استخدام الميزات الجديدة

#### الإعدادات:
1. انقر على اسم المستخدم في الزاوية العلوية
2. اختر "الإعدادات" من القائمة المنسدلة
3. استخدم التبويبات للتنقل بين الأقسام المختلفة
4. احفظ التغييرات لتطبيقها فوراً

#### الملف الشخصي:
1. انقر على اسم المستخدم ثم "الملف الشخصي"
2. استخدم التبويبات لعرض المعلومات أو الإحصائيات
3. في تبويب "تعديل البيانات" يمكنك تحديث معلوماتك
4. انقر على "تغيير الصورة" لرفع صورة شخصية جديدة

## 📁 هيكل المشروع

```
├── index.html              # صفحة تسجيل الدخول
├── dashboard.html          # لوحة التحكم الرئيسية
├── customers.html          # إدارة العملاء
├── suppliers.html          # إدارة الموردين
├── telesales.html          # إدارة فريق التلي سيلز
├── reports.html            # التقارير
├── activities.html         # سجل الأنشطة
├── settings.html           # صفحة الإعدادات الشاملة
├── css/
│   ├── style.css          # الأنماط الأساسية + إعدادات التخصيص
│   ├── dashboard.css      # أنماط لوحة التحكم
│   └── customers.css      # أنماط الجداول والنماذج والإعدادات
├── js/
│   ├── auth.js           # نظام المصادقة
│   ├── utils.js          # الوظائف المساعدة
│   ├── dashboard.js      # وظائف لوحة التحكم + الملف الشخصي المحسن
│   ├── customers.js      # وظائف إدارة العملاء + النوافذ المنبثقة
│   ├── suppliers.js      # وظائف إدارة الموردين
│   ├── telesales.js      # وظائف إدارة التلي سيلز
│   ├── reports.js        # وظائف التقارير
│   ├── activities.js     # وظائف سجل الأنشطة
│   └── settings.js       # وظائف الإعدادات والتخصيص
└── README.md             # دليل الاستخدام
```

## 🔧 الميزات التقنية

### 💾 التخزين
- **Local Storage**: حفظ جميع البيانات محلياً
- **بيانات تجريبية**: عينات جاهزة للاختبار
- **نسخ احتياطي**: إمكانية تصدير واستيراد البيانات

### 🌐 التوافق
- **المتصفحات**: Chrome, Firefox, Safari, Edge
- **الأجهزة**: Desktop, Tablet, Mobile
- **اللغات**: العربية (افتراضي) + الإنجليزية

### 📤 التصدير والاستيراد
- **تصدير**: CSV, JSON, طباعة
- **استيراد**: CSV (Excel قيد التطوير)
- **تقارير مخصصة**: إنشاء تقارير حسب الحاجة

## 🎯 الوظائف المتقدمة

### 🔍 البحث والفلترة
- بحث فوري في جميع البيانات
- فلترة متعددة المعايير
- ترتيب ديناميكي للنتائج

### 📈 تتبع الأداء
- إحصائيات في الوقت الفعلي
- تقييم أداء الموظفين
- متابعة المهام والإنجازات

### 🔒 الأمان
- نظام صلاحيات متدرج
- تسجيل جميع الأنشطة
- حماية البيانات الحساسة

### ⚙️ التخصيص المتقدم
- **ثيمات ألوان متعددة**: أزرق، أخضر، بنفسجي
- **أحجام خط متغيرة**: صغير، متوسط، كبير
- **كثافة بيانات قابلة للتعديل**: مضغوط، مريح، واسع
- **وضع مضغوط للشريط الجانبي**
- **تحكم في الحركات والانتقالات**

### 👤 إدارة الملف الشخصي
- **رفع وتغيير الصور الشخصية** مع معاينة فورية
- **إحصائيات شخصية مفصلة** للأنشطة والإنجازات
- **تعديل البيانات الشخصية** مع التحقق من الصحة
- **تتبع آخر تسجيل دخول** وتاريخ الانضمام

### 🔐 إدارة كلمات المرور
- **تغيير كلمة المرور** مع التحقق من القوة
- **مؤشر قوة كلمة المرور** في الوقت الفعلي
- **إعدادات الأمان المتقدمة** (مهلة الجلسة، تسجيل خروج تلقائي)

### 🔔 نظام الإشعارات
- **تحكم كامل في الإشعارات الصوتية**
- **تنبيهات النظام القابلة للتخصيص**
- **إعدادات مستوى الصوت**
- **تذكير بالمهام والمواعيد**

## 🛠️ التطوير المستقبلي

### المرحلة التالية
- [ ] دعم كامل لملفات Excel
- [ ] نظام الإشعارات
- [ ] تقارير متقدمة مع رسوم بيانية
- [ ] تطبيق جوال
- [ ] تكامل مع APIs خارجية

### التحسينات المقترحة
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] واجهة إدارة المستخدمين
- [ ] تخصيص الواجهة
- [ ] دعم المزيد من اللغات

## 📞 الدعم والمساعدة

### المشاكل الشائعة
1. **لا يمكن تسجيل الدخول**: تأكد من استخدام الحسابات التجريبية الصحيحة
2. **البيانات لا تظهر**: تأكد من تفعيل JavaScript في المتصفح
3. **مشاكل التصدير**: تأكد من السماح بتحميل الملفات

### متطلبات النظام
- متصفح حديث يدعم HTML5 و CSS3 و JavaScript ES6
- دعم Local Storage
- اتصال بالإنترنت لتحميل الخطوط والأيقونات

## 📄 الترخيص

هذا المشروع مطور لأغراض تعليمية وتجريبية. يمكن استخدامه وتطويره بحرية.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2024  
**الإصدار**: 1.0.0
