# نظام إدارة العملاء والموردين وفريق التلي سيلز

نظام إدارة شامل ومتكامل لإدارة العملاء والموردين وفريق التلي سيلز مع واجهة عربية عصرية وميزات متقدمة.

## 🌟 الميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- **ثلاثة مستويات صلاحيات:**
  - **مدير**: صلاحية كاملة على جميع الوظائف
  - **مشرف**: إشراف وتعديل البيانات
  - **تلي سيلز**: عرض محدود وإدخال بيانات فقط

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- تخزين البيانات الشاملة (الاسم، الجوال، البريد، المدينة، النشاط، إلخ)
- بحث وفلترة متقدمة
- تتبع آخر تواصل وحالة العميل
- استيراد وتصدير البيانات

### 🚛 إدارة الموردين
- إدارة بيانات الموردين الكاملة
- تصنيف حسب نوع الخدمة والمدينة
- بحث وفلترة ذكية
- استيراد وتصدير البيانات

### 👨‍💼 إدارة فريق التلي سيلز
- إدارة بيانات الموظفين
- تتبع الأداء والإنجازات
- إدارة المهام اليومية
- تقييم الأداء الأسبوعي

### 📊 التقارير والإحصائيات
- تقارير شاملة لجميع الأقسام
- إحصائيات في الوقت الفعلي
- تقارير مخصصة قابلة للتصدير
- رسوم بيانية تفاعلية

### 📱 واجهة المستخدم
- **تصميم عصري** بالألوان الزرقاء
- **واجهة ثنائية اللغة** (عربي/إنجليزي)
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **تجربة مستخدم سلسة** وبديهية

## 🚀 كيفية الاستخدام

### 1. تشغيل النظام
1. افتح ملف `index.html` في المتصفح
2. استخدم أحد الحسابات التجريبية للدخول

### 2. الحسابات التجريبية

| الدور | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|--------------|-------------|-----------|
| مدير | `admin` | `admin123` | صلاحية كاملة |
| مشرف | `supervisor` | `super123` | إشراف وتعديل |
| تلي سيلز | `telesales` | `tele123` | عرض محدود |

### 3. التنقل في النظام
- **لوحة التحكم**: نظرة عامة وإحصائيات سريعة
- **إدارة العملاء**: إضافة وتعديل ومتابعة العملاء
- **إدارة الموردين**: إدارة بيانات الموردين والخدمات
- **فريق التلي سيلز**: إدارة الموظفين والمهام
- **التقارير**: تقارير شاملة وإحصائيات
- **سجل الأنشطة**: تتبع جميع العمليات

## 📁 هيكل المشروع

```
├── index.html              # صفحة تسجيل الدخول
├── dashboard.html          # لوحة التحكم الرئيسية
├── customers.html          # إدارة العملاء
├── suppliers.html          # إدارة الموردين
├── telesales.html          # إدارة فريق التلي سيلز
├── reports.html            # التقارير
├── activities.html         # سجل الأنشطة
├── css/
│   ├── style.css          # الأنماط الأساسية
│   ├── dashboard.css      # أنماط لوحة التحكم
│   └── customers.css      # أنماط الجداول والنماذج
├── js/
│   ├── auth.js           # نظام المصادقة
│   ├── utils.js          # الوظائف المساعدة
│   ├── dashboard.js      # وظائف لوحة التحكم
│   ├── customers.js      # وظائف إدارة العملاء
│   ├── suppliers.js      # وظائف إدارة الموردين
│   ├── telesales.js      # وظائف إدارة التلي سيلز
│   ├── reports.js        # وظائف التقارير
│   └── activities.js     # وظائف سجل الأنشطة
└── README.md             # دليل الاستخدام
```

## 🔧 الميزات التقنية

### 💾 التخزين
- **Local Storage**: حفظ جميع البيانات محلياً
- **بيانات تجريبية**: عينات جاهزة للاختبار
- **نسخ احتياطي**: إمكانية تصدير واستيراد البيانات

### 🌐 التوافق
- **المتصفحات**: Chrome, Firefox, Safari, Edge
- **الأجهزة**: Desktop, Tablet, Mobile
- **اللغات**: العربية (افتراضي) + الإنجليزية

### 📤 التصدير والاستيراد
- **تصدير**: CSV, JSON, طباعة
- **استيراد**: CSV (Excel قيد التطوير)
- **تقارير مخصصة**: إنشاء تقارير حسب الحاجة

## 🎯 الوظائف المتقدمة

### 🔍 البحث والفلترة
- بحث فوري في جميع البيانات
- فلترة متعددة المعايير
- ترتيب ديناميكي للنتائج

### 📈 تتبع الأداء
- إحصائيات في الوقت الفعلي
- تقييم أداء الموظفين
- متابعة المهام والإنجازات

### 🔒 الأمان
- نظام صلاحيات متدرج
- تسجيل جميع الأنشطة
- حماية البيانات الحساسة

## 🛠️ التطوير المستقبلي

### المرحلة التالية
- [ ] دعم كامل لملفات Excel
- [ ] نظام الإشعارات
- [ ] تقارير متقدمة مع رسوم بيانية
- [ ] تطبيق جوال
- [ ] تكامل مع APIs خارجية

### التحسينات المقترحة
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] واجهة إدارة المستخدمين
- [ ] تخصيص الواجهة
- [ ] دعم المزيد من اللغات

## 📞 الدعم والمساعدة

### المشاكل الشائعة
1. **لا يمكن تسجيل الدخول**: تأكد من استخدام الحسابات التجريبية الصحيحة
2. **البيانات لا تظهر**: تأكد من تفعيل JavaScript في المتصفح
3. **مشاكل التصدير**: تأكد من السماح بتحميل الملفات

### متطلبات النظام
- متصفح حديث يدعم HTML5 و CSS3 و JavaScript ES6
- دعم Local Storage
- اتصال بالإنترنت لتحميل الخطوط والأيقونات

## 📄 الترخيص

هذا المشروع مطور لأغراض تعليمية وتجريبية. يمكن استخدامه وتطويره بحرية.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2024  
**الإصدار**: 1.0.0
