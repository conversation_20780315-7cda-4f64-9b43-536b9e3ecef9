/* ===== متغيرات الألوان ===== */
:root {
    --primary-blue: #2563eb;
    --secondary-blue: #3b82f6;
    --light-blue: #dbeafe;
    --dark-blue: #1e40af;
    --accent-blue: #60a5fa;
    --white: #ffffff;
    --light-gray: #f8fafc;
    --gray: #64748b;
    --dark-gray: #334155;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --shadow: rgba(0, 0, 0, 0.1);
    --border-radius: 12px;
    --transition: all 0.3s ease;
    --base-font-size: 16px;
}

/* ===== إعدادات حجم الخط ===== */
body {
    font-size: var(--base-font-size);
}

/* ===== إعدادات كثافة البيانات ===== */
.density-compact .customers-table th,
.density-compact .customers-table td {
    padding: 8px 10px;
    font-size: 0.85rem;
}

.density-comfortable .customers-table th,
.density-comfortable .customers-table td {
    padding: 15px 12px;
    font-size: 0.9rem;
}

.density-spacious .customers-table th,
.density-spacious .customers-table td {
    padding: 20px 15px;
    font-size: 1rem;
}

/* ===== الوضع المضغوط ===== */
.compact-mode .sidebar {
    width: 60px;
}

.compact-mode .sidebar .sidebar-header span,
.compact-mode .sidebar .nav-item span,
.compact-mode .sidebar .nav-badge {
    display: none;
}

.compact-mode .main-content {
    margin-right: 60px;
}

.compact-mode .page-header {
    padding: 15px 20px;
}

.compact-mode .stats-row {
    gap: 15px;
}

.compact-mode .stat-item {
    padding: 15px;
}

/* ===== إعدادات الحركات ===== */
.no-animations * {
    transition: none !important;
    animation: none !important;
}

/* ===== إعدادات عامة ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--dark-gray);
    line-height: 1.6;
    min-height: 100vh;
}

/* ===== صفحة تسجيل الدخول ===== */
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 450px;
}

.login-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    text-align: center;
    padding: 40px 30px;
}

.login-header i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.login-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.login-header p {
    opacity: 0.9;
    font-size: 0.95rem;
}

.login-form {
    padding: 40px 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-gray);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 15px;
    color: var(--gray);
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 15px 45px 15px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--white);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.toggle-password {
    position: absolute;
    left: 15px;
    background: none;
    border: none;
    color: var(--gray);
    cursor: pointer;
    padding: 5px;
    z-index: 2;
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    border: none;
    padding: 15px;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
}

.login-footer {
    background: var(--light-gray);
    padding: 25px 30px;
    border-top: 1px solid #e2e8f0;
}

.language-toggle {
    text-align: center;
    margin-bottom: 20px;
}

.lang-btn {
    background: var(--white);
    border: 2px solid var(--primary-blue);
    color: var(--primary-blue);
    padding: 8px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.lang-btn:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.demo-accounts h4 {
    color: var(--dark-gray);
    margin-bottom: 15px;
    text-align: center;
}

.demo-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.demo-item {
    background: var(--white);
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    border-left: 4px solid var(--primary-blue);
}

.demo-item strong {
    color: var(--primary-blue);
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 480px) {
    .login-container {
        max-width: 100%;
        margin: 0 10px;
    }
    
    .login-form,
    .login-header,
    .login-footer {
        padding: 25px 20px;
    }
    
    .login-header h1 {
        font-size: 1.3rem;
    }
    
    .demo-list {
        font-size: 0.85rem;
    }
}

/* ===== حالة التحميل ===== */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading .login-btn {
    background: var(--gray);
}

/* ===== رسائل التنبيه ===== */
.alert {
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}
