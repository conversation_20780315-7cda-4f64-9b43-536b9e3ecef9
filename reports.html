<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-ar="التقارير والتحليلات - نظام إدارة العملاء والموردين" data-en="Reports & Analytics - Customer & Supplier Management System">التقارير والتحليلات - نظام إدارة العملاء والموردين</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link rel="stylesheet" href="css/reports.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-chart-line"></i>
            <span data-ar="نظام الإدارة" data-en="Management System">نظام الإدارة</span>
        </div>

        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span data-ar="لوحة التحكم" data-en="Dashboard">لوحة التحكم</span>
            </a>

            <a href="customers.html" class="nav-item">
                <i class="fas fa-users"></i>
                <span data-ar="العملاء" data-en="Customers">العملاء</span>
                <span class="nav-badge" id="customersCount">0</span>
            </a>

            <a href="suppliers.html" class="nav-item">
                <i class="fas fa-truck"></i>
                <span data-ar="الموردين" data-en="Suppliers">الموردين</span>
                <span class="nav-badge" id="suppliersCount">0</span>
            </a>

            <a href="quotations.html" class="nav-item">
                <i class="fas fa-file-invoice-dollar"></i>
                <span data-ar="عروض الأسعار" data-en="Quotations">عروض الأسعار</span>
                <span class="nav-badge" id="quotationsCount">0</span>
            </a>

            <a href="telesales.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span data-ar="فريق التلي سيلز" data-en="Telesales Team">فريق التلي سيلز</span>
                <span class="nav-badge" id="telesalesCount">0</span>
            </a>

            <a href="reports.html" class="nav-item active">
                <i class="fas fa-chart-bar"></i>
                <span data-ar="التقارير" data-en="Reports">التقارير</span>
            </a>

            <a href="activities.html" class="nav-item">
                <i class="fas fa-history"></i>
                <span data-ar="سجل الأنشطة" data-en="Activity Log">سجل الأنشطة</span>
            </a>
        </nav>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- رأس الصفحة -->
        <header class="page-header">
            <div class="header-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 data-ar="التقارير والتحليلات" data-en="Reports & Analytics">التقارير والتحليلات</h1>
            </div>

            <div class="header-right">
                <button class="language-toggle" onclick="toggleLanguage()">
                    <i class="fas fa-globe"></i>
                    <span id="currentLang">العربية</span>
                </button>

                <div class="user-menu">
                    <button class="user-menu-toggle" onclick="toggleUserMenu()">
                        <i class="fas fa-user-circle"></i>
                        <span id="userName">المستخدم</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-menu-dropdown" id="userMenuDropdown">
                        <a href="#" onclick="showProfile()">
                            <i class="fas fa-user"></i>
                            <span data-ar="الملف الشخصي" data-en="Profile">الملف الشخصي</span>
                        </a>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span data-ar="الإعدادات" data-en="Settings">الإعدادات</span>
                        </a>
                        <a href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            <span data-ar="تسجيل الخروج" data-en="Logout">تسجيل الخروج</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- أدوات التحكم في التقارير -->
        <div class="reports-controls">
            <div class="controls-header">
                <h2 class="section-title">
                    <i class="fas fa-filter"></i>
                    فلاتر التقارير
                </h2>
                <div class="quick-actions">
                    <button class="btn-primary-gradient" onclick="generateReport()">
                        <i class="fas fa-chart-line"></i>
                        إنشاء تقرير
                    </button>
                    <button class="btn-secondary-outline" onclick="exportAllReports()">
                        <i class="fas fa-download"></i>
                        تصدير جميع التقارير
                    </button>
                </div>
            </div>

            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-calendar"></i>
                        الفترة الزمنية
                    </label>
                    <select id="dateRange" class="enhanced-select" onchange="updateReports()">
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month" selected>هذا الشهر</option>
                        <option value="quarter">هذا الربع</option>
                        <option value="year">هذا العام</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-chart-bar"></i>
                        نوع التقرير
                    </label>
                    <select id="reportType" class="enhanced-select" onchange="updateReports()">
                        <option value="overview">نظرة عامة</option>
                        <option value="customers">تقرير العملاء</option>
                        <option value="suppliers">تقرير الموردين</option>
                        <option value="quotations">تقرير عروض الأسعار</option>
                        <option value="sales">تقرير المبيعات</option>
                        <option value="performance">تقرير الأداء</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-map-marker-alt"></i>
                        المدينة
                    </label>
                    <select id="cityFilter" class="enhanced-select" onchange="updateReports()">
                        <option value="">جميع المدن</option>
                        <option value="riyadh">الرياض</option>
                        <option value="jeddah">جدة</option>
                        <option value="dammam">الدمام</option>
                        <option value="mecca">مكة المكرمة</option>
                        <option value="medina">المدينة المنورة</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-tags"></i>
                        الحالة
                    </label>
                    <select id="statusFilter" class="enhanced-select" onchange="updateReports()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="pending">معلق</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- لوحة المعلومات السريعة -->
        <div class="quick-stats-dashboard">
            <div class="stat-card-modern total-revenue">
                <div class="stat-icon-modern">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content-modern">
                    <h3 id="totalRevenue">0</h3>
                    <p>إجمالي الإيرادات</p>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+15.3%</span>
                    </div>
                </div>
            </div>

            <div class="stat-card-modern total-customers">
                <div class="stat-icon-modern">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content-modern">
                    <h3 id="totalCustomersCount">0</h3>
                    <p>إجمالي العملاء</p>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+8.2%</span>
                    </div>
                </div>
            </div>

            <div class="stat-card-modern conversion-rate">
                <div class="stat-icon-modern">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-content-modern">
                    <h3 id="conversionRate">0%</h3>
                    <p>معدل التحويل</p>
                    <div class="stat-change negative">
                        <i class="fas fa-arrow-down"></i>
                        <span>-2.1%</span>
                    </div>
                </div>
            </div>

            <div class="stat-card-modern avg-deal-size">
                <div class="stat-icon-modern">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="stat-content-modern">
                    <h3 id="avgDealSize">0</h3>
                    <p>متوسط حجم الصفقة</p>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+12.7%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="charts-grid">
            <div class="chart-container">
                <div class="chart-header">
                    <h3>
                        <i class="fas fa-chart-line"></i>
                        اتجاه المبيعات
                    </h3>
                    <div class="chart-actions">
                        <button class="btn-chart-action" onclick="exportChart('salesTrend')">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn-chart-action" onclick="fullscreenChart('salesTrend')">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
                <canvas id="salesTrendChart"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-header">
                    <h3>
                        <i class="fas fa-chart-pie"></i>
                        توزيع العملاء حسب المدينة
                    </h3>
                    <div class="chart-actions">
                        <button class="btn-chart-action" onclick="exportChart('customerDistribution')">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn-chart-action" onclick="fullscreenChart('customerDistribution')">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
                <canvas id="customerDistributionChart"></canvas>
            </div>
        </div>

        <!-- جداول التقارير -->
        <div class="reports-tables">
            <div class="table-tabs">
                <button class="tab-btn active" onclick="switchTab('customers')">
                    <i class="fas fa-users"></i>
                    تقرير العملاء
                </button>
                <button class="tab-btn" onclick="switchTab('quotations')">
                    <i class="fas fa-file-invoice-dollar"></i>
                    تقرير عروض الأسعار
                </button>
                <button class="tab-btn" onclick="switchTab('performance')">
                    <i class="fas fa-chart-bar"></i>
                    تقرير الأداء
                </button>
            </div>

            <div class="tab-content" id="customersTab">
                <div class="table-header">
                    <h3>تقرير العملاء التفصيلي</h3>
                    <button class="btn-export" onclick="exportCustomersReport()">
                        <i class="fas fa-file-excel"></i>
                        تصدير Excel
                    </button>
                </div>
                <div class="table-container">
                    <table class="reports-table" id="customersReportTable">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>المدينة</th>
                                <th>عدد العروض</th>
                                <th>القيمة الإجمالية</th>
                                <th>آخر نشاط</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="customersReportBody">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="tab-content" id="quotationsTab" style="display: none;">
                <div class="table-header">
                    <h3>تقرير عروض الأسعار</h3>
                    <button class="btn-export" onclick="exportQuotationsReport()">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                </div>
                <div class="table-container">
                    <table class="reports-table" id="quotationsReportTable">
                        <thead>
                            <tr>
                                <th>رقم العرض</th>
                                <th>العميل</th>
                                <th>التاريخ</th>
                                <th>القيمة</th>
                                <th>الحالة</th>
                                <th>معدل النجاح</th>
                            </tr>
                        </thead>
                        <tbody id="quotationsReportBody">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="tab-content" id="performanceTab" style="display: none;">
                <div class="table-header">
                    <h3>تقرير أداء الموظفين</h3>
                    <button class="btn-export" onclick="exportPerformanceReport()">
                        <i class="fas fa-file-csv"></i>
                        تصدير CSV
                    </button>
                </div>
                <div class="table-container">
                    <table class="reports-table" id="performanceReportTable">
                        <thead>
                            <tr>
                                <th>اسم الموظف</th>
                                <th>القسم</th>
                                <th>عدد المكالمات</th>
                                <th>المكالمات الناجحة</th>
                                <th>معدل النجاح</th>
                                <th>التقييم</th>
                            </tr>
                        </thead>
                        <tbody id="performanceReportBody">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/reports.js"></script>

    <script>
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من تسجيل الدخول
            if (!requireAuth()) return;

            // تحديث اللغة
            updateLanguage();

            // تحميل التقارير
            loadReports();

            // تهيئة الرسوم البيانية
            initializeCharts();

            // تسجيل النشاط
            logActivity('reports_view', 'عرض صفحة التقارير والتحليلات');
        });
    </script>
</body>
</html>
