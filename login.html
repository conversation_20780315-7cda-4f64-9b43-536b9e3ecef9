<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - شركة نخبة الإعلان للدعاية والإعلان</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #2563eb;
            --secondary-blue: #3b82f6;
            --light-blue: #dbeafe;
            --dark-gray: #1f2937;
            --gray: #6b7280;
            --light-gray: #f3f4f6;
            --white: #ffffff;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --border-radius: 12px;
            --transition: all 0.3s ease;
            --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        .login-left {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: var(--white);
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .company-logo {
            font-size: 4rem;
            margin-bottom: 30px;
            color: #fbbf24;
        }

        .company-name {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .company-tagline {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .company-features {
            list-style: none;
            text-align: right;
        }

        .company-features li {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .company-features i {
            color: #fbbf24;
            font-size: 1.2rem;
        }

        .login-right {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-title {
            font-size: 2.5rem;
            color: var(--dark-gray);
            margin-bottom: 10px;
            font-weight: 700;
        }

        .login-subtitle {
            color: var(--gray);
            font-size: 1.1rem;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .form-group {
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark-gray);
            font-weight: 600;
            font-size: 1rem;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e5e7eb;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
            background: var(--white);
            font-family: 'Cairo', sans-serif;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-input.error {
            border-color: var(--danger);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray);
            font-size: 1.1rem;
        }

        .form-input.with-icon {
            padding-left: 50px;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--gray);
            cursor: pointer;
            font-size: 1.1rem;
            padding: 5px;
        }

        .password-toggle:hover {
            color: var(--primary-blue);
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--gray);
            font-size: 0.9rem;
        }

        .remember-me input {
            width: 16px;
            height: 16px;
        }

        .forgot-password {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .login-btn {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: var(--white);
            border: none;
            padding: 15px 30px;
            border-radius: var(--border-radius);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            font-family: 'Cairo', sans-serif;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .btn-loading {
            display: none;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn-loading.show {
            display: flex;
        }

        .btn-text.loading {
            display: none;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--white);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .error-message {
            background: #fee2e2;
            color: var(--danger);
            padding: 12px 15px;
            border-radius: var(--border-radius);
            border: 1px solid #fecaca;
            font-size: 0.9rem;
            display: none;
            align-items: center;
            gap: 10px;
        }

        .error-message.show {
            display: flex;
        }

        .success-message {
            background: #d1fae5;
            color: var(--success);
            padding: 12px 15px;
            border-radius: var(--border-radius);
            border: 1px solid #a7f3d0;
            font-size: 0.9rem;
            display: none;
            align-items: center;
            gap: 10px;
        }

        .success-message.show {
            display: flex;
        }

        .demo-accounts {
            margin-top: 30px;
            padding: 20px;
            background: var(--light-gray);
            border-radius: var(--border-radius);
        }

        .demo-title {
            font-weight: 600;
            color: var(--dark-gray);
            margin-bottom: 15px;
            text-align: center;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: var(--white);
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: var(--transition);
        }

        .demo-account:hover {
            background: var(--light-blue);
        }

        .demo-account:last-child {
            margin-bottom: 0;
        }

        .demo-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .demo-role {
            font-weight: 600;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        .demo-username {
            color: var(--gray);
            font-size: 0.8rem;
        }

        .demo-use-btn {
            background: var(--primary-blue);
            color: var(--white);
            border: none;
            padding: 5px 10px;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .demo-use-btn:hover {
            background: var(--secondary-blue);
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .login-container {
                grid-template-columns: 1fr;
                max-width: 500px;
            }

            .login-left {
                padding: 40px 30px;
            }

            .login-right {
                padding: 40px 30px;
            }

            .company-name {
                font-size: 1.5rem;
            }

            .login-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .login-left {
                padding: 30px 20px;
            }

            .login-right {
                padding: 30px 20px;
            }

            .company-logo {
                font-size: 3rem;
            }

            .company-name {
                font-size: 1.3rem;
            }

            .login-title {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- الجانب الأيسر - معلومات الشركة -->
        <div class="login-left">
            <div class="company-logo">
                <i class="fas fa-building"></i>
            </div>
            <h1 class="company-name">شركة نخبة الإعلان للدعاية والإعلان</h1>
            <p class="company-tagline">إبداع في الدعاية والإعلان يحقق أهدافكم التسويقية</p>
            
            <ul class="company-features">
                <li>
                    <i class="fas fa-check-circle"></i>
                    <span>نظام إدارة شامل للعملاء والموردين</span>
                </li>
                <li>
                    <i class="fas fa-check-circle"></i>
                    <span>فريق تلي سيلز متخصص ومحترف</span>
                </li>
                <li>
                    <i class="fas fa-check-circle"></i>
                    <span>تقارير مفصلة وإحصائيات دقيقة</span>
                </li>
                <li>
                    <i class="fas fa-check-circle"></i>
                    <span>أمان عالي وحماية للبيانات</span>
                </li>
            </ul>
        </div>

        <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
        <div class="login-right">
            <div class="login-header">
                <h2 class="login-title">مرحباً بك</h2>
                <p class="login-subtitle">سجل دخولك للوصول إلى لوحة التحكم</p>
            </div>

            <!-- رسائل الخطأ والنجاح -->
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-circle"></i>
                <span id="errorText">حدث خطأ في تسجيل الدخول</span>
            </div>

            <div class="success-message" id="successMessage">
                <i class="fas fa-check-circle"></i>
                <span id="successText">تم تسجيل الدخول بنجاح</span>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label class="form-label" for="username">اسم المستخدم</label>
                    <div style="position: relative;">
                        <input type="text" id="username" name="username" class="form-input with-icon" 
                               placeholder="أدخل اسم المستخدم" required>
                        <i class="fas fa-user input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">كلمة المرور</label>
                    <div style="position: relative;">
                        <input type="password" id="password" name="password" class="form-input with-icon" 
                               placeholder="أدخل كلمة المرور" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        <span>تذكرني</span>
                    </label>
                    <a href="#" class="forgot-password" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <span class="btn-text" id="btnText">تسجيل الدخول</span>
                    <div class="btn-loading" id="btnLoading">
                        <div class="spinner"></div>
                        <span>جاري تسجيل الدخول...</span>
                    </div>
                </button>
            </form>

            <!-- حسابات تجريبية -->
            <div class="demo-accounts">
                <div class="demo-title">حسابات تجريبية للاختبار</div>
                
                <div class="demo-account" onclick="useDemoAccount('admin', 'Admin@2024')">
                    <div class="demo-info">
                        <div class="demo-role">مدير النظام</div>
                        <div class="demo-username">admin</div>
                    </div>
                    <button class="demo-use-btn">استخدام</button>
                </div>

                <div class="demo-account" onclick="useDemoAccount('suhailah.azhari', 'Suhailah@2024')">
                    <div class="demo-info">
                        <div class="demo-role">موظفة تلي سيلز</div>
                        <div class="demo-username">suhailah.azhari</div>
                    </div>
                    <button class="demo-use-btn">استخدام</button>
                </div>

                <div class="demo-account" onclick="useDemoAccount('emad.supplier', 'Emad@2024')">
                    <div class="demo-info">
                        <div class="demo-role">مدير موردين</div>
                        <div class="demo-username">emad.supplier</div>
                    </div>
                    <button class="demo-use-btn">استخدام</button>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/users.js"></script>
    <script src="js/utils.js"></script>
    
    <script>
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة النظام
            if (typeof initializeSystem === 'function') {
                initializeSystem();
            }
            
            // التحقق من وجود جلسة نشطة
            const currentUser = getCurrentUser();
            if (currentUser) {
                redirectToHomePage();
                return;
            }
            
            // ربط نموذج تسجيل الدخول
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', handleLogin);
            
            // التركيز على حقل اسم المستخدم
            document.getElementById('username').focus();
        });

        // معالج تسجيل الدخول
        async function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            // التحقق من صحة البيانات
            if (!username || !password) {
                showError('يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            // إظهار مؤشر التحميل
            showLoading(true);
            hideMessages();
            
            try {
                // محاولة تسجيل الدخول
                const result = await login(username, password, rememberMe);
                
                if (result.success) {
                    showSuccess('تم تسجيل الدخول بنجاح! جاري التوجيه...');
                    
                    // تأخير قصير قبل التوجيه
                    setTimeout(() => {
                        redirectToHomePage();
                    }, 1500);
                } else {
                    showError(result.message || 'فشل في تسجيل الدخول');
                }
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
            } finally {
                showLoading(false);
            }
        }

        // إظهار/إخفاء مؤشر التحميل
        function showLoading(show) {
            const loginBtn = document.getElementById('loginBtn');
            const btnText = document.getElementById('btnText');
            const btnLoading = document.getElementById('btnLoading');
            
            if (show) {
                loginBtn.disabled = true;
                btnText.classList.add('loading');
                btnLoading.classList.add('show');
            } else {
                loginBtn.disabled = false;
                btnText.classList.remove('loading');
                btnLoading.classList.remove('show');
            }
        }

        // إظهار رسالة خطأ
        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorText.textContent = message;
            errorMessage.classList.add('show');
            
            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                errorMessage.classList.remove('show');
            }, 5000);
        }

        // إظهار رسالة نجاح
        function showSuccess(message) {
            const successMessage = document.getElementById('successMessage');
            const successText = document.getElementById('successText');
            
            successText.textContent = message;
            successMessage.classList.add('show');
        }

        // إخفاء جميع الرسائل
        function hideMessages() {
            document.getElementById('errorMessage').classList.remove('show');
            document.getElementById('successMessage').classList.remove('show');
        }

        // تبديل إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }

        // استخدام حساب تجريبي
        function useDemoAccount(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            
            // تسجيل الدخول تلقائياً
            setTimeout(() => {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }, 500);
        }

        // إظهار نافذة نسيان كلمة المرور
        function showForgotPassword() {
            alert('للحصول على كلمة مرور جديدة، يرجى التواصل مع مدير النظام على:\nالهاتف: *********\nالبريد الإلكتروني: <EMAIL>');
        }

        // إعادة التوجيه إلى الصفحة الرئيسية
        function redirectToHomePage() {
            const currentUser = getCurrentUser();
            if (currentUser) {
                if (currentUser.role === 'telesales') {
                    window.location.href = 'telesales-dashboard.html';
                } else {
                    window.location.href = 'index.html';
                }
            } else {
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
