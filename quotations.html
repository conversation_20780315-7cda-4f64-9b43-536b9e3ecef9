<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-ar="عروض الأسعار - نظام إدارة العملاء والموردين" data-en="Quotations - Customer & Supplier Management System">عروض الأسعار - نظام إدارة العملاء والموردين</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-chart-line"></i>
            <span data-ar="نظام الإدارة" data-en="Management System">نظام الإدارة</span>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span data-ar="لوحة التحكم" data-en="Dashboard">لوحة التحكم</span>
            </a>
            
            <a href="customers.html" class="nav-item">
                <i class="fas fa-users"></i>
                <span data-ar="العملاء" data-en="Customers">العملاء</span>
                <span class="nav-badge" id="customersCount">0</span>
            </a>
            
            <a href="suppliers.html" class="nav-item">
                <i class="fas fa-truck"></i>
                <span data-ar="الموردين" data-en="Suppliers">الموردين</span>
                <span class="nav-badge" id="suppliersCount">0</span>
            </a>
            
            <a href="quotations.html" class="nav-item active">
                <i class="fas fa-file-invoice-dollar"></i>
                <span data-ar="عروض الأسعار" data-en="Quotations">عروض الأسعار</span>
                <span class="nav-badge" id="quotationsCount">0</span>
            </a>
            
            <a href="telesales.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span data-ar="فريق التلي سيلز" data-en="Telesales Team">فريق التلي سيلز</span>
                <span class="nav-badge" id="telesalesCount">0</span>
            </a>
            
            <a href="reports.html" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span data-ar="التقارير" data-en="Reports">التقارير</span>
            </a>
            
            <a href="activities.html" class="nav-item">
                <i class="fas fa-history"></i>
                <span data-ar="سجل الأنشطة" data-en="Activity Log">سجل الأنشطة</span>
            </a>
        </nav>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- رأس الصفحة -->
        <header class="page-header">
            <div class="header-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 data-ar="عروض الأسعار" data-en="Quotations">عروض الأسعار</h1>
            </div>
            
            <div class="header-right">
                <button class="language-toggle" onclick="toggleLanguage()">
                    <i class="fas fa-globe"></i>
                    <span id="currentLang">العربية</span>
                </button>
                
                <div class="user-menu">
                    <button class="user-menu-toggle" onclick="toggleUserMenu()">
                        <i class="fas fa-user-circle"></i>
                        <span id="userName">المستخدم</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-menu-dropdown" id="userMenuDropdown">
                        <a href="#" onclick="showProfile()">
                            <i class="fas fa-user"></i>
                            <span data-ar="الملف الشخصي" data-en="Profile">الملف الشخصي</span>
                        </a>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span data-ar="الإعدادات" data-en="Settings">الإعدادات</span>
                        </a>
                        <a href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            <span data-ar="تسجيل الخروج" data-en="Logout">تسجيل الخروج</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- إحصائيات عروض الأسعار المحسنة -->
        <div class="quotations-stats-grid">
            <div class="stat-card total-quotations">
                <div class="stat-card-header">
                    <div class="stat-icon-wrapper">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up trend-up"></i>
                        <span class="trend-value">+12%</span>
                    </div>
                </div>
                <div class="stat-card-body">
                    <h3 id="totalQuotations">0</h3>
                    <p data-ar="إجمالي العروض" data-en="Total Quotations">إجمالي العروض</p>
                    <div class="stat-subtitle">هذا الشهر</div>
                </div>
            </div>

            <div class="stat-card pending-quotations">
                <div class="stat-card-header">
                    <div class="stat-icon-wrapper">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-down trend-down"></i>
                        <span class="trend-value">-5%</span>
                    </div>
                </div>
                <div class="stat-card-body">
                    <h3 id="pendingQuotations">0</h3>
                    <p data-ar="عروض معلقة" data-en="Pending Quotations">عروض معلقة</p>
                    <div class="stat-subtitle">تحتاج متابعة</div>
                </div>
            </div>

            <div class="stat-card accepted-quotations">
                <div class="stat-card-header">
                    <div class="stat-icon-wrapper">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up trend-up"></i>
                        <span class="trend-value">+18%</span>
                    </div>
                </div>
                <div class="stat-card-body">
                    <h3 id="acceptedQuotations">0</h3>
                    <p data-ar="عروض مقبولة" data-en="Accepted Quotations">عروض مقبولة</p>
                    <div class="stat-subtitle">معدل النجاح 75%</div>
                </div>
            </div>

            <div class="stat-card total-value">
                <div class="stat-card-header">
                    <div class="stat-icon-wrapper">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up trend-up"></i>
                        <span class="trend-value">+25%</span>
                    </div>
                </div>
                <div class="stat-card-body">
                    <h3 id="totalValue">0</h3>
                    <p data-ar="القيمة الإجمالية" data-en="Total Value">القيمة الإجمالية</p>
                    <div class="stat-subtitle">الإيرادات المتوقعة</div>
                </div>
            </div>
        </div>

        <!-- مؤشرات الأداء السريعة -->
        <div class="performance-indicators">
            <div class="indicator-item">
                <div class="indicator-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="indicator-content">
                    <span class="indicator-value" id="conversionRate">75%</span>
                    <span class="indicator-label">معدل التحويل</span>
                </div>
            </div>

            <div class="indicator-item">
                <div class="indicator-icon">
                    <i class="fas fa-stopwatch"></i>
                </div>
                <div class="indicator-content">
                    <span class="indicator-value" id="avgResponseTime">2.5</span>
                    <span class="indicator-label">متوسط وقت الرد (أيام)</span>
                </div>
            </div>

            <div class="indicator-item">
                <div class="indicator-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="indicator-content">
                    <span class="indicator-value" id="avgQuoteValue">45,000</span>
                    <span class="indicator-label">متوسط قيمة العرض (ريال)</span>
                </div>
            </div>

            <div class="indicator-item">
                <div class="indicator-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="indicator-content">
                    <span class="indicator-value" id="thisMonthQuotes">12</span>
                    <span class="indicator-label">عروض هذا الشهر</span>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم المحسنة -->
        <div class="enhanced-controls-section">
            <div class="controls-header">
                <h2 class="section-title">
                    <i class="fas fa-cogs"></i>
                    إدارة عروض الأسعار
                </h2>
                <div class="quick-actions">
                    <button class="btn-primary-gradient" onclick="showAddQuotationModal()">
                        <i class="fas fa-plus-circle"></i>
                        <span data-ar="عرض سعر جديد" data-en="New Quotation">عرض سعر جديد</span>
                    </button>

                    <button class="btn-secondary-outline" onclick="showQuotationTemplates()">
                        <i class="fas fa-file-alt"></i>
                        <span data-ar="القوالب" data-en="Templates">القوالب</span>
                    </button>

                    <button class="btn-info-outline" onclick="showQuotationAnalytics()">
                        <i class="fas fa-chart-pie"></i>
                        <span data-ar="التحليلات" data-en="Analytics">التحليلات</span>
                    </button>
                </div>
            </div>

            <div class="advanced-filters">
                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-search"></i>
                        البحث السريع
                    </label>
                    <div class="search-input-wrapper">
                        <input type="text" id="quotationSearch" class="enhanced-search"
                               placeholder="ابحث برقم العرض، اسم العميل، أو محتوى العرض..."
                               onkeyup="searchQuotations()">
                        <button class="search-clear-btn" onclick="clearSearch()" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-filter"></i>
                        حالة العرض
                    </label>
                    <select id="statusFilter" class="enhanced-select" onchange="filterQuotations()">
                        <option value="">جميع الحالات</option>
                        <option value="draft">📝 مسودة</option>
                        <option value="sent">📤 مرسل</option>
                        <option value="accepted">✅ مقبول</option>
                        <option value="rejected">❌ مرفوض</option>
                        <option value="expired">⏰ منتهي الصلاحية</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-calendar"></i>
                        الفترة الزمنية
                    </label>
                    <select id="dateFilter" class="enhanced-select" onchange="filterByDate()">
                        <option value="">جميع الفترات</option>
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                        <option value="quarter">هذا الربع</option>
                        <option value="year">هذا العام</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-sort"></i>
                        ترتيب حسب
                    </label>
                    <select id="sortFilter" class="enhanced-select" onchange="sortQuotations()">
                        <option value="date_desc">الأحدث أولاً</option>
                        <option value="date_asc">الأقدم أولاً</option>
                        <option value="value_desc">القيمة (الأعلى أولاً)</option>
                        <option value="value_asc">القيمة (الأقل أولاً)</option>
                        <option value="customer_asc">اسم العميل (أ-ي)</option>
                        <option value="status">الحالة</option>
                    </select>
                </div>

                <div class="filter-actions">
                    <button class="btn-clear-filters" onclick="clearAllFilters()">
                        <i class="fas fa-eraser"></i>
                        مسح الفلاتر
                    </button>
                    <button class="btn-save-filter" onclick="saveCurrentFilter()">
                        <i class="fas fa-bookmark"></i>
                        حفظ الفلتر
                    </button>
                </div>
            </div>
        </div>

        <!-- أدوات التصدير المحسنة -->
        <div class="enhanced-export-section">
            <div class="export-header">
                <h3 class="export-title">
                    <i class="fas fa-download"></i>
                    تصدير ومشاركة البيانات
                </h3>
                <div class="export-info">
                    <span class="export-count">عدد العروض المحددة: <strong id="selectedCount">0</strong></span>
                </div>
            </div>

            <div class="export-grid">
                <div class="export-card excel-card">
                    <div class="export-card-icon">
                        <i class="fas fa-file-excel"></i>
                    </div>
                    <div class="export-card-content">
                        <h4>تصدير Excel</h4>
                        <p>ملف شامل مع جميع البيانات والتنسيق</p>
                        <button class="btn-export-card excel-btn" onclick="exportQuotationsExcel()">
                            <i class="fas fa-download"></i>
                            تحميل Excel
                        </button>
                    </div>
                </div>

                <div class="export-card pdf-card">
                    <div class="export-card-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="export-card-content">
                        <h4>تقرير PDF</h4>
                        <p>تقرير احترافي جاهز للطباعة</p>
                        <button class="btn-export-card pdf-btn" onclick="exportQuotationsPDF()">
                            <i class="fas fa-download"></i>
                            تحميل PDF
                        </button>
                    </div>
                </div>

                <div class="export-card csv-card">
                    <div class="export-card-icon">
                        <i class="fas fa-file-csv"></i>
                    </div>
                    <div class="export-card-content">
                        <h4>بيانات CSV</h4>
                        <p>ملف بيانات للتحليل والاستيراد</p>
                        <button class="btn-export-card csv-btn" onclick="exportQuotations('csv')">
                            <i class="fas fa-download"></i>
                            تحميل CSV
                        </button>
                    </div>
                </div>

                <div class="export-card print-card">
                    <div class="export-card-icon">
                        <i class="fas fa-print"></i>
                    </div>
                    <div class="export-card-content">
                        <h4>طباعة مباشرة</h4>
                        <p>طباعة التقرير مباشرة من المتصفح</p>
                        <button class="btn-export-card print-btn" onclick="printQuotations()">
                            <i class="fas fa-print"></i>
                            طباعة الآن
                        </button>
                    </div>
                </div>

                <div class="export-card email-card">
                    <div class="export-card-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="export-card-content">
                        <h4>إرسال بالبريد</h4>
                        <p>إرسال التقرير عبر البريد الإلكتروني</p>
                        <button class="btn-export-card email-btn" onclick="emailQuotationsReport()">
                            <i class="fas fa-paper-plane"></i>
                            إرسال الآن
                        </button>
                    </div>
                </div>

                <div class="export-card backup-card">
                    <div class="export-card-icon">
                        <i class="fas fa-cloud-download-alt"></i>
                    </div>
                    <div class="export-card-content">
                        <h4>نسخة احتياطية</h4>
                        <p>حفظ نسخة احتياطية كاملة</p>
                        <button class="btn-export-card backup-btn" onclick="createBackup()">
                            <i class="fas fa-save"></i>
                            إنشاء نسخة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول عروض الأسعار المحسن -->
        <div class="enhanced-table-section">
            <div class="table-header-controls">
                <div class="table-title">
                    <h3>
                        <i class="fas fa-table"></i>
                        قائمة عروض الأسعار
                    </h3>
                    <div class="table-actions">
                        <button class="btn-table-action" onclick="selectAllQuotations()" title="تحديد الكل">
                            <i class="fas fa-check-square"></i>
                        </button>
                        <button class="btn-table-action" onclick="refreshQuotations()" title="تحديث">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn-table-action" onclick="toggleTableView()" title="تغيير العرض">
                            <i class="fas fa-th-list"></i>
                        </button>
                    </div>
                </div>

                <div class="table-pagination-top">
                    <div class="pagination-info">
                        <span>عرض</span>
                        <select id="itemsPerPage" class="items-per-page" onchange="changeItemsPerPage()">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>من إجمالي <strong id="totalQuotationsCount">0</strong> عرض</span>
                    </div>
                </div>
            </div>

            <div class="enhanced-table-container">
                <table class="enhanced-quotations-table" id="quotationsTable">
                    <thead>
                        <tr>
                            <th class="checkbox-column">
                                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </th>
                            <th class="sortable" data-sort="quoteNumber">
                                <span>رقم العرض</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="customerName">
                                <span>العميل</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="quoteDate">
                                <span>تاريخ العرض</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="validUntil">
                                <span>صالح حتى</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="totalAmount">
                                <span>القيمة الإجمالية</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="status">
                                <span>الحالة</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="actions-column">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="quotationsTableBody">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </tbody>
                </table>

                <!-- حالة الجدول الفارغ -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-state-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <h3>لا توجد عروض أسعار</h3>
                    <p>ابدأ بإنشاء عرض سعر جديد لعملائك</p>
                    <button class="btn-primary-gradient" onclick="showAddQuotationModal()">
                        <i class="fas fa-plus"></i>
                        إنشاء عرض سعر جديد
                    </button>
                </div>

                <!-- مؤشر التحميل -->
                <div class="loading-state" id="loadingState" style="display: none;">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <p>جاري تحميل عروض الأسعار...</p>
                </div>
            </div>

            <!-- تذييل الجدول مع الترقيم -->
            <div class="table-footer">
                <div class="table-summary">
                    <span class="summary-text">
                        عرض <strong id="quotationsCount">0</strong> من إجمالي <strong id="totalQuotationsDisplay">0</strong> عرض سعر
                    </span>
                    <div class="selected-actions" id="selectedActions" style="display: none;">
                        <span class="selected-count">تم تحديد <strong id="selectedQuotationsCount">0</strong> عرض</span>
                        <button class="btn-bulk-action" onclick="bulkExport()">
                            <i class="fas fa-download"></i>
                            تصدير المحدد
                        </button>
                        <button class="btn-bulk-action" onclick="bulkDelete()">
                            <i class="fas fa-trash"></i>
                            حذف المحدد
                        </button>
                    </div>
                </div>

                <div class="table-pagination" id="tablePagination">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/quotations.js"></script>
    
    <script>
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من تسجيل الدخول
            if (!requireAuth()) return;
            
            // تحديث اللغة
            updateLanguage();
            
            // تحميل عروض الأسعار
            loadQuotations();
            
            // تحديث الإحصائيات
            updateQuotationStats();
            
            // تسجيل النشاط
            logActivity('quotations_view', 'عرض صفحة عروض الأسعار');
        });
    </script>
</body>
</html>
