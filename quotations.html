<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-ar="عروض الأسعار - نظام إدارة العملاء والموردين" data-en="Quotations - Customer & Supplier Management System">عروض الأسعار - نظام إدارة العملاء والموردين</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-chart-line"></i>
            <span data-ar="نظام الإدارة" data-en="Management System">نظام الإدارة</span>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span data-ar="لوحة التحكم" data-en="Dashboard">لوحة التحكم</span>
            </a>
            
            <a href="customers.html" class="nav-item">
                <i class="fas fa-users"></i>
                <span data-ar="العملاء" data-en="Customers">العملاء</span>
                <span class="nav-badge" id="customersCount">0</span>
            </a>
            
            <a href="suppliers.html" class="nav-item">
                <i class="fas fa-truck"></i>
                <span data-ar="الموردين" data-en="Suppliers">الموردين</span>
                <span class="nav-badge" id="suppliersCount">0</span>
            </a>
            
            <a href="quotations.html" class="nav-item active">
                <i class="fas fa-file-invoice-dollar"></i>
                <span data-ar="عروض الأسعار" data-en="Quotations">عروض الأسعار</span>
                <span class="nav-badge" id="quotationsCount">0</span>
            </a>
            
            <a href="telesales.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span data-ar="فريق التلي سيلز" data-en="Telesales Team">فريق التلي سيلز</span>
                <span class="nav-badge" id="telesalesCount">0</span>
            </a>
            
            <a href="reports.html" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span data-ar="التقارير" data-en="Reports">التقارير</span>
            </a>
            
            <a href="activities.html" class="nav-item">
                <i class="fas fa-history"></i>
                <span data-ar="سجل الأنشطة" data-en="Activity Log">سجل الأنشطة</span>
            </a>
        </nav>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- رأس الصفحة -->
        <header class="page-header">
            <div class="header-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 data-ar="عروض الأسعار" data-en="Quotations">عروض الأسعار</h1>
            </div>
            
            <div class="header-right">
                <button class="language-toggle" onclick="toggleLanguage()">
                    <i class="fas fa-globe"></i>
                    <span id="currentLang">العربية</span>
                </button>
                
                <div class="user-menu">
                    <button class="user-menu-toggle" onclick="toggleUserMenu()">
                        <i class="fas fa-user-circle"></i>
                        <span id="userName">المستخدم</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-menu-dropdown" id="userMenuDropdown">
                        <a href="#" onclick="showProfile()">
                            <i class="fas fa-user"></i>
                            <span data-ar="الملف الشخصي" data-en="Profile">الملف الشخصي</span>
                        </a>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span data-ar="الإعدادات" data-en="Settings">الإعدادات</span>
                        </a>
                        <a href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            <span data-ar="تسجيل الخروج" data-en="Logout">تسجيل الخروج</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- إحصائيات عروض الأسعار -->
        <div class="stats-row">
            <div class="stat-item">
                <div class="stat-icon">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalQuotations">0</h3>
                    <p data-ar="إجمالي العروض" data-en="Total Quotations">إجمالي العروض</p>
                </div>
            </div>
            
            <div class="stat-item">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3 id="pendingQuotations">0</h3>
                    <p data-ar="عروض معلقة" data-en="Pending Quotations">عروض معلقة</p>
                </div>
            </div>
            
            <div class="stat-item">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3 id="acceptedQuotations">0</h3>
                    <p data-ar="عروض مقبولة" data-en="Accepted Quotations">عروض مقبولة</p>
                </div>
            </div>
            
            <div class="stat-item">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalValue">0</h3>
                    <p data-ar="القيمة الإجمالية" data-en="Total Value">القيمة الإجمالية</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="controls-section">
            <div class="controls-left">
                <button class="btn-primary" onclick="showAddQuotationModal()">
                    <i class="fas fa-plus"></i>
                    <span data-ar="عرض سعر جديد" data-en="New Quotation">عرض سعر جديد</span>
                </button>
                
                <button class="btn-secondary" onclick="showQuotationTemplates()">
                    <i class="fas fa-file-alt"></i>
                    <span data-ar="القوالب" data-en="Templates">القوالب</span>
                </button>
            </div>
            
            <div class="controls-right">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="quotationSearch" placeholder="البحث في عروض الأسعار..." onkeyup="searchQuotations()">
                </div>
                
                <div class="filter-dropdown">
                    <select id="statusFilter" onchange="filterQuotations()">
                        <option value="">جميع الحالات</option>
                        <option value="draft">مسودة</option>
                        <option value="sent">مرسل</option>
                        <option value="accepted">مقبول</option>
                        <option value="rejected">مرفوض</option>
                        <option value="expired">منتهي الصلاحية</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- أدوات التصدير -->
        <div class="export-section">
            <div class="export-actions">
                <button class="btn-export excel-btn" onclick="exportQuotationsExcel()">
                    <i class="fas fa-file-excel"></i>
                    <span data-ar="تصدير Excel" data-en="Export Excel">تصدير Excel</span>
                </button>

                <button class="btn-export" onclick="exportQuotations('csv')">
                    <i class="fas fa-file-csv"></i>
                    <span data-ar="تصدير CSV" data-en="Export CSV">تصدير CSV</span>
                </button>

                <button class="btn-export pdf-btn" onclick="exportQuotationsPDF()">
                    <i class="fas fa-file-pdf"></i>
                    <span data-ar="تصدير PDF" data-en="Export PDF">تصدير PDF</span>
                </button>

                <button class="btn-export" onclick="printQuotations()">
                    <i class="fas fa-print"></i>
                    <span data-ar="طباعة" data-en="Print">طباعة</span>
                </button>
            </div>
        </div>

        <!-- جدول عروض الأسعار -->
        <div class="table-container">
            <table class="customers-table" id="quotationsTable">
                <thead>
                    <tr>
                        <th data-ar="رقم العرض" data-en="Quote Number">رقم العرض</th>
                        <th data-ar="العميل" data-en="Customer">العميل</th>
                        <th data-ar="تاريخ العرض" data-en="Quote Date">تاريخ العرض</th>
                        <th data-ar="صالح حتى" data-en="Valid Until">صالح حتى</th>
                        <th data-ar="القيمة الإجمالية" data-en="Total Value">القيمة الإجمالية</th>
                        <th data-ar="الحالة" data-en="Status">الحالة</th>
                        <th data-ar="الإجراءات" data-en="Actions">الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="quotationsTableBody">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- معلومات الجدول -->
        <div class="table-info">
            <span data-ar="عرض" data-en="Showing">عرض</span>
            <span id="quotationsCount">0</span>
            <span data-ar="من إجمالي" data-en="of total">من إجمالي</span>
            <span id="totalQuotationsCount">0</span>
            <span data-ar="عرض سعر" data-en="quotations">عرض سعر</span>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/quotations.js"></script>
    
    <script>
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من تسجيل الدخول
            if (!requireAuth()) return;
            
            // تحديث اللغة
            updateLanguage();
            
            // تحميل عروض الأسعار
            loadQuotations();
            
            // تحديث الإحصائيات
            updateQuotationStats();
            
            // تسجيل النشاط
            logActivity('quotations_view', 'عرض صفحة عروض الأسعار');
        });
    </script>
</body>
</html>
