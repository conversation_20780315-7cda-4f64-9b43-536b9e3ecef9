// ===== إدارة عروض الأسعار =====

let quotations = [];
let filteredQuotations = [];
let customers = [];
let quotationItems = [];
let currentQuotationId = null;

// تهيئة صفحة عروض الأسعار
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_quotations') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // تهيئة البيانات
    initializeQuotations();
    loadCustomers();
    
    // ربط الأحداث
    bindQuotationEvents();
    
    // تحديث العرض
    updateQuotationsDisplay();
    
    // تسجيل النشاط
    logActivity('quotations_view', 'عرض صفحة عروض الأسعار');
});

// تهيئة بيانات عروض الأسعار
function initializeQuotations() {
    // تحميل عروض الأسعار من التخزين المحلي
    quotations = JSON.parse(localStorage.getItem('quotations')) || [];
    
    // إضافة بيانات تجريبية إذا لم توجد بيانات
    if (quotations.length === 0) {
        quotations = [
            {
                id: generateId(),
                quoteNumber: 'Q-2024-001',
                customerId: 'customer_1',
                customerName: 'شركة التقنية المتقدمة',
                quoteDate: '2024-01-15',
                validUntil: '2024-02-15',
                status: 'sent',
                items: [
                    {
                        id: generateId(),
                        description: 'تطوير موقع إلكتروني متجاوب',
                        quantity: 1,
                        unitPrice: 15000,
                        total: 15000
                    },
                    {
                        id: generateId(),
                        description: 'تطبيق جوال iOS و Android',
                        quantity: 1,
                        unitPrice: 25000,
                        total: 25000
                    }
                ],
                subtotal: 40000,
                taxRate: 15,
                taxAmount: 6000,
                discount: 2000,
                totalAmount: 44000,
                notes: 'العرض شامل التصميم والبرمجة والاستضافة لسنة واحدة',
                terms: 'الدفع على 3 دفعات: 50% عند البدء، 30% عند التسليم الأولي، 20% عند التسليم النهائي',
                createdAt: '2024-01-15T10:00:00Z',
                createdBy: 'admin',
                updatedAt: '2024-01-15T10:00:00Z',
                updatedBy: 'admin'
            },
            {
                id: generateId(),
                quoteNumber: 'Q-2024-002',
                customerId: 'customer_2',
                customerName: 'مؤسسة الأعمال الذكية',
                quoteDate: '2024-01-18',
                validUntil: '2024-02-18',
                status: 'accepted',
                items: [
                    {
                        id: generateId(),
                        description: 'نظام إدارة المخزون',
                        quantity: 1,
                        unitPrice: 35000,
                        total: 35000
                    },
                    {
                        id: generateId(),
                        description: 'تدريب الموظفين',
                        quantity: 20,
                        unitPrice: 500,
                        total: 10000
                    }
                ],
                subtotal: 45000,
                taxRate: 15,
                taxAmount: 6750,
                discount: 0,
                totalAmount: 51750,
                notes: 'النظام يشمل إدارة المخزون والمبيعات والتقارير',
                terms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
                createdAt: '2024-01-18T14:30:00Z',
                createdBy: 'sales_manager',
                updatedAt: '2024-01-20T09:15:00Z',
                updatedBy: 'admin'
            },
            {
                id: generateId(),
                quoteNumber: 'Q-2024-003',
                customerId: 'customer_3',
                customerName: 'شركة الحلول الرقمية',
                quoteDate: '2024-01-20',
                validUntil: '2024-02-20',
                status: 'draft',
                items: [
                    {
                        id: generateId(),
                        description: 'استشارات تقنية',
                        quantity: 40,
                        unitPrice: 300,
                        total: 12000
                    }
                ],
                subtotal: 12000,
                taxRate: 15,
                taxAmount: 1800,
                discount: 500,
                totalAmount: 13300,
                notes: 'استشارات تقنية متخصصة في التحول الرقمي',
                terms: 'الدفع عند التسليم',
                createdAt: '2024-01-20T16:45:00Z',
                createdBy: 'consultant',
                updatedAt: '2024-01-20T16:45:00Z',
                updatedBy: 'consultant'
            }
        ];
        
        saveQuotations();
    }
    
    filteredQuotations = [...quotations];
}

// تحميل بيانات العملاء
function loadCustomers() {
    customers = JSON.parse(localStorage.getItem('customers')) || [];
}

// حفظ عروض الأسعار في التخزين المحلي
function saveQuotations() {
    localStorage.setItem('quotations', JSON.stringify(quotations));
}

// ربط الأحداث
function bindQuotationEvents() {
    // البحث
    const searchInput = document.getElementById('quotationSearch');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(searchQuotations, 300));
    }
    
    // الفلاتر
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', filterQuotations);
    }
}

// البحث في عروض الأسعار
function searchQuotations() {
    const searchInput = document.getElementById('quotationSearch');
    const clearBtn = document.querySelector('.search-clear-btn');
    const searchTerm = searchInput.value.trim().toLowerCase();

    // إظهار/إخفاء زر المسح
    if (clearBtn) {
        clearBtn.style.display = searchTerm ? 'block' : 'none';
    }

    if (searchTerm === '') {
        filteredQuotations = [...quotations];
    } else {
        filteredQuotations = quotations.filter(quotation =>
            quotation.quoteNumber.toLowerCase().includes(searchTerm) ||
            quotation.customerName.toLowerCase().includes(searchTerm) ||
            (quotation.notes && quotation.notes.toLowerCase().includes(searchTerm)) ||
            (quotation.items && quotation.items.some(item =>
                item.description && item.description.toLowerCase().includes(searchTerm)
            ))
        );
    }

    updateQuotationsDisplay();

    // تحديث عداد النتائج
    const resultsCount = filteredQuotations.length;
    const totalCount = quotations.length;

    if (searchTerm && resultsCount !== totalCount) {
        showNotification(`تم العثور على ${resultsCount} نتيجة من أصل ${totalCount}`, 'info');
    }
}

// فلترة عروض الأسعار
function filterQuotations() {
    const statusFilter = document.getElementById('statusFilter').value;
    
    if (statusFilter === '') {
        filteredQuotations = [...quotations];
    } else {
        filteredQuotations = quotations.filter(quotation => quotation.status === statusFilter);
    }
    
    // تطبيق البحث إذا كان موجوداً
    const searchTerm = document.getElementById('quotationSearch').value.trim();
    if (searchTerm) {
        searchQuotations();
        return;
    }
    
    updateQuotationsDisplay();
}

// تحديث عرض عروض الأسعار
function updateQuotationsDisplay() {
    updateQuotationStats();
    updateQuotationsTable();
}

// تحديث إحصائيات عروض الأسعار
function updateQuotationStats() {
    const totalQuotations = quotations.length;
    const pendingQuotations = quotations.filter(q => q.status === 'sent').length;
    const acceptedQuotations = quotations.filter(q => q.status === 'accepted').length;
    const totalValue = quotations.reduce((sum, q) => sum + (q.totalAmount || 0), 0);
    
    // تحديث العدادات
    updateCounter('totalQuotations', totalQuotations);
    updateCounter('pendingQuotations', pendingQuotations);
    updateCounter('acceptedQuotations', acceptedQuotations);
    
    const totalValueElement = document.getElementById('totalValue');
    if (totalValueElement) {
        totalValueElement.textContent = formatCurrency(totalValue);
    }
    
    // تحديث شارة التنقل
    updateNavBadge('quotationsCount', totalQuotations);
    
    // تحديث معلومات الجدول
    const quotationsCountElement = document.getElementById('quotationsCount');
    const totalQuotationsCountElement = document.getElementById('totalQuotationsCount');
    
    if (quotationsCountElement) {
        quotationsCountElement.textContent = filteredQuotations.length;
    }
    if (totalQuotationsCountElement) {
        totalQuotationsCountElement.textContent = totalQuotations;
    }
}

// تحديث جدول عروض الأسعار
function updateQuotationsTable() {
    const tbody = document.getElementById('quotationsTableBody');
    const emptyState = document.getElementById('emptyState');
    const loadingState = document.getElementById('loadingState');

    if (!tbody) return;

    // إخفاء حالات الجدول
    if (emptyState) emptyState.style.display = 'none';
    if (loadingState) loadingState.style.display = 'none';

    if (filteredQuotations.length === 0) {
        tbody.innerHTML = '';
        if (emptyState) {
            emptyState.style.display = 'block';
        }
        return;
    }

    tbody.innerHTML = filteredQuotations.map(quotation => `
        <tr>
            <td class="checkbox-column">
                <input type="checkbox" class="quotation-checkbox" value="${quotation.id}" onchange="updateSelectedActions()">
            </td>
            <td>
                <div class="quote-number-cell">
                    <strong>${quotation.quoteNumber}</strong>
                    <div class="quote-meta">
                        ${quotation.items?.length || 0} بند
                    </div>
                </div>
            </td>
            <td>
                <div class="customer-cell">
                    <strong>${quotation.customerName}</strong>
                    <div class="customer-meta">
                        ${customers.find(c => c.id === quotation.customerId)?.city || 'غير محدد'}
                    </div>
                </div>
            </td>
            <td>
                <div class="date-cell">
                    ${formatDate(quotation.quoteDate)}
                    <div class="date-meta">
                        ${getDateDifference(quotation.quoteDate)} يوم مضى
                    </div>
                </div>
            </td>
            <td>
                <div class="date-cell ${isExpiringSoon(quotation.validUntil) ? 'expiring-soon' : ''}">
                    ${formatDate(quotation.validUntil)}
                    <div class="date-meta">
                        ${getExpiryStatus(quotation.validUntil)}
                    </div>
                </div>
            </td>
            <td>
                <div class="amount-cell">
                    <strong>${formatCurrency(quotation.totalAmount)}</strong>
                    <div class="amount-meta">
                        ${quotation.taxAmount ? `ض.ق: ${formatCurrency(quotation.taxAmount)}` : 'بدون ضريبة'}
                    </div>
                </div>
            </td>
            <td>
                <span class="status-badge status-${quotation.status}">
                    <i class="fas fa-${getStatusIcon(quotation.status)}"></i>
                    ${getQuotationStatusDisplayName(quotation.status)}
                </span>
            </td>
            <td class="actions-column">
                <div class="action-buttons">
                    <button class="action-btn preview" onclick="previewQuotation('${quotation.id}')" title="معاينة العرض">
                        <i class="fas fa-file-alt"></i>
                    </button>
                    <button class="action-btn view" onclick="viewQuotation('${quotation.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${hasPermission('edit_quotations') || hasPermission('all') ? `
                        <button class="action-btn edit" onclick="editQuotation('${quotation.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : ''}
                    <button class="action-btn print" onclick="printQuotation('${quotation.id}')" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="action-btn email" onclick="emailQuotation('${quotation.id}')" title="إرسال بالبريد">
                        <i class="fas fa-envelope"></i>
                    </button>
                    ${hasPermission('all') ? `
                        <button class="action-btn delete" onclick="deleteQuotation('${quotation.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');

    // تحديث عداد العناصر المحددة
    updateSelectedCount();
}

// دوال مساعدة للجدول المحسن
function getDateDifference(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
}

function isExpiringSoon(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7 && diffDays >= 0;
}

function getExpiryStatus(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
        return 'منتهي الصلاحية';
    } else if (diffDays === 0) {
        return 'ينتهي اليوم';
    } else if (diffDays <= 7) {
        return `ينتهي خلال ${diffDays} أيام`;
    } else {
        return `باقي ${diffDays} يوم`;
    }
}

function getStatusIcon(status) {
    const icons = {
        'draft': 'edit',
        'sent': 'paper-plane',
        'accepted': 'check-circle',
        'rejected': 'times-circle',
        'expired': 'clock'
    };
    return icons[status] || 'file';
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.quotation-checkbox:checked');
    const selectedCountElement = document.getElementById('selectedCount');

    if (selectedCountElement) {
        selectedCountElement.textContent = selectedCheckboxes.length;
    }
}

// الحصول على اسم حالة العرض للعرض
function getQuotationStatusDisplayName(status) {
    const statuses = {
        'draft': 'مسودة',
        'sent': 'مرسل',
        'accepted': 'مقبول',
        'rejected': 'مرفوض',
        'expired': 'منتهي الصلاحية'
    };
    
    return statuses[status] || status;
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

// عرض نافذة إضافة عرض سعر جديد
function showAddQuotationModal() {
    if (!hasPermission('add_quotations') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لإضافة عروض أسعار جديدة');
        return;
    }

    showQuotationModal();
}

// عرض نافذة عرض السعر (إضافة أو تعديل)
function showQuotationModal(quotationId = null) {
    const isEdit = quotationId !== null;
    const quotation = isEdit ? quotations.find(q => q.id === quotationId) : null;

    // إعداد البيانات الافتراضية للعرض الجديد
    const defaultData = {
        quoteNumber: generateQuoteNumber(),
        quoteDate: new Date().toISOString().split('T')[0],
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 يوم من اليوم
        status: 'draft',
        taxRate: 15,
        discount: 0,
        items: []
    };

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content quotation-modal">
            <div class="modal-header">
                <h3>${isEdit ? 'تعديل عرض السعر' : 'إنشاء عرض سعر جديد'}</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="quotationForm" class="quotation-form">
                    <!-- معلومات العرض الأساسية -->
                    <div class="form-section">
                        <h4><i class="fas fa-info-circle"></i> معلومات العرض</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="quoteNumber">رقم العرض *</label>
                                <input type="text" id="quoteNumber" name="quoteNumber" required
                                       value="${quotation?.quoteNumber || defaultData.quoteNumber}" readonly>
                            </div>
                            <div class="form-group">
                                <label for="customerId">العميل *</label>
                                <select id="customerId" name="customerId" required onchange="updateCustomerInfo()">
                                    <option value="">اختر العميل</option>
                                    ${customers.map(customer => `
                                        <option value="${customer.id}" ${quotation?.customerId === customer.id ? 'selected' : ''}>
                                            ${customer.name}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="quoteDate">تاريخ العرض *</label>
                                <input type="date" id="quoteDate" name="quoteDate" required
                                       value="${quotation?.quoteDate || defaultData.quoteDate}">
                            </div>
                            <div class="form-group">
                                <label for="validUntil">صالح حتى *</label>
                                <input type="date" id="validUntil" name="validUntil" required
                                       value="${quotation?.validUntil || defaultData.validUntil}">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="status">حالة العرض *</label>
                                <select id="status" name="status" required>
                                    <option value="draft" ${(quotation?.status || defaultData.status) === 'draft' ? 'selected' : ''}>مسودة</option>
                                    <option value="sent" ${quotation?.status === 'sent' ? 'selected' : ''}>مرسل</option>
                                    <option value="accepted" ${quotation?.status === 'accepted' ? 'selected' : ''}>مقبول</option>
                                    <option value="rejected" ${quotation?.status === 'rejected' ? 'selected' : ''}>مرفوض</option>
                                    <option value="expired" ${quotation?.status === 'expired' ? 'selected' : ''}>منتهي الصلاحية</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- بنود العرض -->
                    <div class="form-section">
                        <h4><i class="fas fa-list"></i> بنود العرض</h4>
                        <div class="items-container">
                            <div class="items-header">
                                <button type="button" class="btn-secondary" onclick="addQuotationItem()">
                                    <i class="fas fa-plus"></i>
                                    إضافة بند
                                </button>
                            </div>
                            <div class="items-list" id="itemsList">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- الحسابات المالية -->
                    <div class="form-section">
                        <h4><i class="fas fa-calculator"></i> الحسابات المالية</h4>
                        <div class="financial-calculations">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="subtotal">المجموع الفرعي</label>
                                    <input type="number" id="subtotal" name="subtotal" readonly
                                           value="${quotation?.subtotal || 0}">
                                </div>
                                <div class="form-group">
                                    <label for="discount">الخصم</label>
                                    <input type="number" id="discount" name="discount" min="0"
                                           value="${quotation?.discount || defaultData.discount}"
                                           onchange="calculateTotals()">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="taxRate">نسبة الضريبة (%)</label>
                                    <input type="number" id="taxRate" name="taxRate" min="0" max="100"
                                           value="${quotation?.taxRate || defaultData.taxRate}"
                                           onchange="calculateTotals()">
                                </div>
                                <div class="form-group">
                                    <label for="taxAmount">مبلغ الضريبة</label>
                                    <input type="number" id="taxAmount" name="taxAmount" readonly
                                           value="${quotation?.taxAmount || 0}">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group total-amount">
                                    <label for="totalAmount">المبلغ الإجمالي</label>
                                    <input type="number" id="totalAmount" name="totalAmount" readonly
                                           value="${quotation?.totalAmount || 0}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات والشروط -->
                    <div class="form-section">
                        <h4><i class="fas fa-sticky-note"></i> الملاحظات والشروط</h4>
                        <div class="form-group">
                            <label for="notes">ملاحظات العرض</label>
                            <textarea id="notes" name="notes" rows="3"
                                      placeholder="أدخل أي ملاحظات إضافية حول العرض...">${quotation?.notes || ''}</textarea>
                        </div>
                        <div class="form-group">
                            <label for="terms">شروط وأحكام العرض</label>
                            <textarea id="terms" name="terms" rows="4"
                                      placeholder="أدخل شروط وأحكام العرض...">${quotation?.terms || 'الدفع خلال 30 يوم من تاريخ الفاتورة'}</textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn-export" onclick="previewQuotation()">
                    <i class="fas fa-eye"></i>
                    معاينة
                </button>
                <button type="button" class="btn-primary" onclick="saveQuotation('${quotationId || ''}')">
                    <i class="fas fa-save"></i>
                    ${isEdit ? 'حفظ التغييرات' : 'إنشاء العرض'}
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    updateLanguage();

    // تحميل بنود العرض إذا كان تعديل
    if (isEdit && quotation?.items) {
        quotationItems = [...quotation.items];
        updateItemsList();
        calculateTotals();
    }

    // التركيز على أول حقل
    setTimeout(() => {
        document.getElementById('customerId').focus();
    }, 100);
}

// توليد رقم عرض سعر جديد
function generateQuoteNumber() {
    const year = new Date().getFullYear();
    const existingNumbers = quotations
        .filter(q => q.quoteNumber.startsWith(`Q-${year}-`))
        .map(q => parseInt(q.quoteNumber.split('-')[2]))
        .filter(n => !isNaN(n));

    const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1;
    return `Q-${year}-${nextNumber.toString().padStart(3, '0')}`;
}

// إضافة بند جديد للعرض
function addQuotationItem() {
    const newItem = {
        id: generateId(),
        description: '',
        quantity: 1,
        unitPrice: 0,
        total: 0
    };

    quotationItems.push(newItem);
    updateItemsList();
}

// تحديث قائمة البنود
function updateItemsList() {
    const itemsList = document.getElementById('itemsList');
    if (!itemsList) return;

    if (quotationItems.length === 0) {
        itemsList.innerHTML = `
            <div class="no-items">
                <i class="fas fa-list"></i>
                <p>لا توجد بنود في العرض. انقر على "إضافة بند" لإضافة بند جديد.</p>
            </div>
        `;
        return;
    }

    itemsList.innerHTML = quotationItems.map((item, index) => `
        <div class="item-row" data-item-id="${item.id}">
            <div class="item-number">${index + 1}</div>
            <div class="item-fields">
                <div class="form-group">
                    <label>وصف البند *</label>
                    <input type="text" class="item-description" value="${item.description}"
                           placeholder="أدخل وصف البند..." onchange="updateItemDescription('${item.id}', this.value)">
                </div>
                <div class="form-group">
                    <label>الكمية *</label>
                    <input type="number" class="item-quantity" value="${item.quantity}" min="1"
                           onchange="updateItemQuantity('${item.id}', this.value)">
                </div>
                <div class="form-group">
                    <label>سعر الوحدة *</label>
                    <input type="number" class="item-unit-price" value="${item.unitPrice}" min="0" step="0.01"
                           onchange="updateItemUnitPrice('${item.id}', this.value)">
                </div>
                <div class="form-group">
                    <label>المجموع</label>
                    <input type="number" class="item-total" value="${item.total}" readonly>
                </div>
                <div class="item-actions">
                    <button type="button" class="btn-danger-small" onclick="removeQuotationItem('${item.id}')" title="حذف البند">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// تحديث وصف البند
function updateItemDescription(itemId, description) {
    const item = quotationItems.find(i => i.id === itemId);
    if (item) {
        item.description = description;
    }
}

// تحديث كمية البند
function updateItemQuantity(itemId, quantity) {
    const item = quotationItems.find(i => i.id === itemId);
    if (item) {
        item.quantity = parseFloat(quantity) || 1;
        item.total = item.quantity * item.unitPrice;
        updateItemTotal(itemId);
        calculateTotals();
    }
}

// تحديث سعر الوحدة
function updateItemUnitPrice(itemId, unitPrice) {
    const item = quotationItems.find(i => i.id === itemId);
    if (item) {
        item.unitPrice = parseFloat(unitPrice) || 0;
        item.total = item.quantity * item.unitPrice;
        updateItemTotal(itemId);
        calculateTotals();
    }
}

// تحديث مجموع البند في الواجهة
function updateItemTotal(itemId) {
    const item = quotationItems.find(i => i.id === itemId);
    if (item) {
        const totalInput = document.querySelector(`[data-item-id="${itemId}"] .item-total`);
        if (totalInput) {
            totalInput.value = item.total;
        }
    }
}

// حذف بند من العرض
function removeQuotationItem(itemId) {
    quotationItems = quotationItems.filter(i => i.id !== itemId);
    updateItemsList();
    calculateTotals();
}

// حساب المجاميع
function calculateTotals() {
    const subtotal = quotationItems.reduce((sum, item) => sum + (item.total || 0), 0);
    const discount = parseFloat(document.getElementById('discount')?.value) || 0;
    const taxRate = parseFloat(document.getElementById('taxRate')?.value) || 0;

    const discountedAmount = subtotal - discount;
    const taxAmount = (discountedAmount * taxRate) / 100;
    const totalAmount = discountedAmount + taxAmount;

    // تحديث الحقول
    const subtotalInput = document.getElementById('subtotal');
    const taxAmountInput = document.getElementById('taxAmount');
    const totalAmountInput = document.getElementById('totalAmount');

    if (subtotalInput) subtotalInput.value = subtotal;
    if (taxAmountInput) taxAmountInput.value = taxAmount;
    if (totalAmountInput) totalAmountInput.value = totalAmount;
}

// تحديث معلومات العميل
function updateCustomerInfo() {
    const customerId = document.getElementById('customerId').value;
    const customer = customers.find(c => c.id === customerId);

    // يمكن إضافة منطق لتحديث معلومات العميل في النموذج
    if (customer) {
        console.log('تم اختيار العميل:', customer.name);
    }
}

// حفظ عرض السعر
function saveQuotation(quotationId = '') {
    const form = document.getElementById('quotationForm');
    const formData = new FormData(form);

    // التحقق من صحة البيانات
    if (!validateQuotationForm(formData)) {
        return;
    }

    const quotationData = {
        quoteNumber: formData.get('quoteNumber'),
        customerId: formData.get('customerId'),
        customerName: customers.find(c => c.id === formData.get('customerId'))?.name || '',
        quoteDate: formData.get('quoteDate'),
        validUntil: formData.get('validUntil'),
        status: formData.get('status'),
        items: [...quotationItems],
        subtotal: parseFloat(formData.get('subtotal')) || 0,
        discount: parseFloat(formData.get('discount')) || 0,
        taxRate: parseFloat(formData.get('taxRate')) || 0,
        taxAmount: parseFloat(formData.get('taxAmount')) || 0,
        totalAmount: parseFloat(formData.get('totalAmount')) || 0,
        notes: formData.get('notes').trim(),
        terms: formData.get('terms').trim()
    };

    const currentUser = getCurrentUser();

    if (quotationId) {
        // تعديل عرض موجود
        const quotationIndex = quotations.findIndex(q => q.id === quotationId);
        if (quotationIndex !== -1) {
            quotations[quotationIndex] = {
                ...quotations[quotationIndex],
                ...quotationData,
                updatedAt: new Date().toISOString(),
                updatedBy: currentUser.username
            };

            logActivity('quotation_edit', `تعديل عرض السعر: ${quotationData.quoteNumber}`);
            showNotification('تم تحديث عرض السعر بنجاح', 'success');
        }
    } else {
        // إضافة عرض جديد
        const newQuotation = {
            id: generateId(),
            ...quotationData,
            createdAt: new Date().toISOString(),
            createdBy: currentUser.username
        };

        quotations.push(newQuotation);
        logActivity('quotation_add', `إنشاء عرض سعر جديد: ${quotationData.quoteNumber}`);
        showNotification('تم إنشاء عرض السعر بنجاح', 'success');
    }

    saveQuotations();
    filteredQuotations = [...quotations];
    updateQuotationsDisplay();

    // إعادة تعيين بنود العرض
    quotationItems = [];

    // إغلاق النافذة المنبثقة
    document.querySelector('.modal-overlay').remove();
}

// التحقق من صحة نموذج عرض السعر
function validateQuotationForm(formData) {
    const customerId = formData.get('customerId');
    const quoteDate = formData.get('quoteDate');
    const validUntil = formData.get('validUntil');

    // التحقق من الحقول المطلوبة
    if (!customerId) {
        showNotification('يرجى اختيار العميل', 'error');
        document.getElementById('customerId').focus();
        return false;
    }

    if (!quoteDate) {
        showNotification('يرجى إدخال تاريخ العرض', 'error');
        document.getElementById('quoteDate').focus();
        return false;
    }

    if (!validUntil) {
        showNotification('يرجى إدخال تاريخ انتهاء صلاحية العرض', 'error');
        document.getElementById('validUntil').focus();
        return false;
    }

    // التحقق من التواريخ
    if (new Date(validUntil) <= new Date(quoteDate)) {
        showNotification('تاريخ انتهاء الصلاحية يجب أن يكون بعد تاريخ العرض', 'error');
        document.getElementById('validUntil').focus();
        return false;
    }

    // التحقق من وجود بنود في العرض
    if (quotationItems.length === 0) {
        showNotification('يجب إضافة بند واحد على الأقل للعرض', 'error');
        return false;
    }

    // التحقق من صحة البنود
    for (let i = 0; i < quotationItems.length; i++) {
        const item = quotationItems[i];
        if (!item.description.trim()) {
            showNotification(`يرجى إدخال وصف البند رقم ${i + 1}`, 'error');
            return false;
        }
        if (item.quantity <= 0) {
            showNotification(`كمية البند رقم ${i + 1} يجب أن تكون أكبر من صفر`, 'error');
            return false;
        }
        if (item.unitPrice < 0) {
            showNotification(`سعر الوحدة للبند رقم ${i + 1} لا يمكن أن يكون سالباً`, 'error');
            return false;
        }
    }

    return true;
}

// معاينة عرض السعر
function previewQuotation(quotationId) {
    const quotation = quotations.find(q => q.id === quotationId);
    if (!quotation) {
        showNotification('عرض السعر غير موجود', 'error');
        return;
    }

    // الانتقال إلى صفحة المعاينة
    window.location.href = `quotation-preview.html?id=${quotation.quoteNumber}`;
}

// عرض تفاصيل عرض السعر
function viewQuotation(quotationId) {
    const quotation = quotations.find(q => q.id === quotationId);
    if (!quotation) {
        showNotification('عرض السعر غير موجود', 'error');
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content quotation-details-modal">
            <div class="modal-header">
                <h3><i class="fas fa-file-invoice-dollar"></i> تفاصيل عرض السعر</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="quotation-details">
                    <!-- رأس العرض -->
                    <div class="quotation-header">
                        <div class="quotation-info">
                            <h2>${quotation.quoteNumber}</h2>
                            <p class="customer-name">${quotation.customerName}</p>
                            <div class="quotation-dates">
                                <span><strong>تاريخ العرض:</strong> ${formatDate(quotation.quoteDate)}</span>
                                <span><strong>صالح حتى:</strong> ${formatDate(quotation.validUntil)}</span>
                            </div>
                        </div>
                        <div class="quotation-status">
                            <span class="status-badge status-${quotation.status}">
                                ${getQuotationStatusDisplayName(quotation.status)}
                            </span>
                            <div class="quotation-total">
                                <strong>${formatCurrency(quotation.totalAmount)}</strong>
                            </div>
                        </div>
                    </div>

                    <!-- بنود العرض -->
                    <div class="details-section">
                        <h4><i class="fas fa-list"></i> بنود العرض</h4>
                        <div class="items-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الوصف</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${quotation.items.map((item, index) => `
                                        <tr>
                                            <td>${index + 1}</td>
                                            <td>${item.description}</td>
                                            <td>${item.quantity}</td>
                                            <td>${formatCurrency(item.unitPrice)}</td>
                                            <td>${formatCurrency(item.total)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- الحسابات المالية -->
                    <div class="details-section">
                        <h4><i class="fas fa-calculator"></i> الحسابات المالية</h4>
                        <div class="financial-summary">
                            <div class="summary-row">
                                <span>المجموع الفرعي:</span>
                                <span>${formatCurrency(quotation.subtotal)}</span>
                            </div>
                            ${quotation.discount > 0 ? `
                                <div class="summary-row">
                                    <span>الخصم:</span>
                                    <span>-${formatCurrency(quotation.discount)}</span>
                                </div>
                            ` : ''}
                            <div class="summary-row">
                                <span>الضريبة (${quotation.taxRate}%):</span>
                                <span>${formatCurrency(quotation.taxAmount)}</span>
                            </div>
                            <div class="summary-row total">
                                <span><strong>المبلغ الإجمالي:</strong></span>
                                <span><strong>${formatCurrency(quotation.totalAmount)}</strong></span>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات والشروط -->
                    ${quotation.notes ? `
                        <div class="details-section">
                            <h4><i class="fas fa-sticky-note"></i> الملاحظات</h4>
                            <div class="notes-content">
                                <p>${quotation.notes}</p>
                            </div>
                        </div>
                    ` : ''}

                    ${quotation.terms ? `
                        <div class="details-section">
                            <h4><i class="fas fa-file-contract"></i> الشروط والأحكام</h4>
                            <div class="terms-content">
                                <p>${quotation.terms}</p>
                            </div>
                        </div>
                    ` : ''}

                    <!-- معلومات إضافية -->
                    <div class="details-section">
                        <h4><i class="fas fa-info-circle"></i> معلومات إضافية</h4>
                        <div class="additional-info">
                            <p><strong>تاريخ الإنشاء:</strong> ${formatDate(quotation.createdAt, true)}</p>
                            <p><strong>أنشئ بواسطة:</strong> ${quotation.createdBy}</p>
                            ${quotation.updatedAt ? `
                                <p><strong>آخر تحديث:</strong> ${formatDate(quotation.updatedAt, true)}</p>
                                <p><strong>حدث بواسطة:</strong> ${quotation.updatedBy}</p>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
                <button type="button" class="btn-export" onclick="printQuotation('${quotation.id}')">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
                ${hasPermission('edit_quotations') || hasPermission('all') ? `
                    <button type="button" class="btn-primary" onclick="this.closest('.modal-overlay').remove(); editQuotation('${quotation.id}')">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                ` : ''}
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    updateLanguage();
}

// تعديل عرض السعر
function editQuotation(quotationId) {
    if (!hasPermission('edit_quotations') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتعديل عروض الأسعار');
        return;
    }

    showQuotationModal(quotationId);
}

// طباعة عرض السعر
function printQuotation(quotationId) {
    // سيتم تنفيذها في الجزء التالي
    alert('طباعة عرض السعر قيد التطوير');
}

// إرسال عرض السعر بالبريد الإلكتروني
function emailQuotation(quotationId) {
    // سيتم تنفيذها في الجزء التالي
    alert('إرسال عرض السعر قيد التطوير');
}

// حذف عرض السعر
function deleteQuotation(quotationId) {
    if (!hasPermission('all')) {
        alert('ليس لديك صلاحية لحذف عروض الأسعار');
        return;
    }
    
    const quotation = quotations.find(q => q.id === quotationId);
    if (!quotation) {
        showNotification('عرض السعر غير موجود', 'error');
        return;
    }
    
    if (confirm(`هل أنت متأكد من حذف عرض السعر "${quotation.quoteNumber}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        quotations = quotations.filter(q => q.id !== quotationId);
        saveQuotations();
        filteredQuotations = [...quotations];
        updateQuotationsDisplay();
        
        logActivity('quotation_delete', `حذف عرض السعر: ${quotation.quoteNumber}`);
        showNotification('تم حذف عرض السعر بنجاح', 'success');
    }
}

// طباعة عرض السعر
function printQuotation(quotationId) {
    const quotation = quotations.find(q => q.id === quotationId);
    if (!quotation) {
        showNotification('عرض السعر غير موجود', 'error');
        return;
    }

    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>عرض السعر - ${quotation.quoteNumber}</title>
            <style>
                body { font-family: 'Arial', sans-serif; margin: 20px; color: #333; }
                .header { text-align: center; border-bottom: 2px solid #2563eb; padding-bottom: 20px; margin-bottom: 30px; }
                .company-name { font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 10px; }
                .quote-title { font-size: 20px; margin-bottom: 20px; }
                .quote-info { display: flex; justify-content: space-between; margin-bottom: 30px; }
                .quote-details, .customer-details { width: 48%; }
                .items-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
                .items-table th, .items-table td { border: 1px solid #ddd; padding: 10px; text-align: right; }
                .items-table th { background-color: #f8f9fa; font-weight: bold; }
                .financial-summary { width: 300px; margin-left: auto; }
                .summary-row { display: flex; justify-content: space-between; padding: 5px 0; }
                .total-row { border-top: 2px solid #2563eb; font-weight: bold; font-size: 18px; }
                .notes, .terms { margin-top: 30px; }
                .section-title { font-weight: bold; margin-bottom: 10px; color: #2563eb; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">شركة الحلول التقنية المتقدمة</div>
                <div class="quote-title">عرض سعر</div>
            </div>

            <div class="quote-info">
                <div class="quote-details">
                    <div><strong>رقم العرض:</strong> ${quotation.quoteNumber}</div>
                    <div><strong>تاريخ العرض:</strong> ${formatDate(quotation.quoteDate)}</div>
                    <div><strong>صالح حتى:</strong> ${formatDate(quotation.validUntil)}</div>
                    <div><strong>الحالة:</strong> ${getQuotationStatusDisplayName(quotation.status)}</div>
                </div>
                <div class="customer-details">
                    <div><strong>العميل:</strong> ${quotation.customerName}</div>
                </div>
            </div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>سعر الوحدة</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${quotation.items.map((item, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${item.description}</td>
                            <td>${item.quantity}</td>
                            <td>${formatCurrency(item.unitPrice)}</td>
                            <td>${formatCurrency(item.total)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="financial-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span>${formatCurrency(quotation.subtotal)}</span>
                </div>
                ${quotation.discount > 0 ? `
                    <div class="summary-row">
                        <span>الخصم:</span>
                        <span>-${formatCurrency(quotation.discount)}</span>
                    </div>
                ` : ''}
                <div class="summary-row">
                    <span>الضريبة (${quotation.taxRate}%):</span>
                    <span>${formatCurrency(quotation.taxAmount)}</span>
                </div>
                <div class="summary-row total-row">
                    <span>المبلغ الإجمالي:</span>
                    <span>${formatCurrency(quotation.totalAmount)}</span>
                </div>
            </div>

            ${quotation.notes ? `
                <div class="notes">
                    <div class="section-title">الملاحظات:</div>
                    <p>${quotation.notes}</p>
                </div>
            ` : ''}

            ${quotation.terms ? `
                <div class="terms">
                    <div class="section-title">الشروط والأحكام:</div>
                    <p>${quotation.terms}</p>
                </div>
            ` : ''}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
}

// إرسال عرض السعر بالبريد الإلكتروني
function emailQuotation(quotationId) {
    const quotation = quotations.find(q => q.id === quotationId);
    if (!quotation) {
        showNotification('عرض السعر غير موجود', 'error');
        return;
    }

    const customer = customers.find(c => c.id === quotation.customerId);
    if (!customer || !customer.email) {
        showNotification('لا يوجد بريد إلكتروني للعميل', 'error');
        return;
    }

    // إنشاء رابط mailto
    const subject = `عرض سعر رقم ${quotation.quoteNumber}`;
    const body = `السلام عليكم ورحمة الله وبركاته،

نتشرف بإرسال عرض السعر رقم ${quotation.quoteNumber} بتاريخ ${formatDate(quotation.quoteDate)}.

تفاصيل العرض:
- العميل: ${quotation.customerName}
- المبلغ الإجمالي: ${formatCurrency(quotation.totalAmount)}
- صالح حتى: ${formatDate(quotation.validUntil)}

يرجى مراجعة العرض والرد علينا في أقرب وقت ممكن.

شكراً لكم،
فريق المبيعات`;

    const mailtoLink = `mailto:${customer.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoLink;

    logActivity('quotation_email', `إرسال عرض السعر ${quotation.quoteNumber} بالبريد الإلكتروني`);
}

// تصدير عروض الأسعار إلى Excel
async function exportQuotationsExcel() {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }

    if (filteredQuotations.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    // إظهار مؤشر التحميل
    const exportBtn = document.querySelector('.excel-btn');
    const originalContent = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري التصدير...</span>';
    exportBtn.disabled = true;

    try {
        const headers = [
            { key: 'quoteNumber', label: 'رقم العرض' },
            { key: 'customerName', label: 'العميل' },
            { key: 'quoteDate', label: 'تاريخ العرض' },
            { key: 'validUntil', label: 'صالح حتى' },
            { key: 'status', label: 'الحالة' },
            { key: 'subtotal', label: 'المجموع الفرعي' },
            { key: 'discount', label: 'الخصم' },
            { key: 'taxAmount', label: 'الضريبة' },
            { key: 'totalAmount', label: 'المبلغ الإجمالي' },
            { key: 'itemsCount', label: 'عدد البنود' },
            { key: 'notes', label: 'الملاحظات' }
        ];

        // تحضير البيانات مع تنسيق
        const formattedQuotations = filteredQuotations.map(quotation => ({
            ...quotation,
            quoteDate: formatDate(quotation.quoteDate),
            validUntil: formatDate(quotation.validUntil),
            status: getQuotationStatusDisplayName(quotation.status),
            subtotal: quotation.subtotal || 0,
            discount: quotation.discount || 0,
            taxAmount: quotation.taxAmount || 0,
            totalAmount: quotation.totalAmount || 0,
            itemsCount: quotation.items?.length || 0,
            notes: quotation.notes || 'لا توجد ملاحظات'
        }));

        const success = await exportToExcel(
            formattedQuotations,
            'عروض_الأسعار',
            headers,
            {
                sheetName: 'عروض الأسعار',
                title: 'تقرير عروض الأسعار'
            }
        );

        if (success) {
            showNotification('تم تصدير عروض الأسعار بنجاح', 'success');
        }

    } catch (error) {
        console.error('خطأ في تصدير Excel:', error);
        showNotification('حدث خطأ أثناء تصدير البيانات', 'error');
    } finally {
        // إعادة تعيين الزر
        exportBtn.innerHTML = originalContent;
        exportBtn.disabled = false;
    }
}

// تصدير عروض الأسعار إلى PDF
async function exportQuotationsPDF() {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }

    if (filteredQuotations.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    // إظهار مؤشر التحميل
    const exportBtn = document.querySelector('.pdf-btn');
    const originalContent = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري التصدير...</span>';
    exportBtn.disabled = true;

    try {
        const headers = [
            { key: 'quoteNumber', label: 'رقم العرض' },
            { key: 'customerName', label: 'العميل' },
            { key: 'quoteDate', label: 'التاريخ' },
            { key: 'status', label: 'الحالة' },
            { key: 'totalAmount', label: 'المبلغ الإجمالي' }
        ];

        // تحضير البيانات مع تنسيق مبسط للـ PDF
        const formattedQuotations = filteredQuotations.map(quotation => ({
            ...quotation,
            quoteDate: formatDate(quotation.quoteDate),
            status: getQuotationStatusDisplayName(quotation.status),
            totalAmount: formatCurrency(quotation.totalAmount)
        }));

        const success = await exportToPDF(
            formattedQuotations,
            'تقرير_عروض_الأسعار',
            headers,
            {
                title: 'تقرير عروض الأسعار'
            }
        );

        if (success) {
            showNotification('تم تصدير تقرير عروض الأسعار بنجاح', 'success');
        }

    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        showNotification('حدث خطأ أثناء تصدير التقرير', 'error');
    } finally {
        // إعادة تعيين الزر
        exportBtn.innerHTML = originalContent;
        exportBtn.disabled = false;
    }
}

// تصدير عروض الأسعار
function exportQuotations(format) {
    if (format === 'csv') {
        exportQuotationsCSV();
    } else if (format === 'json') {
        exportQuotationsJSON();
    }
}

// تصدير CSV
function exportQuotationsCSV() {
    if (filteredQuotations.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    const headers = ['رقم العرض', 'العميل', 'تاريخ العرض', 'صالح حتى', 'الحالة', 'المبلغ الإجمالي'];
    const csvContent = [
        headers.join(','),
        ...filteredQuotations.map(q => [
            q.quoteNumber,
            q.customerName,
            formatDate(q.quoteDate),
            formatDate(q.validUntil),
            getQuotationStatusDisplayName(q.status),
            q.totalAmount
        ].join(','))
    ].join('\n');

    downloadFile(csvContent, 'عروض_الأسعار.csv', 'text/csv');
    showNotification('تم تصدير ملف CSV بنجاح', 'success');
}

// تصدير JSON
function exportQuotationsJSON() {
    if (filteredQuotations.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    const jsonContent = JSON.stringify(filteredQuotations, null, 2);
    downloadFile(jsonContent, 'عروض_الأسعار.json', 'application/json');
    showNotification('تم تصدير ملف JSON بنجاح', 'success');
}

// طباعة عروض الأسعار
function printQuotations() {
    if (filteredQuotations.length === 0) {
        alert('لا توجد بيانات للطباعة');
        return;
    }

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير عروض الأسعار</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { text-align: center; color: #2563eb; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f8f9fa; }
                .status-active { color: #10b981; }
                .status-inactive { color: #ef4444; }
            </style>
        </head>
        <body>
            <h1>تقرير عروض الأسعار</h1>
            <table>
                <thead>
                    <tr>
                        <th>رقم العرض</th>
                        <th>العميل</th>
                        <th>تاريخ العرض</th>
                        <th>صالح حتى</th>
                        <th>الحالة</th>
                        <th>المبلغ الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${filteredQuotations.map(quotation => `
                        <tr>
                            <td>${quotation.quoteNumber}</td>
                            <td>${quotation.customerName}</td>
                            <td>${formatDate(quotation.quoteDate)}</td>
                            <td>${formatDate(quotation.validUntil)}</td>
                            <td>${getQuotationStatusDisplayName(quotation.status)}</td>
                            <td>${formatCurrency(quotation.totalAmount)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
}

// عرض القوالب
function showQuotationTemplates() {
    showNotification('قوالب عروض الأسعار قيد التطوير', 'info');
}

// دوال الواجهة المحسنة

// مسح البحث
function clearSearch() {
    const searchInput = document.getElementById('quotationSearch');
    const clearBtn = document.querySelector('.search-clear-btn');

    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }

    if (clearBtn) {
        clearBtn.style.display = 'none';
    }

    // إعادة تعيين النتائج
    filteredQuotations = [...quotations];
    updateQuotationsDisplay();
}

// فلترة حسب التاريخ
function filterByDate() {
    const dateFilter = document.getElementById('dateFilter').value;
    const now = new Date();
    let startDate = null;

    switch (dateFilter) {
        case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
        case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
        case 'quarter':
            const quarter = Math.floor(now.getMonth() / 3);
            startDate = new Date(now.getFullYear(), quarter * 3, 1);
            break;
        case 'year':
            startDate = new Date(now.getFullYear(), 0, 1);
            break;
        default:
            filteredQuotations = [...quotations];
            updateQuotationsDisplay();
            return;
    }

    if (startDate) {
        filteredQuotations = quotations.filter(quotation => {
            const quoteDate = new Date(quotation.quoteDate);
            return quoteDate >= startDate;
        });
        updateQuotationsDisplay();
    }
}

// ترتيب عروض الأسعار
function sortQuotations() {
    const sortFilter = document.getElementById('sortFilter').value;

    switch (sortFilter) {
        case 'date_desc':
            filteredQuotations.sort((a, b) => new Date(b.quoteDate) - new Date(a.quoteDate));
            break;
        case 'date_asc':
            filteredQuotations.sort((a, b) => new Date(a.quoteDate) - new Date(b.quoteDate));
            break;
        case 'value_desc':
            filteredQuotations.sort((a, b) => (b.totalAmount || 0) - (a.totalAmount || 0));
            break;
        case 'value_asc':
            filteredQuotations.sort((a, b) => (a.totalAmount || 0) - (b.totalAmount || 0));
            break;
        case 'customer_asc':
            filteredQuotations.sort((a, b) => a.customerName.localeCompare(b.customerName, 'ar'));
            break;
        case 'status':
            const statusOrder = { 'draft': 1, 'sent': 2, 'accepted': 3, 'rejected': 4, 'expired': 5 };
            filteredQuotations.sort((a, b) => statusOrder[a.status] - statusOrder[b.status]);
            break;
    }

    updateQuotationsDisplay();
}

// مسح جميع الفلاتر
function clearAllFilters() {
    document.getElementById('quotationSearch').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFilter').value = '';
    document.getElementById('sortFilter').value = 'date_desc';

    const clearBtn = document.querySelector('.search-clear-btn');
    if (clearBtn) {
        clearBtn.style.display = 'none';
    }

    filteredQuotations = [...quotations];
    updateQuotationsDisplay();

    showNotification('تم مسح جميع الفلاتر', 'info');
}

// حفظ الفلتر الحالي
function saveCurrentFilter() {
    const filterData = {
        search: document.getElementById('quotationSearch').value,
        status: document.getElementById('statusFilter').value,
        date: document.getElementById('dateFilter').value,
        sort: document.getElementById('sortFilter').value
    };

    localStorage.setItem('savedQuotationFilter', JSON.stringify(filterData));
    showNotification('تم حفظ الفلتر بنجاح', 'success');
}

// تحديد جميع عروض الأسعار
function selectAllQuotations() {
    const checkboxes = document.querySelectorAll('.quotation-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(checkbox => {
        checkbox.checked = !allChecked;
    });

    selectAllCheckbox.checked = !allChecked;
    updateSelectedActions();
}

// تبديل تحديد الكل
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.quotation-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedActions();
}

// تحديث إجراءات المحدد
function updateSelectedActions() {
    const checkboxes = document.querySelectorAll('.quotation-checkbox:checked');
    const selectedActions = document.getElementById('selectedActions');
    const selectedCount = document.getElementById('selectedQuotationsCount');

    if (checkboxes.length > 0) {
        selectedActions.style.display = 'flex';
        selectedCount.textContent = checkboxes.length;
    } else {
        selectedActions.style.display = 'none';
    }
}

// تحديث عروض الأسعار
function refreshQuotations() {
    const refreshBtn = document.querySelector('.btn-table-action i.fa-sync-alt');
    if (refreshBtn) {
        refreshBtn.classList.add('fa-spin');
    }

    // محاكاة التحديث
    setTimeout(() => {
        updateQuotationsDisplay();
        if (refreshBtn) {
            refreshBtn.classList.remove('fa-spin');
        }
        showNotification('تم تحديث البيانات', 'success');
    }, 1000);
}

// تغيير عرض الجدول
function toggleTableView() {
    const table = document.getElementById('quotationsTable');
    const isCompact = table.classList.contains('compact-view');

    if (isCompact) {
        table.classList.remove('compact-view');
        showNotification('تم التبديل إلى العرض العادي', 'info');
    } else {
        table.classList.add('compact-view');
        showNotification('تم التبديل إلى العرض المضغوط', 'info');
    }
}

// تغيير عدد العناصر في الصفحة
function changeItemsPerPage() {
    const itemsPerPage = document.getElementById('itemsPerPage').value;
    // هنا يمكن تطبيق منطق الترقيم
    showNotification(`تم تغيير العرض إلى ${itemsPerPage} عنصر في الصفحة`, 'info');
}

// تصدير المحدد
function bulkExport() {
    const checkboxes = document.querySelectorAll('.quotation-checkbox:checked');
    if (checkboxes.length === 0) {
        showNotification('يرجى تحديد عروض أسعار للتصدير', 'warning');
        return;
    }

    showNotification(`جاري تصدير ${checkboxes.length} عرض سعر...`, 'info');
    // منطق التصدير هنا
}

// حذف المحدد
function bulkDelete() {
    const checkboxes = document.querySelectorAll('.quotation-checkbox:checked');
    if (checkboxes.length === 0) {
        showNotification('يرجى تحديد عروض أسعار للحذف', 'warning');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${checkboxes.length} عرض سعر؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // منطق الحذف هنا
        showNotification(`تم حذف ${checkboxes.length} عرض سعر`, 'success');
        updateSelectedActions();
    }
}

// إرسال تقرير بالبريد الإلكتروني
function emailQuotationsReport() {
    showNotification('جاري إعداد التقرير للإرسال...', 'info');
    // منطق إرسال التقرير
}

// إنشاء نسخة احتياطية
function createBackup() {
    const backupData = {
        quotations: quotations,
        timestamp: new Date().toISOString(),
        version: '1.0'
    };

    const dataStr = JSON.stringify(backupData, null, 2);
    downloadFile(dataStr, `backup_quotations_${new Date().toISOString().split('T')[0]}.json`, 'application/json');

    showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
}

// عرض التحليلات
function showQuotationAnalytics() {
    showNotification('نافذة التحليلات قيد التطوير', 'info');
}

// عرض الإشعارات (إذا لم تكن موجودة)
function showNotification(message, type = 'info') {
    // التحقق من وجود الدالة في dashboard.js
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }

    // إنشاء إشعار بسيط
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#2563eb'};
    `;

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"
               style="color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#2563eb'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}
