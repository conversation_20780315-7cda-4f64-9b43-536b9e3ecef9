<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المرحلة الثانية - النوافذ المنبثقة والوظائف المعلقة</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div style="padding: 20px; max-width: 1200px; margin: 0 auto;">
        <h1 style="text-align: center; color: var(--primary-blue); margin-bottom: 30px;">
            <i class="fas fa-cogs"></i>
            اختبار المرحلة الثانية - النوافذ المنبثقة والوظائف المعلقة
        </h1>
        
        <!-- قسم اختبار نوافذ الموردين -->
        <div class="test-section">
            <h2><i class="fas fa-truck"></i> اختبار نوافذ الموردين</h2>
            <div class="test-buttons">
                <button class="btn-primary" onclick="testAddSupplier()">
                    <i class="fas fa-plus"></i>
                    إضافة مورد جديد
                </button>
                <button class="btn-secondary" onclick="testViewSupplier()">
                    <i class="fas fa-eye"></i>
                    عرض تفاصيل مورد
                </button>
                <button class="btn-export" onclick="testEditSupplier()">
                    <i class="fas fa-edit"></i>
                    تعديل مورد
                </button>
                <button class="btn-export excel-btn" onclick="testExportSuppliersExcel()">
                    <i class="fas fa-file-excel"></i>
                    تصدير Excel
                </button>
            </div>
            <div class="test-description">
                <p><strong>الميزات الجديدة للموردين:</strong></p>
                <ul>
                    <li>نافذة إضافة مورد شاملة مع جميع الحقول المطلوبة</li>
                    <li>تقييم المورد بنظام النجوم (1-5)</li>
                    <li>وسائل تواصل متعددة (هاتف، بريد، واتساب، موقع)</li>
                    <li>عنوان تفصيلي واختيار المدينة</li>
                    <li>حالة التعاون (نشط، غير نشط، قيد المراجعة، معلق)</li>
                    <li>نافذة عرض تفاصيل احترافية مع روابط التواصل</li>
                    <li>تصدير Excel و PDF مع تنسيق متقدم</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار نوافذ الموظفين -->
        <div class="test-section">
            <h2><i class="fas fa-users"></i> اختبار نوافذ الموظفين</h2>
            <div class="test-buttons">
                <button class="btn-primary" onclick="testAddEmployee()">
                    <i class="fas fa-user-plus"></i>
                    إضافة موظف جديد
                </button>
                <button class="btn-secondary" onclick="testViewEmployee()">
                    <i class="fas fa-id-card"></i>
                    عرض تفاصيل موظف
                </button>
                <button class="btn-export" onclick="testEditEmployee()">
                    <i class="fas fa-user-edit"></i>
                    تعديل موظف
                </button>
                <button class="btn-export pdf-btn" onclick="testExportEmployeesPDF()">
                    <i class="fas fa-file-pdf"></i>
                    تصدير PDF
                </button>
            </div>
            <div class="test-description">
                <p><strong>الميزات الجديدة للموظفين:</strong></p>
                <ul>
                    <li>نموذج شامل للبيانات الشخصية (الاسم، تاريخ الميلاد، التواصل)</li>
                    <li>بيانات العمل (القسم، المسمى الوظيفي، تاريخ الانضمام، الراتب)</li>
                    <li>معلومات الأداء (الهدف الشهري، نسبة الإنجاز، التقييم)</li>
                    <li>رفع صورة الموظف مع معاينة</li>
                    <li>حالة الموظف (نشط، غير نشط، في إجازة، معلق)</li>
                    <li>نافذة تفاصيل مع إحصائيات الأداء</li>
                    <li>تصدير متقدم مع جميع البيانات</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار التحقق من البيانات -->
        <div class="test-section">
            <h2><i class="fas fa-shield-alt"></i> اختبار التحقق من البيانات</h2>
            <div class="test-buttons">
                <button class="btn-secondary" onclick="testValidation()">
                    <i class="fas fa-check-circle"></i>
                    اختبار التحقق من الصحة
                </button>
                <button class="btn-export" onclick="testNotifications()">
                    <i class="fas fa-bell"></i>
                    اختبار الإشعارات
                </button>
                <button class="btn-primary" onclick="testPermissions()">
                    <i class="fas fa-lock"></i>
                    اختبار الصلاحيات
                </button>
            </div>
            <div class="test-description">
                <p><strong>ميزات التحقق والأمان:</strong></p>
                <ul>
                    <li>تحقق شامل من جميع الحقول المطلوبة</li>
                    <li>التحقق من صحة البريد الإلكتروني وأرقام الهواتف</li>
                    <li>رسائل خطأ واضحة باللغتين العربية والإنجليزية</li>
                    <li>منع إرسال النماذج الفارغة أو غير الصحيحة</li>
                    <li>مؤشرات بصرية للحقول المطلوبة (*)</li>
                    <li>نظام صلاحيات متدرج حسب دور المستخدم</li>
                </ul>
            </div>
        </div>

        <!-- قسم معاينة النماذج -->
        <div class="test-section">
            <h2><i class="fas fa-eye"></i> معاينة تصميم النماذج</h2>
            
            <!-- نموذج تجريبي للمورد -->
            <div class="form-preview">
                <h4>نموذج المورد</h4>
                <div class="form-section">
                    <h4><i class="fas fa-info-circle"></i> المعلومات الأساسية</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم المورد *</label>
                            <input type="text" placeholder="شركة التقنية المتقدمة" readonly>
                        </div>
                        <div class="form-group">
                            <label>نوع الخدمة *</label>
                            <select disabled>
                                <option>خدمات تقنية</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4><i class="fas fa-star"></i> التقييم</h4>
                    <div class="rating-input">
                        <input type="radio" id="star1" name="rating" value="1">
                        <label for="star1" class="star-label"><i class="fas fa-star"></i></label>
                        <input type="radio" id="star2" name="rating" value="2">
                        <label for="star2" class="star-label"><i class="fas fa-star"></i></label>
                        <input type="radio" id="star3" name="rating" value="3">
                        <label for="star3" class="star-label"><i class="fas fa-star"></i></label>
                        <input type="radio" id="star4" name="rating" value="4" checked>
                        <label for="star4" class="star-label"><i class="fas fa-star"></i></label>
                        <input type="radio" id="star5" name="rating" value="5">
                        <label for="star5" class="star-label"><i class="fas fa-star"></i></label>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط التنقل -->
        <div class="test-section">
            <h2><i class="fas fa-link"></i> روابط التنقل</h2>
            <div class="test-buttons">
                <a href="suppliers.html" class="btn-primary">
                    <i class="fas fa-truck"></i>
                    صفحة الموردين
                </a>
                <a href="telesales.html" class="btn-secondary">
                    <i class="fas fa-users"></i>
                    صفحة الموظفين
                </a>
                <a href="test-phase1.html" class="btn-export">
                    <i class="fas fa-arrow-left"></i>
                    المرحلة الأولى
                </a>
                <a href="index.html" class="btn-export">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/suppliers.js"></script>
    <script src="js/telesales.js"></script>

    <script>
        // إعداد مستخدم تجريبي
        if (!localStorage.getItem('currentUser')) {
            const testUser = {
                username: 'test_admin',
                name: 'أحمد محمد المدير',
                role: 'admin',
                email: '<EMAIL>',
                phone: '0501234567',
                jobTitle: 'مدير النظام',
                loginTime: new Date().toISOString(),
                joinDate: '2024-01-01',
                avatar: null
            };
            localStorage.setItem('currentUser', JSON.stringify(testUser));
        }

        // اختبار إضافة مورد
        function testAddSupplier() {
            if (typeof showAddSupplierModal === 'function') {
                showAddSupplierModal();
            } else {
                alert('يجب تحميل suppliers.js أولاً');
            }
        }

        // اختبار عرض تفاصيل مورد
        function testViewSupplier() {
            // إنشاء مورد تجريبي إذا لم يوجد
            if (!localStorage.getItem('suppliers')) {
                const testSupplier = {
                    id: 'supplier_test_1',
                    name: 'شركة التقنية المتقدمة',
                    serviceType: 'خدمات تقنية',
                    phone: '0112345678',
                    email: '<EMAIL>',
                    whatsapp: '0501234567',
                    website: 'www.techadvanced.com',
                    address: 'شارع الملك فهد، حي العليا',
                    city: 'الرياض',
                    rating: 5,
                    status: 'active',
                    notes: 'شركة متخصصة في تطوير الأنظمة المتقدمة',
                    createdAt: '2024-01-10',
                    createdBy: 'admin'
                };
                localStorage.setItem('suppliers', JSON.stringify([testSupplier]));
            }
            
            if (typeof viewSupplier === 'function') {
                viewSupplier('supplier_test_1');
            } else {
                alert('يجب تحميل suppliers.js أولاً');
            }
        }

        // اختبار تعديل مورد
        function testEditSupplier() {
            if (typeof editSupplier === 'function') {
                editSupplier('supplier_test_1');
            } else {
                alert('يجب تحميل suppliers.js أولاً');
            }
        }

        // اختبار تصدير Excel للموردين
        function testExportSuppliersExcel() {
            if (typeof exportSuppliersExcel === 'function') {
                exportSuppliersExcel();
            } else {
                alert('يجب تحميل suppliers.js أولاً');
            }
        }

        // اختبار إضافة موظف
        function testAddEmployee() {
            if (typeof showAddEmployeeModal === 'function') {
                showAddEmployeeModal();
            } else {
                alert('يجب تحميل telesales.js أولاً');
            }
        }

        // اختبار عرض تفاصيل موظف
        function testViewEmployee() {
            // إنشاء موظف تجريبي إذا لم يوجد
            if (!localStorage.getItem('employees')) {
                const testEmployee = {
                    id: 'employee_test_1',
                    name: 'سارة أحمد محمد',
                    birthDate: '1995-05-15',
                    phone: '0501234567',
                    email: '<EMAIL>',
                    department: 'telesales',
                    jobTitle: 'أخصائي تلي سيلز',
                    joinDate: '2024-01-15',
                    salary: 8000,
                    monthlyTarget: 100,
                    achievementRate: 85,
                    performance: 'excellent',
                    performanceScore: 92,
                    status: 'active',
                    notes: 'موظفة متميزة في الأداء',
                    totalCalls: 450,
                    successfulCalls: 380,
                    createdAt: '2024-01-15',
                    createdBy: 'admin'
                };
                localStorage.setItem('employees', JSON.stringify([testEmployee]));
            }
            
            if (typeof viewEmployee === 'function') {
                viewEmployee('employee_test_1');
            } else {
                alert('يجب تحميل telesales.js أولاً');
            }
        }

        // اختبار تعديل موظف
        function testEditEmployee() {
            if (typeof editEmployee === 'function') {
                editEmployee('employee_test_1');
            } else {
                alert('يجب تحميل telesales.js أولاً');
            }
        }

        // اختبار تصدير PDF للموظفين
        function testExportEmployeesPDF() {
            if (typeof exportEmployeesPDF === 'function') {
                exportEmployeesPDF();
            } else {
                alert('يجب تحميل telesales.js أولاً');
            }
        }

        // اختبار التحقق من البيانات
        function testValidation() {
            showNotification('اختبار التحقق من البيانات - جميع الحقول المطلوبة محمية', 'info');
            setTimeout(() => showNotification('البريد الإلكتروني وأرقام الهواتف يتم التحقق منها تلقائياً', 'success'), 1500);
        }

        // اختبار الإشعارات
        function testNotifications() {
            showNotification('إشعار نجاح - تم الحفظ بنجاح!', 'success');
            setTimeout(() => showNotification('إشعار خطأ - يرجى التحقق من البيانات', 'error'), 1000);
            setTimeout(() => showNotification('إشعار تحذير - تأكد من صحة المعلومات', 'warning'), 2000);
            setTimeout(() => showNotification('إشعار معلومات - تم تحديث النظام', 'info'), 3000);
        }

        // اختبار الصلاحيات
        function testPermissions() {
            const currentUser = JSON.parse(localStorage.getItem('currentUser'));
            showNotification(`المستخدم الحالي: ${currentUser.name} - الدور: ${currentUser.role}`, 'info');
            setTimeout(() => showNotification('جميع الوظائف محمية بنظام صلاحيات متدرج', 'success'), 1500);
        }
    </script>

    <style>
        .test-section {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 8px var(--shadow);
            border-left: 4px solid var(--primary-blue);
        }

        .test-section h2 {
            color: var(--primary-blue);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .test-description {
            background: var(--light-gray);
            padding: 20px;
            border-radius: var(--border-radius);
            border-right: 4px solid var(--secondary-blue);
        }

        .test-description ul {
            margin: 10px 0;
            padding-right: 20px;
        }

        .test-description li {
            margin-bottom: 8px;
            color: var(--dark-gray);
        }

        .form-preview {
            background: var(--white);
            border: 2px solid #e2e8f0;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 20px;
        }

        .form-preview h4 {
            color: var(--primary-blue);
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .test-buttons {
                flex-direction: column;
            }
            
            .test-buttons .btn-primary,
            .test-buttons .btn-secondary,
            .test-buttons .btn-export {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</body>
</html>
