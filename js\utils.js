// ===== الوظائف المساعدة =====

// الحصول على المستخدم الحالي
function getCurrentUser() {
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        return JSON.parse(currentUser);
    }

    // إرجاع مستخدم افتراضي إذا لم يتم العثور على مستخدم
    return {
        id: 'admin',
        username: 'admin',
        name: 'المدير العام',
        role: 'admin'
    };
}

// تنسيق العملة السعودية الموحد
function formatCurrency(amount, showCurrency = true) {
    if (amount === null || amount === undefined || isNaN(amount)) {
        amount = 0;
    }

    const formattedNumber = new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);

    return showCurrency ? `${formattedNumber} ريال سعودي` : formattedNumber;
}

// تنسيق العملة المختصر
function formatCurrencyShort(amount) {
    if (amount === null || amount === undefined || isNaN(amount)) {
        amount = 0;
    }

    const formattedNumber = new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);

    return `${formattedNumber} ر.س`;
}

// تنسيق العملة للعرض في الجداول
function formatCurrencyTable(amount) {
    if (amount === null || amount === undefined || isNaN(amount)) {
        amount = 0;
    }

    const formattedNumber = new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);

    return `${formattedNumber} ر.س`;
}

// إعدادات اللغة
let currentLanguage = localStorage.getItem('language') || 'ar';

// تبديل اللغة
function toggleLanguage() {
    currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
    localStorage.setItem('language', currentLanguage);
    updateLanguage();
}

// تحديث النصوص حسب اللغة
function updateLanguage() {
    const elements = document.querySelectorAll('[data-ar][data-en]');
    const html = document.documentElement;
    const langText = document.getElementById('langText');
    
    elements.forEach(element => {
        if (currentLanguage === 'ar') {
            element.textContent = element.getAttribute('data-ar');
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            if (langText) langText.textContent = 'English';
        } else {
            element.textContent = element.getAttribute('data-en');
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            if (langText) langText.textContent = 'العربية';
        }
    });
}

// تهيئة اللغة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateLanguage();
});

// تنسيق التاريخ
function formatDate(date, includeTime = false) {
    const d = new Date(date);
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    
    if (includeTime) {
        options.hour = '2-digit';
        options.minute = '2-digit';
    }
    
    return d.toLocaleDateString(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', options);
}

// تنسيق الوقت
function formatTime(date) {
    const d = new Date(date);
    return d.toLocaleTimeString(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// البحث في البيانات
function searchData(data, searchTerm, fields) {
    if (!searchTerm) return data;
    
    const term = searchTerm.toLowerCase();
    return data.filter(item => {
        return fields.some(field => {
            const value = getNestedValue(item, field);
            return value && value.toString().toLowerCase().includes(term);
        });
    });
}

// الحصول على قيمة متداخلة من كائن
function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
}

// فلترة البيانات
function filterData(data, filters) {
    return data.filter(item => {
        return Object.keys(filters).every(key => {
            const filterValue = filters[key];
            if (!filterValue || filterValue === 'all') return true;
            
            const itemValue = getNestedValue(item, key);
            return itemValue === filterValue;
        });
    });
}

// ترتيب البيانات
function sortData(data, sortBy, sortOrder = 'asc') {
    return [...data].sort((a, b) => {
        const aValue = getNestedValue(a, sortBy);
        const bValue = getNestedValue(b, sortBy);
        
        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
        return 0;
    });
}

// تصدير البيانات إلى CSV
function exportToCSV(data, filename, headers) {
    if (!data || data.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    // إنشاء رؤوس الأعمدة
    const csvHeaders = headers.map(h => h.label).join(',');
    
    // إنشاء صفوف البيانات
    const csvRows = data.map(item => {
        return headers.map(header => {
            const value = getNestedValue(item, header.key);
            // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
            const cleanValue = value ? value.toString().replace(/"/g, '""') : '';
            return `"${cleanValue}"`;
        }).join(',');
    });
    
    // دمج الرؤوس والصفوف
    const csvContent = [csvHeaders, ...csvRows].join('\n');
    
    // إنشاء وتحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${formatDate(new Date()).replace(/\//g, '-')}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تصدير البيانات إلى JSON
function exportToJSON(data, filename) {
    if (!data || data.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    const jsonContent = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${formatDate(new Date()).replace(/\//g, '-')}.json`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// طباعة البيانات
function printData(title, data, headers) {
    const printWindow = window.open('', '_blank');
    const tableRows = data.map(item => {
        const cells = headers.map(header => {
            const value = getNestedValue(item, header.key);
            return `<td>${value || ''}</td>`;
        }).join('');
        return `<tr>${cells}</tr>`;
    }).join('');
    
    const tableHeaders = headers.map(h => `<th>${h.label}</th>`).join('');
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="${currentLanguage === 'ar' ? 'rtl' : 'ltr'}">
        <head>
            <title>${title}</title>
            <meta charset="UTF-8">
            <style>
                body { font-family: 'Cairo', Arial, sans-serif; margin: 20px; }
                h1 { color: #2563eb; text-align: center; margin-bottom: 30px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 12px; text-align: ${currentLanguage === 'ar' ? 'right' : 'left'}; }
                th { background-color: #f8fafc; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .print-date { text-align: center; margin-top: 20px; color: #666; }
            </style>
        </head>
        <body>
            <h1>${title}</h1>
            <table>
                <thead><tr>${tableHeaders}</tr></thead>
                <tbody>${tableRows}</tbody>
            </table>
            <div class="print-date">تاريخ الطباعة: ${formatDate(new Date(), true)}</div>
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 250);
}

// التحقق من صحة البريد الإلكتروني
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// التحقق من صحة رقم الجوال
function validatePhone(phone) {
    const phoneRegex = /^[0-9+\-\s()]{10,}$/;
    return phoneRegex.test(phone);
}

// تنظيف النص
function sanitizeText(text) {
    if (!text) return '';
    return text.toString().trim().replace(/[<>]/g, '');
}

// إنشاء معرف فريد
function generateId() {
    return Date.now() + Math.random().toString(36).substr(2, 9);
}

// تحويل النص إلى عنوان URL
function slugify(text) {
    return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/\s+/g, '-')
        .replace(/[^\w\-]+/g, '')
        .replace(/\-\-+/g, '-');
}

// تحميل مكتبة SheetJS لتصدير Excel
function loadSheetJS() {
    return new Promise((resolve, reject) => {
        if (window.XLSX) {
            resolve();
            return;
        }

        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('فشل في تحميل مكتبة Excel'));
        document.head.appendChild(script);
    });
}

// تصدير البيانات إلى Excel
async function exportToExcel(data, filename, headers, options = {}) {
    if (!data || data.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    try {
        // تحميل مكتبة SheetJS
        await loadSheetJS();

        // إعداد البيانات
        const currentUser = getCurrentUser();
        const exportDate = new Date();

        // إنشاء ورقة العمل
        const ws = XLSX.utils.aoa_to_sheet([]);

        // إضافة معلومات الشركة والتصدير
        const companyInfo = [
            ['نظام إدارة العملاء والموردين', '', '', '', ''],
            ['تاريخ التصدير:', formatDate(exportDate, true), '', '', ''],
            ['المستخدم:', currentUser ? currentUser.name : 'غير معروف', '', '', ''],
            ['إجمالي السجلات:', data.length.toString(), '', '', ''],
            ['', '', '', '', ''], // سطر فارغ
        ];

        // إضافة معلومات الشركة
        XLSX.utils.sheet_add_aoa(ws, companyInfo, { origin: 'A1' });

        // إضافة رؤوس الأعمدة
        const headerRow = headers.map(h => h.label);
        XLSX.utils.sheet_add_aoa(ws, [headerRow], { origin: 'A6' });

        // إضافة البيانات
        const dataRows = data.map(item => {
            return headers.map(header => {
                const value = getNestedValue(item, header.key);
                return value || '';
            });
        });

        XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: 'A7' });

        // تنسيق الخلايا
        const range = XLSX.utils.decode_range(ws['!ref']);

        // تنسيق رؤوس الشركة
        for (let row = 0; row < 4; row++) {
            const cellAddress = XLSX.utils.encode_cell({ r: row, c: 0 });
            if (ws[cellAddress]) {
                ws[cellAddress].s = {
                    font: { bold: true, color: { rgb: "2563eb" } },
                    alignment: { horizontal: "right" }
                };
            }
        }

        // تنسيق رؤوس الأعمدة
        for (let col = 0; col <= range.e.c; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 5, c: col });
            if (ws[cellAddress]) {
                ws[cellAddress].s = {
                    font: { bold: true, color: { rgb: "ffffff" } },
                    fill: { fgColor: { rgb: "2563eb" } },
                    alignment: { horizontal: "center" },
                    border: {
                        top: { style: "thin", color: { rgb: "000000" } },
                        bottom: { style: "thin", color: { rgb: "000000" } },
                        left: { style: "thin", color: { rgb: "000000" } },
                        right: { style: "thin", color: { rgb: "000000" } }
                    }
                };
            }
        }

        // تنسيق البيانات
        for (let row = 6; row <= range.e.r; row++) {
            for (let col = 0; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                if (ws[cellAddress]) {
                    ws[cellAddress].s = {
                        alignment: { horizontal: "right" },
                        border: {
                            top: { style: "thin", color: { rgb: "cccccc" } },
                            bottom: { style: "thin", color: { rgb: "cccccc" } },
                            left: { style: "thin", color: { rgb: "cccccc" } },
                            right: { style: "thin", color: { rgb: "cccccc" } }
                        }
                    };

                    // تلوين الصفوف المتناوبة
                    if (row % 2 === 0) {
                        ws[cellAddress].s.fill = { fgColor: { rgb: "f8fafc" } };
                    }
                }
            }
        }

        // تحديد عرض الأعمدة
        const colWidths = headers.map(() => ({ wch: 20 }));
        ws['!cols'] = colWidths;

        // إنشاء كتاب العمل
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, options.sheetName || 'البيانات');

        // تحميل الملف
        const fileName = `${filename}_${formatDate(exportDate).replace(/\//g, '-')}.xlsx`;
        XLSX.writeFile(wb, fileName);

        // تسجيل النشاط
        if (typeof logActivity === 'function') {
            logActivity('export_excel', `تصدير ${data.length} سجل إلى Excel: ${filename}`);
        }

        return true;

    } catch (error) {
        console.error('خطأ في تصدير Excel:', error);
        alert('حدث خطأ أثناء تصدير الملف: ' + error.message);
        return false;
    }
}

// تحميل مكتبة jsPDF لتصدير PDF
function loadJsPDF() {
    return new Promise((resolve, reject) => {
        if (window.jsPDF) {
            resolve();
            return;
        }

        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('فشل في تحميل مكتبة PDF'));
        document.head.appendChild(script);
    });
}

// تصدير البيانات إلى PDF
async function exportToPDF(data, filename, headers, options = {}) {
    if (!data || data.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    try {
        // تحميل مكتبة jsPDF
        await loadJsPDF();

        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4'
        });

        // إعداد الخط العربي (إذا كان متاحاً)
        doc.setFont('helvetica');
        doc.setFontSize(16);

        // عنوان التقرير
        const title = options.title || 'تقرير البيانات';
        doc.text(title, 148, 20, { align: 'center' });

        // معلومات التصدير
        doc.setFontSize(10);
        const currentUser = getCurrentUser();
        const exportDate = formatDate(new Date(), true);

        doc.text(`تاريخ التصدير: ${exportDate}`, 20, 35);
        doc.text(`المستخدم: ${currentUser ? currentUser.name : 'غير معروف'}`, 20, 42);
        doc.text(`عدد السجلات: ${data.length}`, 20, 49);

        // إنشاء الجدول
        const tableData = data.map(item => {
            return headers.map(header => {
                const value = getNestedValue(item, header.key);
                return value ? value.toString() : '';
            });
        });

        const tableHeaders = headers.map(h => h.label);

        // إضافة الجدول (يتطلب مكتبة إضافية للجداول المعقدة)
        let yPosition = 60;
        const rowHeight = 8;
        const colWidth = (297 - 40) / headers.length; // عرض الصفحة مقسوم على عدد الأعمدة

        // رؤوس الأعمدة
        doc.setFillColor(37, 99, 235);
        doc.rect(20, yPosition, 257, rowHeight, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(9);

        tableHeaders.forEach((header, index) => {
            doc.text(header, 25 + (index * colWidth), yPosition + 5);
        });

        // البيانات
        doc.setTextColor(0, 0, 0);
        yPosition += rowHeight;

        tableData.forEach((row, rowIndex) => {
            if (yPosition > 180) { // صفحة جديدة
                doc.addPage();
                yPosition = 20;
            }

            // تلوين الصفوف المتناوبة
            if (rowIndex % 2 === 0) {
                doc.setFillColor(248, 250, 252);
                doc.rect(20, yPosition, 257, rowHeight, 'F');
            }

            row.forEach((cell, cellIndex) => {
                doc.text(cell.substring(0, 25), 25 + (cellIndex * colWidth), yPosition + 5); // تقليم النص الطويل
            });

            yPosition += rowHeight;
        });

        // حفظ الملف
        const fileName = `${filename}_${formatDate(new Date()).replace(/\//g, '-')}.pdf`;
        doc.save(fileName);

        // تسجيل النشاط
        if (typeof logActivity === 'function') {
            logActivity('export_pdf', `تصدير ${data.length} سجل إلى PDF: ${filename}`);
        }

        return true;

    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        alert('حدث خطأ أثناء تصدير الملف: ' + error.message);
        return false;
    }
}

// تصدير الوظائف للاستخدام العام
window.utils = {
    toggleLanguage,
    updateLanguage,
    formatDate,
    formatTime,
    searchData,
    filterData,
    sortData,
    exportToCSV,
    exportToJSON,
    exportToExcel,
    exportToPDF,
    printData,
    validateEmail,
    validatePhone,
    sanitizeText,
    generateId,
    slugify,
    getNestedValue,
    loadSheetJS,
    loadJsPDF
};
