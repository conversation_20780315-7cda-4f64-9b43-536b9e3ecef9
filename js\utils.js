// ===== الوظائف المساعدة =====

// إعدادات اللغة
let currentLanguage = localStorage.getItem('language') || 'ar';

// تبديل اللغة
function toggleLanguage() {
    currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
    localStorage.setItem('language', currentLanguage);
    updateLanguage();
}

// تحديث النصوص حسب اللغة
function updateLanguage() {
    const elements = document.querySelectorAll('[data-ar][data-en]');
    const html = document.documentElement;
    const langText = document.getElementById('langText');
    
    elements.forEach(element => {
        if (currentLanguage === 'ar') {
            element.textContent = element.getAttribute('data-ar');
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            if (langText) langText.textContent = 'English';
        } else {
            element.textContent = element.getAttribute('data-en');
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            if (langText) langText.textContent = 'العربية';
        }
    });
}

// تهيئة اللغة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateLanguage();
});

// تنسيق التاريخ
function formatDate(date, includeTime = false) {
    const d = new Date(date);
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    
    if (includeTime) {
        options.hour = '2-digit';
        options.minute = '2-digit';
    }
    
    return d.toLocaleDateString(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', options);
}

// تنسيق الوقت
function formatTime(date) {
    const d = new Date(date);
    return d.toLocaleTimeString(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// البحث في البيانات
function searchData(data, searchTerm, fields) {
    if (!searchTerm) return data;
    
    const term = searchTerm.toLowerCase();
    return data.filter(item => {
        return fields.some(field => {
            const value = getNestedValue(item, field);
            return value && value.toString().toLowerCase().includes(term);
        });
    });
}

// الحصول على قيمة متداخلة من كائن
function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
}

// فلترة البيانات
function filterData(data, filters) {
    return data.filter(item => {
        return Object.keys(filters).every(key => {
            const filterValue = filters[key];
            if (!filterValue || filterValue === 'all') return true;
            
            const itemValue = getNestedValue(item, key);
            return itemValue === filterValue;
        });
    });
}

// ترتيب البيانات
function sortData(data, sortBy, sortOrder = 'asc') {
    return [...data].sort((a, b) => {
        const aValue = getNestedValue(a, sortBy);
        const bValue = getNestedValue(b, sortBy);
        
        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
        return 0;
    });
}

// تصدير البيانات إلى CSV
function exportToCSV(data, filename, headers) {
    if (!data || data.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    // إنشاء رؤوس الأعمدة
    const csvHeaders = headers.map(h => h.label).join(',');
    
    // إنشاء صفوف البيانات
    const csvRows = data.map(item => {
        return headers.map(header => {
            const value = getNestedValue(item, header.key);
            // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
            const cleanValue = value ? value.toString().replace(/"/g, '""') : '';
            return `"${cleanValue}"`;
        }).join(',');
    });
    
    // دمج الرؤوس والصفوف
    const csvContent = [csvHeaders, ...csvRows].join('\n');
    
    // إنشاء وتحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${formatDate(new Date()).replace(/\//g, '-')}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تصدير البيانات إلى JSON
function exportToJSON(data, filename) {
    if (!data || data.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    const jsonContent = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${formatDate(new Date()).replace(/\//g, '-')}.json`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// طباعة البيانات
function printData(title, data, headers) {
    const printWindow = window.open('', '_blank');
    const tableRows = data.map(item => {
        const cells = headers.map(header => {
            const value = getNestedValue(item, header.key);
            return `<td>${value || ''}</td>`;
        }).join('');
        return `<tr>${cells}</tr>`;
    }).join('');
    
    const tableHeaders = headers.map(h => `<th>${h.label}</th>`).join('');
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="${currentLanguage === 'ar' ? 'rtl' : 'ltr'}">
        <head>
            <title>${title}</title>
            <meta charset="UTF-8">
            <style>
                body { font-family: 'Cairo', Arial, sans-serif; margin: 20px; }
                h1 { color: #2563eb; text-align: center; margin-bottom: 30px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 12px; text-align: ${currentLanguage === 'ar' ? 'right' : 'left'}; }
                th { background-color: #f8fafc; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .print-date { text-align: center; margin-top: 20px; color: #666; }
            </style>
        </head>
        <body>
            <h1>${title}</h1>
            <table>
                <thead><tr>${tableHeaders}</tr></thead>
                <tbody>${tableRows}</tbody>
            </table>
            <div class="print-date">تاريخ الطباعة: ${formatDate(new Date(), true)}</div>
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 250);
}

// التحقق من صحة البريد الإلكتروني
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// التحقق من صحة رقم الجوال
function validatePhone(phone) {
    const phoneRegex = /^[0-9+\-\s()]{10,}$/;
    return phoneRegex.test(phone);
}

// تنظيف النص
function sanitizeText(text) {
    if (!text) return '';
    return text.toString().trim().replace(/[<>]/g, '');
}

// إنشاء معرف فريد
function generateId() {
    return Date.now() + Math.random().toString(36).substr(2, 9);
}

// تحويل النص إلى عنوان URL
function slugify(text) {
    return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/\s+/g, '-')
        .replace(/[^\w\-]+/g, '')
        .replace(/\-\-+/g, '-');
}

// تصدير الوظائف للاستخدام العام
window.utils = {
    toggleLanguage,
    updateLanguage,
    formatDate,
    formatTime,
    searchData,
    filterData,
    sortData,
    exportToCSV,
    exportToJSON,
    printData,
    validateEmail,
    validatePhone,
    sanitizeText,
    generateId,
    slugify,
    getNestedValue
};
