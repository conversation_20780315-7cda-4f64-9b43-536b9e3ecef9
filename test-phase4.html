<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المرحلة الرابعة - نظام التقارير والتحليلات المتقدم</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link rel="stylesheet" href="css/reports.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: var(--primary-blue); margin-bottom: 30px;">
            <i class="fas fa-chart-line"></i>
            اختبار المرحلة الرابعة - نظام التقارير والتحليلات المتقدم
        </h1>
        
        <!-- إشعار المرحلة الجديدة -->
        <div class="phase-announcement">
            <div class="announcement-header">
                <i class="fas fa-rocket"></i>
                <h2>🚀 المرحلة الرابعة: نظام التقارير والتحليلات المتقدم</h2>
            </div>
            <div class="announcement-content">
                <p><strong>تم تطوير نظام تقارير وتحليلات شامل ومتطور يتضمن:</strong></p>
                <div class="features-showcase">
                    <div class="feature-highlight">
                        <i class="fas fa-chart-bar"></i>
                        <h4>إحصائيات تفاعلية</h4>
                        <p>بطاقات ملونة تعرض الإيرادات، العملاء، معدل التحويل، ومتوسط حجم الصفقة</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-chart-line"></i>
                        <h4>رسوم بيانية متقدمة</h4>
                        <p>رسوم بيانية تفاعلية لاتجاه المبيعات وتوزيع العملاء باستخدام Chart.js</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-filter"></i>
                        <h4>فلاتر ذكية</h4>
                        <p>فلترة متقدمة حسب الفترة الزمنية، نوع التقرير، المدينة، والحالة</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-table"></i>
                        <h4>جداول تفصيلية</h4>
                        <p>جداول تقارير شاملة للعملاء، عروض الأسعار، وأداء الموظفين</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-download"></i>
                        <h4>تصدير متطور</h4>
                        <p>تصدير التقارير والرسوم البيانية بصيغ متعددة (CSV, Excel, PDF, PNG)</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-mobile-alt"></i>
                        <h4>تصميم متجاوب</h4>
                        <p>واجهة تتكيف مع جميع أحجام الشاشات مع تجربة مستخدم محسنة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم اختبار الإحصائيات التفاعلية -->
        <div class="test-section analytics-section">
            <h2><i class="fas fa-chart-bar"></i> اختبار الإحصائيات التفاعلية</h2>
            <div class="test-buttons">
                <button class="btn-analytics" onclick="testRevenueStats()">
                    <i class="fas fa-dollar-sign"></i>
                    إحصائيات الإيرادات
                </button>
                <button class="btn-customers" onclick="testCustomerStats()">
                    <i class="fas fa-users"></i>
                    إحصائيات العملاء
                </button>
                <button class="btn-conversion" onclick="testConversionRate()">
                    <i class="fas fa-percentage"></i>
                    معدل التحويل
                </button>
                <button class="btn-deals" onclick="testDealSize()">
                    <i class="fas fa-handshake"></i>
                    متوسط حجم الصفقة
                </button>
            </div>
            <div class="test-description">
                <p><strong>الإحصائيات التفاعلية تتضمن:</strong></p>
                <ul>
                    <li>💰 <strong>إجمالي الإيرادات:</strong> مع مؤشر النمو الشهري</li>
                    <li>👥 <strong>عدد العملاء:</strong> مع معدل الزيادة</li>
                    <li>📊 <strong>معدل التحويل:</strong> نسبة نجاح عروض الأسعار</li>
                    <li>🤝 <strong>متوسط حجم الصفقة:</strong> القيمة المتوسطة للصفقات المقبولة</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار الرسوم البيانية -->
        <div class="test-section charts-section">
            <h2><i class="fas fa-chart-line"></i> اختبار الرسوم البيانية المتقدمة</h2>
            <div class="test-buttons">
                <button class="btn-sales-trend" onclick="testSalesTrend()">
                    <i class="fas fa-chart-line"></i>
                    اتجاه المبيعات
                </button>
                <button class="btn-customer-distribution" onclick="testCustomerDistribution()">
                    <i class="fas fa-chart-pie"></i>
                    توزيع العملاء
                </button>
                <button class="btn-chart-export" onclick="testChartExport()">
                    <i class="fas fa-download"></i>
                    تصدير الرسوم
                </button>
                <button class="btn-chart-fullscreen" onclick="testChartFullscreen()">
                    <i class="fas fa-expand"></i>
                    عرض بملء الشاشة
                </button>
            </div>
            <div class="test-description">
                <p><strong>الرسوم البيانية المتقدمة تشمل:</strong></p>
                <ul>
                    <li>📈 <strong>رسم بياني خطي:</strong> لاتجاه المبيعات عبر الوقت</li>
                    <li>🍩 <strong>رسم بياني دائري:</strong> لتوزيع العملاء حسب المدن</li>
                    <li>🎨 <strong>ألوان تفاعلية:</strong> مع تدرجات جذابة</li>
                    <li>📱 <strong>تصميم متجاوب:</strong> يتكيف مع حجم الشاشة</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار الفلاتر والتقارير -->
        <div class="test-section filters-section">
            <h2><i class="fas fa-filter"></i> اختبار الفلاتر والتقارير التفصيلية</h2>
            <div class="test-buttons">
                <button class="btn-date-filters" onclick="testDateFilters()">
                    <i class="fas fa-calendar"></i>
                    فلاتر التاريخ
                </button>
                <button class="btn-report-types" onclick="testReportTypes()">
                    <i class="fas fa-list"></i>
                    أنواع التقارير
                </button>
                <button class="btn-detailed-tables" onclick="testDetailedTables()">
                    <i class="fas fa-table"></i>
                    الجداول التفصيلية
                </button>
                <button class="btn-export-options" onclick="testExportOptions()">
                    <i class="fas fa-file-export"></i>
                    خيارات التصدير
                </button>
            </div>
            <div class="test-description">
                <p><strong>نظام الفلاتر والتقارير يوفر:</strong></p>
                <ul>
                    <li>📅 <strong>فلترة زمنية:</strong> اليوم، الأسبوع، الشهر، الربع، السنة</li>
                    <li>📋 <strong>تقارير متنوعة:</strong> العملاء، الموردين، عروض الأسعار، الأداء</li>
                    <li>📊 <strong>جداول تفاعلية:</strong> مع ترتيب وبحث متقدم</li>
                    <li>💾 <strong>تصدير شامل:</strong> CSV, Excel, PDF, وطباعة</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار التحليلات المتقدمة -->
        <div class="test-section advanced-section">
            <h2><i class="fas fa-brain"></i> اختبار التحليلات المتقدمة</h2>
            <div class="test-buttons">
                <button class="btn-performance-analysis" onclick="testPerformanceAnalysis()">
                    <i class="fas fa-chart-bar"></i>
                    تحليل الأداء
                </button>
                <button class="btn-trend-analysis" onclick="testTrendAnalysis()">
                    <i class="fas fa-trending-up"></i>
                    تحليل الاتجاهات
                </button>
                <button class="btn-predictive-analytics" onclick="testPredictiveAnalytics()">
                    <i class="fas fa-crystal-ball"></i>
                    التحليلات التنبؤية
                </button>
                <button class="btn-custom-reports" onclick="testCustomReports()">
                    <i class="fas fa-cogs"></i>
                    التقارير المخصصة
                </button>
            </div>
            <div class="test-description">
                <p><strong>التحليلات المتقدمة تتضمن:</strong></p>
                <ul>
                    <li>🎯 <strong>تحليل الأداء:</strong> مؤشرات الأداء الرئيسية (KPIs)</li>
                    <li>📈 <strong>تحليل الاتجاهات:</strong> توقع الاتجاهات المستقبلية</li>
                    <li>🔮 <strong>التحليلات التنبؤية:</strong> توقعات ذكية للمبيعات</li>
                    <li>⚙️ <strong>تقارير مخصصة:</strong> إنشاء تقارير حسب الحاجة</li>
                </ul>
            </div>
        </div>

        <!-- قسم الروابط السريعة -->
        <div class="test-section quick-links-section">
            <h2><i class="fas fa-link"></i> الروابط السريعة</h2>
            <div class="test-buttons">
                <a href="reports.html" class="btn-primary">
                    <i class="fas fa-chart-line"></i>
                    صفحة التقارير الرئيسية
                </a>
                <a href="dashboard.html" class="btn-secondary">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="quotations.html" class="btn-info">
                    <i class="fas fa-file-invoice-dollar"></i>
                    عروض الأسعار
                </a>
                <a href="customers.html" class="btn-success">
                    <i class="fas fa-users"></i>
                    إدارة العملاء
                </a>
            </div>
            <div class="test-description">
                <p><strong>انتقل إلى الصفحات المختلفة لاختبار:</strong></p>
                <ul>
                    <li>🏠 <strong>لوحة التحكم:</strong> نظرة عامة على النظام</li>
                    <li>👥 <strong>إدارة العملاء:</strong> قاعدة بيانات العملاء</li>
                    <li>💰 <strong>عروض الأسعار:</strong> نظام عروض الأسعار المحسن</li>
                    <li>📊 <strong>التقارير:</strong> نظام التقارير والتحليلات الجديد</li>
                </ul>
            </div>
        </div>

        <!-- ملخص الإنجازات -->
        <div class="achievements-summary">
            <h2><i class="fas fa-trophy"></i> ملخص إنجازات المرحلة الرابعة</h2>
            <div class="achievements-grid">
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>نظام التقارير المتقدم</h4>
                    <p>تم تطوير نظام تقارير شامل مع إحصائيات تفاعلية</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>الرسوم البيانية التفاعلية</h4>
                    <p>تم دمج Chart.js لرسوم بيانية متقدمة وتفاعلية</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>فلاتر ذكية</h4>
                    <p>تم إضافة نظام فلترة متقدم للتقارير</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>تصدير شامل</h4>
                    <p>تم تطوير نظام تصدير متعدد الصيغ</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>تصميم متجاوب</h4>
                    <p>تم تحسين التصميم ليعمل على جميع الأجهزة</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>تحليلات متقدمة</h4>
                    <p>تم إضافة مؤشرات أداء ذكية وتحليلات عميقة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    
    <style>
        /* أنماط خاصة بصفحة اختبار المرحلة الرابعة */
        .phase-announcement {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .announcement-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
        }

        .announcement-header i {
            font-size: 3rem;
            color: #fbbf24;
        }

        .announcement-header h2 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .features-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .feature-highlight {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .feature-highlight:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .feature-highlight i {
            font-size: 2.5rem;
            color: #fbbf24;
            margin-bottom: 15px;
        }

        .feature-highlight h4 {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .feature-highlight p {
            margin: 0;
            opacity: 0.9;
            line-height: 1.6;
        }

        /* أنماط أقسام الاختبار المتخصصة */
        .analytics-section {
            border-left: 5px solid #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
        }

        .charts-section {
            border-left: 5px solid #2563eb;
            background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
        }

        .filters-section {
            border-left: 5px solid #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
        }

        .advanced-section {
            border-left: 5px solid #8b5cf6;
            background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
        }

        .quick-links-section {
            border-left: 5px solid #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }

        /* أزرار متخصصة */
        .btn-analytics {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .btn-analytics:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .btn-customers {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }

        .btn-customers:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        .btn-conversion {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-conversion:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
        }

        .btn-deals {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }

        .btn-deals:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
        }

        /* أزرار الرسوم البيانية */
        .btn-sales-trend,
        .btn-customer-distribution,
        .btn-chart-export,
        .btn-chart-fullscreen {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }

        .btn-sales-trend:hover,
        .btn-customer-distribution:hover,
        .btn-chart-export:hover,
        .btn-chart-fullscreen:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        /* أزرار الفلاتر */
        .btn-date-filters,
        .btn-report-types,
        .btn-detailed-tables,
        .btn-export-options {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-date-filters:hover,
        .btn-report-types:hover,
        .btn-detailed-tables:hover,
        .btn-export-options:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
        }

        /* أزرار التحليلات المتقدمة */
        .btn-performance-analysis,
        .btn-trend-analysis,
        .btn-predictive-analytics,
        .btn-custom-reports {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }

        .btn-performance-analysis:hover,
        .btn-trend-analysis:hover,
        .btn-predictive-analytics:hover,
        .btn-custom-reports:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
        }

        /* ملخص الإنجازات */
        .achievements-summary {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            border: 2px solid #e2e8f0;
        }

        .achievements-summary h2 {
            color: var(--primary-blue);
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .achievement-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #10b981;
            transition: var(--transition);
        }

        .achievement-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .achievement-item.completed i {
            color: #10b981;
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .achievement-item h4 {
            color: var(--primary-blue);
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .achievement-item p {
            color: var(--gray);
            margin: 0;
            line-height: 1.6;
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .features-showcase {
                grid-template-columns: 1fr;
            }

            .announcement-header {
                flex-direction: column;
                text-align: center;
            }

            .announcement-header h2 {
                font-size: 1.5rem;
            }

            .achievements-grid {
                grid-template-columns: 1fr;
            }

            .phase-announcement {
                margin-left: -10px;
                margin-right: -10px;
                border-radius: 0;
            }
        }
    </style>

    <script>
        // دوال اختبار الإحصائيات التفاعلية
        function testRevenueStats() {
            showNotification('💰 إحصائيات الإيرادات - عرض الإيرادات الإجمالية مع مؤشر النمو', 'success');
        }

        function testCustomerStats() {
            showNotification('👥 إحصائيات العملاء - عدد العملاء الإجمالي مع معدل الزيادة', 'success');
        }

        function testConversionRate() {
            showNotification('📊 معدل التحويل - نسبة نجاح عروض الأسعار المرسلة', 'info');
        }

        function testDealSize() {
            showNotification('🤝 متوسط حجم الصفقة - القيمة المتوسطة للصفقات المقبولة', 'info');
        }

        // دوال اختبار الرسوم البيانية
        function testSalesTrend() {
            showNotification('📈 رسم بياني لاتجاه المبيعات - عرض تطور المبيعات عبر الوقت', 'success');
        }

        function testCustomerDistribution() {
            showNotification('🍩 رسم بياني لتوزيع العملاء - توزيع العملاء حسب المدن', 'success');
        }

        function testChartExport() {
            showNotification('📥 تصدير الرسوم البيانية - حفظ الرسوم كصور PNG', 'info');
        }

        function testChartFullscreen() {
            showNotification('🖥️ عرض بملء الشاشة - عرض الرسوم البيانية بحجم كامل', 'info');
        }

        // دوال اختبار الفلاتر والتقارير
        function testDateFilters() {
            showNotification('📅 فلاتر التاريخ - فلترة البيانات حسب فترات زمنية مختلفة', 'success');
        }

        function testReportTypes() {
            showNotification('📋 أنواع التقارير - تقارير العملاء، الموردين، عروض الأسعار، والأداء', 'success');
        }

        function testDetailedTables() {
            showNotification('📊 الجداول التفصيلية - جداول تفاعلية مع ترتيب وبحث', 'info');
        }

        function testExportOptions() {
            showNotification('💾 خيارات التصدير - تصدير بصيغ CSV, Excel, PDF', 'info');
        }

        // دوال اختبار التحليلات المتقدمة
        function testPerformanceAnalysis() {
            showNotification('🎯 تحليل الأداء - مؤشرات الأداء الرئيسية ومعدلات النجاح', 'success');
        }

        function testTrendAnalysis() {
            showNotification('📈 تحليل الاتجاهات - تحليل الاتجاهات والأنماط في البيانات', 'success');
        }

        function testPredictiveAnalytics() {
            showNotification('🔮 التحليلات التنبؤية - توقعات ذكية للمبيعات والنمو', 'info');
        }

        function testCustomReports() {
            showNotification('⚙️ التقارير المخصصة - إنشاء تقارير مخصصة حسب الحاجة', 'info');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('🚀 مرحباً بك في المرحلة الرابعة - نظام التقارير والتحليلات المتقدم!', 'success');
        });
    </script>
</body>
</html>
