// ===== نظام إدارة المستخدمين والصلاحيات =====

// تشفير كلمة المرور (تشفير بسيط للتجربة)
function hashPassword(password) {
    return btoa(password + 'salt_key_2024');
}

// التحقق من كلمة المرور
function verifyPassword(password, hashedPassword) {
    return hashPassword(password) === hashedPassword;
}

// إنشاء المستخدمين الافتراضيين
function initializeUsers() {
    const users = JSON.parse(localStorage.getItem('users')) || [];
    
    if (users.length === 0) {
        const defaultUsers = [
            // المدير العام
            {
                id: 'admin',
                username: 'admin',
                password: hashPassword('admin123'),
                name: 'المدير العام',
                role: 'admin',
                permissions: ['all'],
                createdAt: new Date().toISOString()
            },
            
            // موظفي التلي سيلز
            {
                id: 'telesales_1',
                username: 'suhai<PERSON>.azhari',
                password: hashPassword('Suhailah@2024'),
                name: 'سهيلة الأزهري',
                role: 'telesales',
                permissions: ['view_customers', 'add_customers', 'edit_customers', 'view_quotations', 'add_quotations', 'edit_quotations'],
                telesalesId: 'ts_001',
                createdAt: new Date().toISOString()
            },
            {
                id: 'telesales_2',
                username: 'amani.adel',
                password: hashPassword('Amani@2024'),
                name: 'أماني عادل',
                role: 'telesales',
                permissions: ['view_customers', 'add_customers', 'edit_customers', 'view_quotations', 'add_quotations', 'edit_quotations'],
                telesalesId: 'ts_002',
                createdAt: new Date().toISOString()
            },
            {
                id: 'telesales_3',
                username: 'basant.hamouda',
                password: hashPassword('Basant@2024'),
                name: 'بسنت حمودة',
                role: 'telesales',
                permissions: ['view_customers', 'add_customers', 'edit_customers', 'view_quotations', 'add_quotations', 'edit_quotations'],
                telesalesId: 'ts_003',
                createdAt: new Date().toISOString()
            },
            {
                id: 'telesales_4',
                username: 'esraa.badra',
                password: hashPassword('Esraa@2024'),
                name: 'إسراء بدرة',
                role: 'telesales',
                permissions: ['view_customers', 'add_customers', 'edit_customers', 'view_quotations', 'add_quotations', 'edit_quotations'],
                telesalesId: 'ts_004',
                createdAt: new Date().toISOString()
            },
            {
                id: 'telesales_5',
                username: 'gharam.etreby',
                password: hashPassword('Gharam@2024'),
                name: 'غرام الإتربي',
                role: 'telesales',
                permissions: ['view_customers', 'add_customers', 'edit_customers', 'view_quotations', 'add_quotations', 'edit_quotations'],
                telesalesId: 'ts_005',
                createdAt: new Date().toISOString()
            },
            
            // مسؤولي الموردين
            {
                id: 'supplier_1',
                username: 'emad.supplier',
                password: hashPassword('Emad@2024'),
                name: 'عماد',
                role: 'supplier_manager',
                permissions: ['view_suppliers', 'add_suppliers', 'edit_suppliers', 'delete_suppliers'],
                createdAt: new Date().toISOString()
            },
            {
                id: 'supplier_2',
                username: 'yomna.emad',
                password: hashPassword('Yomna@2024'),
                name: 'يمنى عماد',
                role: 'supplier_manager',
                permissions: ['view_suppliers', 'add_suppliers', 'edit_suppliers'],
                createdAt: new Date().toISOString()
            }
        ];
        
        localStorage.setItem('users', JSON.stringify(defaultUsers));
        return defaultUsers;
    }
    
    return users;
}

// إنشاء بيانات موظفي التلي سيلز
function initializeTelesalesData() {
    const telesales = JSON.parse(localStorage.getItem('telesales')) || [];
    
    if (telesales.length === 0) {
        const defaultTelesales = [
            {
                id: 'ts_001',
                name: 'سهيلة الأزهري',
                userId: 'telesales_1',
                phone: '+966 50 123 4567',
                email: '<EMAIL>',
                department: 'المبيعات',
                position: 'موظف تلي سيلز أول',
                hireDate: '2023-01-15',
                status: 'active',
                targets: {
                    monthly_calls: 500,
                    monthly_customers: 50,
                    monthly_quotations: 20
                },
                performance: {
                    total_calls: 0,
                    successful_calls: 0,
                    unanswered_calls: 0,
                    quotations_generated: 0,
                    customers_acquired: 0
                },
                representatives: ['rep_001', 'rep_002'],
                createdAt: new Date().toISOString()
            },
            {
                id: 'ts_002',
                name: 'أماني عادل',
                userId: 'telesales_2',
                phone: '+966 50 234 5678',
                email: '<EMAIL>',
                department: 'المبيعات',
                position: 'موظف تلي سيلز',
                hireDate: '2023-03-20',
                status: 'active',
                targets: {
                    monthly_calls: 450,
                    monthly_customers: 45,
                    monthly_quotations: 18
                },
                performance: {
                    total_calls: 0,
                    successful_calls: 0,
                    unanswered_calls: 0,
                    quotations_generated: 0,
                    customers_acquired: 0
                },
                representatives: ['rep_003', 'rep_004'],
                createdAt: new Date().toISOString()
            },
            {
                id: 'ts_003',
                name: 'بسنت حمودة',
                userId: 'telesales_3',
                phone: '+966 50 345 6789',
                email: '<EMAIL>',
                department: 'المبيعات',
                position: 'موظف تلي سيلز',
                hireDate: '2023-05-10',
                status: 'active',
                targets: {
                    monthly_calls: 400,
                    monthly_customers: 40,
                    monthly_quotations: 15
                },
                performance: {
                    total_calls: 0,
                    successful_calls: 0,
                    unanswered_calls: 0,
                    quotations_generated: 0,
                    customers_acquired: 0
                },
                representatives: ['rep_005'],
                createdAt: new Date().toISOString()
            },
            {
                id: 'ts_004',
                name: 'إسراء بدرة',
                userId: 'telesales_4',
                phone: '+966 50 456 7890',
                email: '<EMAIL>',
                department: 'المبيعات',
                position: 'موظف تلي سيلز',
                hireDate: '2023-07-01',
                status: 'active',
                targets: {
                    monthly_calls: 350,
                    monthly_customers: 35,
                    monthly_quotations: 12
                },
                performance: {
                    total_calls: 0,
                    successful_calls: 0,
                    unanswered_calls: 0,
                    quotations_generated: 0,
                    customers_acquired: 0
                },
                representatives: ['rep_006'],
                createdAt: new Date().toISOString()
            },
            {
                id: 'ts_005',
                name: 'غرام الإتربي',
                userId: 'telesales_5',
                phone: '+966 50 567 8901',
                email: '<EMAIL>',
                department: 'المبيعات',
                position: 'موظف تلي سيلز',
                hireDate: '2023-09-15',
                status: 'active',
                targets: {
                    monthly_calls: 300,
                    monthly_customers: 30,
                    monthly_quotations: 10
                },
                performance: {
                    total_calls: 0,
                    successful_calls: 0,
                    unanswered_calls: 0,
                    quotations_generated: 0,
                    customers_acquired: 0
                },
                representatives: ['rep_007'],
                createdAt: new Date().toISOString()
            }
        ];
        
        localStorage.setItem('telesales', JSON.stringify(defaultTelesales));
        return defaultTelesales;
    }
    
    return telesales;
}

// إنشاء بيانات المندوبين
function initializeRepresentatives() {
    const representatives = JSON.parse(localStorage.getItem('representatives')) || [];
    
    if (representatives.length === 0) {
        const defaultRepresentatives = [
            {
                id: 'rep_001',
                name: 'أحمد محمد',
                telesalesId: 'ts_001',
                phone: '+966 55 111 2222',
                email: '<EMAIL>',
                area: 'الرياض - الشمال',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: 'rep_002',
                name: 'فاطمة علي',
                telesalesId: 'ts_001',
                phone: '+966 55 222 3333',
                email: '<EMAIL>',
                area: 'الرياض - الجنوب',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: 'rep_003',
                name: 'محمد سالم',
                telesalesId: 'ts_002',
                phone: '+966 55 333 4444',
                email: '<EMAIL>',
                area: 'جدة - الشمال',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: 'rep_004',
                name: 'نورا أحمد',
                telesalesId: 'ts_002',
                phone: '+966 55 444 5555',
                email: '<EMAIL>',
                area: 'جدة - الجنوب',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: 'rep_005',
                name: 'خالد عبدالله',
                telesalesId: 'ts_003',
                phone: '+966 55 555 6666',
                email: '<EMAIL>',
                area: 'الدمام',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: 'rep_006',
                name: 'سارة حسن',
                telesalesId: 'ts_004',
                phone: '+966 55 666 7777',
                email: '<EMAIL>',
                area: 'مكة المكرمة',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: 'rep_007',
                name: 'عبدالرحمن يوسف',
                telesalesId: 'ts_005',
                phone: '+966 55 777 8888',
                email: '<EMAIL>',
                area: 'المدينة المنورة',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        ];
        
        localStorage.setItem('representatives', JSON.stringify(defaultRepresentatives));
        return defaultRepresentatives;
    }
    
    return representatives;
}

// التحقق من الصلاحيات
function hasPermission(permission) {
    const currentUser = getCurrentUser();
    if (!currentUser) return false;
    
    // المدير العام له جميع الصلاحيات
    if (currentUser.role === 'admin' || currentUser.permissions.includes('all')) {
        return true;
    }
    
    return currentUser.permissions && currentUser.permissions.includes(permission);
}

// التحقق من إمكانية الوصول للعميل
function canAccessCustomer(customerId) {
    const currentUser = getCurrentUser();
    if (!currentUser) return false;
    
    // المدير العام يمكنه الوصول لجميع العملاء
    if (currentUser.role === 'admin') return true;
    
    // موظف التلي سيلز يمكنه الوصول للعملاء المخصصين له ولمندوبيه فقط
    if (currentUser.role === 'telesales') {
        const customers = JSON.parse(localStorage.getItem('customers')) || [];
        const customer = customers.find(c => c.id === customerId);
        
        if (!customer) return false;
        
        // التحقق من أن العميل مخصص لموظف التلي سيلز أو أحد مندوبيه
        return customer.telesalesId === currentUser.telesalesId || 
               (customer.representativeId && isRepresentativeUnderTelesales(customer.representativeId, currentUser.telesalesId));
    }
    
    return false;
}

// التحقق من أن المندوب تابع لموظف التلي سيلز
function isRepresentativeUnderTelesales(representativeId, telesalesId) {
    const representatives = JSON.parse(localStorage.getItem('representatives')) || [];
    const representative = representatives.find(r => r.id === representativeId);
    return representative && representative.telesalesId === telesalesId;
}

// الحصول على العملاء المسموح بالوصول إليهم
function getAccessibleCustomers() {
    const currentUser = getCurrentUser();
    const customers = JSON.parse(localStorage.getItem('customers')) || [];
    
    // المدير العام يمكنه الوصول لجميع العملاء
    if (currentUser.role === 'admin') {
        return customers;
    }
    
    // موظف التلي سيلز يحصل على عملائه ومندوبيه فقط
    if (currentUser.role === 'telesales') {
        const representatives = JSON.parse(localStorage.getItem('representatives')) || [];
        const userRepresentatives = representatives.filter(r => r.telesalesId === currentUser.telesalesId);
        const representativeIds = userRepresentatives.map(r => r.id);
        
        return customers.filter(customer => 
            customer.telesalesId === currentUser.telesalesId || 
            (customer.representativeId && representativeIds.includes(customer.representativeId))
        );
    }
    
    return [];
}

// تهيئة النظام
function initializeSystem() {
    initializeUsers();
    initializeTelesalesData();
    initializeRepresentatives();
}
