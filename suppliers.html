<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - نظام إدارة العملاء والموردين</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- شريط التنقل العلوي -->
    <nav class="top-navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="menu-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="nav-title" data-ar="إدارة الموردين" data-en="Supplier Management">إدارة الموردين</h1>
            </div>
            
            <div class="nav-right">
                <button class="lang-toggle" onclick="toggleLanguage()">
                    <i class="fas fa-globe"></i>
                    <span id="langText">English</span>
                </button>
                
                <div class="user-menu">
                    <button class="user-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-user-circle"></i>
                        <span id="userName">المستخدم</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" onclick="showProfile()">
                            <i class="fas fa-user"></i>
                            <span data-ar="الملف الشخصي" data-en="Profile">الملف الشخصي</span>
                        </a>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span data-ar="الإعدادات" data-en="Settings">الإعدادات</span>
                        </a>
                        <hr>
                        <a href="#" onclick="logout()" class="logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            <span data-ar="تسجيل الخروج" data-en="Logout">تسجيل الخروج</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-users-cog"></i>
            <span data-ar="نظام الإدارة" data-en="Management System">نظام الإدارة</span>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span data-ar="لوحة التحكم" data-en="Dashboard">لوحة التحكم</span>
            </a>
            
            <a href="customers.html" class="nav-item">
                <i class="fas fa-users"></i>
                <span data-ar="إدارة العملاء" data-en="Customer Management">إدارة العملاء</span>
                <span class="nav-badge" id="customersBadge">0</span>
            </a>
            
            <a href="suppliers.html" class="nav-item active">
                <i class="fas fa-truck"></i>
                <span data-ar="إدارة الموردين" data-en="Supplier Management">إدارة الموردين</span>
                <span class="nav-badge" id="suppliersBadge">0</span>
            </a>

            <a href="quotations.html" class="nav-item">
                <i class="fas fa-file-invoice-dollar"></i>
                <span data-ar="عروض الأسعار" data-en="Quotations">عروض الأسعار</span>
                <span class="nav-badge" id="quotationsBadge">0</span>
            </a>

            <a href="telesales.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span data-ar="فريق التلي سيلز" data-en="Telesales Team">فريق التلي سيلز</span>
                <span class="nav-badge" id="telesalesBadge">0</span>
            </a>
            
            <div class="nav-divider"></div>
            
            <a href="reports.html" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span data-ar="التقارير" data-en="Reports">التقارير</span>
            </a>
            
            <a href="activities.html" class="nav-item">
                <i class="fas fa-history"></i>
                <span data-ar="سجل الأنشطة" data-en="Activity Log">سجل الأنشطة</span>
            </a>
        </nav>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="content-container">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <div class="page-title">
                    <h2 data-ar="إدارة الموردين" data-en="Supplier Management">إدارة الموردين</h2>
                    <p data-ar="إضافة وتعديل ومتابعة بيانات الموردين" data-en="Add, edit and track supplier data">إضافة وتعديل ومتابعة بيانات الموردين</p>
                </div>
                
                <div class="page-actions">
                    <button class="btn-primary" onclick="showAddSupplierModal()" id="addSupplierBtn">
                        <i class="fas fa-truck-loading"></i>
                        <span data-ar="إضافة مورد جديد" data-en="Add New Supplier">إضافة مورد جديد</span>
                    </button>
                </div>
            </div>

            <!-- أدوات البحث والفلترة -->
            <div class="filters-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="البحث في الموردين..." data-ar-placeholder="البحث في الموردين..." data-en-placeholder="Search suppliers...">
                </div>
                
                <div class="filters-row">
                    <select id="serviceFilter" class="filter-select">
                        <option value="all" data-ar="جميع الخدمات" data-en="All Services">جميع الخدمات</option>
                    </select>
                    
                    <select id="cityFilter" class="filter-select">
                        <option value="all" data-ar="جميع المدن" data-en="All Cities">جميع المدن</option>
                    </select>
                    
                    <select id="statusFilter" class="filter-select">
                        <option value="all" data-ar="جميع الحالات" data-en="All Status">جميع الحالات</option>
                        <option value="active" data-ar="نشط" data-en="Active">نشط</option>
                        <option value="inactive" data-ar="غير نشط" data-en="Inactive">غير نشط</option>
                    </select>
                    
                    <button class="btn-secondary" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                        <span data-ar="مسح الفلاتر" data-en="Clear Filters">مسح الفلاتر</span>
                    </button>
                </div>
                
                <div class="export-actions">
                    <button class="btn-import" onclick="importSuppliers()">
                        <i class="fas fa-file-import"></i>
                        <span data-ar="استيراد Excel" data-en="Import Excel">استيراد Excel</span>
                    </button>

                    <button class="btn-export excel-btn" onclick="exportSuppliersExcel()">
                        <i class="fas fa-file-excel"></i>
                        <span data-ar="تصدير Excel" data-en="Export Excel">تصدير Excel</span>
                    </button>

                    <button class="btn-export" onclick="exportSuppliers('csv')">
                        <i class="fas fa-file-csv"></i>
                        <span data-ar="تصدير CSV" data-en="Export CSV">تصدير CSV</span>
                    </button>

                    <button class="btn-export" onclick="exportSuppliers('json')">
                        <i class="fas fa-file-code"></i>
                        <span data-ar="تصدير JSON" data-en="Export JSON">تصدير JSON</span>
                    </button>

                    <button class="btn-export pdf-btn" onclick="exportSuppliersPDF()">
                        <i class="fas fa-file-pdf"></i>
                        <span data-ar="تصدير PDF" data-en="Export PDF">تصدير PDF</span>
                    </button>

                    <button class="btn-export" onclick="printSuppliers()">
                        <i class="fas fa-print"></i>
                        <span data-ar="طباعة" data-en="Print">طباعة</span>
                    </button>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-row">
                <div class="stat-item">
                    <i class="fas fa-truck"></i>
                    <div class="stat-content">
                        <h3 id="totalSuppliersCount">0</h3>
                        <p data-ar="إجمالي الموردين" data-en="Total Suppliers">إجمالي الموردين</p>
                    </div>
                </div>
                
                <div class="stat-item">
                    <i class="fas fa-truck-loading"></i>
                    <div class="stat-content">
                        <h3 id="activeSuppliersCount">0</h3>
                        <p data-ar="موردين نشطون" data-en="Active Suppliers">موردين نشطون</p>
                    </div>
                </div>
                
                <div class="stat-item">
                    <i class="fas fa-tools"></i>
                    <div class="stat-content">
                        <h3 id="servicesCount">0</h3>
                        <p data-ar="أنواع الخدمات" data-en="Service Types">أنواع الخدمات</p>
                    </div>
                </div>
                
                <div class="stat-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <div class="stat-content">
                        <h3 id="citiesCount">0</h3>
                        <p data-ar="المدن المغطاة" data-en="Cities Covered">المدن المغطاة</p>
                    </div>
                </div>
            </div>

            <!-- جدول الموردين -->
            <div class="table-section">
                <div class="table-header">
                    <h3 data-ar="قائمة الموردين" data-en="Supplier List">قائمة الموردين</h3>
                    <div class="table-controls">
                        <select id="sortBy" class="sort-select">
                            <option value="name" data-ar="ترتيب حسب الاسم" data-en="Sort by Name">ترتيب حسب الاسم</option>
                            <option value="serviceType" data-ar="ترتيب حسب نوع الخدمة" data-en="Sort by Service Type">ترتيب حسب نوع الخدمة</option>
                            <option value="city" data-ar="ترتيب حسب المدينة" data-en="Sort by City">ترتيب حسب المدينة</option>
                            <option value="status" data-ar="ترتيب حسب الحالة" data-en="Sort by Status">ترتيب حسب الحالة</option>
                        </select>
                        
                        <button class="sort-order-btn" onclick="toggleSortOrder()" id="sortOrderBtn">
                            <i class="fas fa-sort-amount-down"></i>
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="customers-table" id="suppliersTable">
                        <thead>
                            <tr>
                                <th data-ar="اسم المورد" data-en="Supplier Name">اسم المورد</th>
                                <th data-ar="نوع الخدمة" data-en="Service Type">نوع الخدمة</th>
                                <th data-ar="وسائل التواصل" data-en="Contact Info">وسائل التواصل</th>
                                <th data-ar="المدينة" data-en="City">المدينة</th>
                                <th data-ar="التقييم" data-en="Rating">التقييم</th>
                                <th data-ar="الموقع الإلكتروني" data-en="Website">الموقع الإلكتروني</th>
                                <th data-ar="حالة التعاون" data-en="Cooperation Status">حالة التعاون</th>
                                <th data-ar="الإجراءات" data-en="Actions">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliersTableBody">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
                
                <div class="table-pagination" id="pagination">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
    </main>

    <!-- ملف الاستيراد المخفي -->
    <input type="file" id="importFileInput" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleFileImport(event)">

    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/suppliers.js"></script>
</body>
</html>
