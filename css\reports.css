/* ===== أنماط صفحة التقارير والتحليلات ===== */

/* أدوات التحكم في التقارير */
.reports-controls {
    background: var(--white);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f5f9;
}

.section-title {
    color: var(--primary-blue);
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.quick-actions {
    display: flex;
    gap: 15px;
}

.btn-primary-gradient {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-primary-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.btn-secondary-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary-outline:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
}

/* فلاتر التقارير */
.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.enhanced-select {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    background: var(--white);
    cursor: pointer;
    transition: var(--transition);
}

.enhanced-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* لوحة الإحصائيات السريعة */
.quick-stats-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.stat-card-modern {
    background: var(--white);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.total-revenue::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.total-customers::before {
    background: linear-gradient(90deg, #2563eb, #1d4ed8);
}

.conversion-rate::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.avg-deal-size::before {
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.stat-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon-modern {
    width: 70px;
    height: 70px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--white);
    flex-shrink: 0;
}

.total-revenue .stat-icon-modern {
    background: linear-gradient(135deg, #10b981, #059669);
}

.total-customers .stat-icon-modern {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

.conversion-rate .stat-icon-modern {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.avg-deal-size .stat-icon-modern {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-content-modern {
    flex: 1;
}

.stat-content-modern h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin: 0 0 5px 0;
}

.stat-content-modern p {
    font-size: 1.1rem;
    color: var(--gray);
    margin: 0 0 10px 0;
    font-weight: 600;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    font-weight: 600;
}

.stat-change.positive {
    color: #10b981;
}

.stat-change.negative {
    color: #ef4444;
}

/* الرسوم البيانية */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.chart-container {
    background: var(--white);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    position: relative;
    height: 400px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f1f5f9;
}

.chart-header h3 {
    color: var(--primary-blue);
    font-size: 1.2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.chart-actions {
    display: flex;
    gap: 10px;
}

.btn-chart-action {
    width: 35px;
    height: 35px;
    border: 1px solid #e2e8f0;
    background: var(--white);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--gray);
}

.btn-chart-action:hover {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
    background: #f0f9ff;
}

.chart-container canvas {
    max-height: 300px;
}

/* جداول التقارير */
.reports-tables {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.table-tabs {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 600;
    color: var(--gray);
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #f1f5f9;
    color: var(--primary-blue);
}

.tab-btn.active {
    background: var(--white);
    color: var(--primary-blue);
    border-bottom-color: var(--primary-blue);
}

.tab-content {
    padding: 25px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-header h3 {
    color: var(--primary-blue);
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0;
}

.btn-export {
    background: linear-gradient(135deg, #10b981, #059669);
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-export:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.table-container {
    overflow-x: auto;
}

.reports-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.reports-table th {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #1e40af;
}

.reports-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.reports-table tbody tr {
    transition: var(--transition);
}

.reports-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* شريط التقدم */
.progress-bar {
    position: relative;
    width: 100%;
    height: 20px;
    background: #f1f5f9;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--white);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* شارات الحالة والأداء */
.status-badge,
.performance-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-potential {
    background: #dbeafe;
    color: #1d4ed8;
}

.performance-excellent {
    background: #d1fae5;
    color: #065f46;
}

.performance-good {
    background: #dbeafe;
    color: #1d4ed8;
}

.performance-average {
    background: #fef3c7;
    color: #92400e;
}

.performance-poor {
    background: #fee2e2;
    color: #991b1b;
}

/* تصميم متجاوب */
@media (max-width: 1200px) {
    .quick-stats-dashboard {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .controls-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .quick-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .filters-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .quick-stats-dashboard {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stat-card-modern {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .stat-content-modern h3 {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 300px;
        padding: 20px;
    }
    
    .table-tabs {
        flex-direction: column;
    }
    
    .tab-content {
        padding: 20px;
    }
    
    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .reports-table {
        font-size: 0.85rem;
    }
    
    .reports-table th,
    .reports-table td {
        padding: 10px 8px;
    }
}

@media (max-width: 480px) {
    .reports-controls,
    .reports-tables {
        margin-left: -10px;
        margin-right: -10px;
        border-radius: 0;
    }
    
    .stat-icon-modern {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .stat-content-modern h3 {
        font-size: 1.8rem;
    }
    
    .chart-container {
        height: 250px;
        padding: 15px;
    }
}
