// ===== إدارة سجل الأنشطة =====

let activities = [];
let filteredActivities = [];
let currentPage = 1;
let itemsPerPage = 20;
let sortBy = 'timestamp';
let sortOrder = 'desc';

// تهيئة صفحة سجل الأنشطة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_activities') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // تهيئة البيانات
    initializeActivities();
    
    // ربط الأحداث
    bindEvents();
    
    // تحديث العرض
    updateActivitiesDisplay();
    
    // تسجيل النشاط
    logActivity('activities_view', 'عرض صفحة سجل الأنشطة');
});

// تهيئة بيانات الأنشطة
function initializeActivities() {
    // تحميل الأنشطة من التخزين المحلي
    activities = JSON.parse(localStorage.getItem('activities')) || [];
    
    // ترتيب الأنشطة حسب الوقت (الأحدث أولاً)
    activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    filteredActivities = [...activities];
}

// ربط الأحداث
function bindEvents() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
    
    // الفلاتر
    const filters = ['typeFilter', 'userFilter', 'dateFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', handleFilter);
        }
    });
    
    // الترتيب
    const sortBy = document.getElementById('sortBy');
    if (sortBy) {
        sortBy.addEventListener('change', handleSort);
    }
    
    // تحديث فلتر المستخدمين
    updateUserFilter();
}

// تحديث فلتر المستخدمين
function updateUserFilter() {
    const userFilter = document.getElementById('userFilter');
    if (!userFilter) return;
    
    // الحصول على قائمة المستخدمين الفريدة
    const users = [...new Set(activities.map(activity => activity.user).filter(user => user))];
    
    // مسح الخيارات الحالية (عدا الخيار الأول)
    const firstOption = userFilter.querySelector('option[value="all"]');
    userFilter.innerHTML = '';
    userFilter.appendChild(firstOption);
    
    // إضافة المستخدمين
    users.forEach(user => {
        const option = document.createElement('option');
        option.value = user;
        option.textContent = user;
        userFilter.appendChild(option);
    });
}

// معالج البحث
function handleSearch(event) {
    const searchTerm = event.target.value.trim();
    
    if (searchTerm === '') {
        filteredActivities = [...activities];
    } else {
        filteredActivities = searchData(activities, searchTerm, [
            'description', 'user', 'type'
        ]);
    }
    
    currentPage = 1;
    updateActivitiesDisplay();
}

// معالج الفلاتر
function handleFilter() {
    const typeFilter = document.getElementById('typeFilter').value;
    const userFilter = document.getElementById('userFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    
    filteredActivities = [...activities];
    
    // فلترة حسب النوع
    if (typeFilter !== 'all') {
        if (typeFilter === 'customer') {
            filteredActivities = filteredActivities.filter(activity => 
                activity.type.includes('customer')
            );
        } else if (typeFilter === 'supplier') {
            filteredActivities = filteredActivities.filter(activity => 
                activity.type.includes('supplier')
            );
        } else if (typeFilter === 'employee') {
            filteredActivities = filteredActivities.filter(activity => 
                activity.type.includes('telesales') || activity.type.includes('employee')
            );
        } else if (typeFilter === 'export') {
            filteredActivities = filteredActivities.filter(activity => 
                activity.type.includes('export')
            );
        } else if (typeFilter === 'import') {
            filteredActivities = filteredActivities.filter(activity => 
                activity.type.includes('import')
            );
        } else {
            filteredActivities = filteredActivities.filter(activity => 
                activity.type === typeFilter
            );
        }
    }
    
    // فلترة حسب المستخدم
    if (userFilter !== 'all') {
        filteredActivities = filteredActivities.filter(activity => 
            activity.user === userFilter
        );
    }
    
    // فلترة حسب التاريخ
    if (dateFilter !== 'all') {
        const now = new Date();
        let startDate;
        
        switch (dateFilter) {
            case 'today':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                break;
            case 'yesterday':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
                const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                filteredActivities = filteredActivities.filter(activity => {
                    const activityDate = new Date(activity.timestamp);
                    return activityDate >= startDate && activityDate < endDate;
                });
                break;
            case 'week':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                break;
        }
        
        if (dateFilter !== 'yesterday') {
            filteredActivities = filteredActivities.filter(activity => 
                new Date(activity.timestamp) >= startDate
            );
        }
    }
    
    // تطبيق البحث إذا كان موجوداً
    const searchTerm = document.getElementById('searchInput').value.trim();
    if (searchTerm) {
        filteredActivities = searchData(filteredActivities, searchTerm, [
            'description', 'user', 'type'
        ]);
    }
    
    currentPage = 1;
    updateActivitiesDisplay();
}

// معالج الترتيب
function handleSort() {
    sortBy = document.getElementById('sortBy').value;
    filteredActivities = sortData(filteredActivities, sortBy, sortOrder);
    updateActivitiesDisplay();
}

// تبديل ترتيب الفرز
function toggleSortOrder() {
    sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    
    const sortOrderBtn = document.getElementById('sortOrderBtn');
    if (sortOrderBtn) {
        const icon = sortOrderBtn.querySelector('i');
        icon.className = sortOrder === 'asc' ? 'fas fa-sort-amount-up' : 'fas fa-sort-amount-down';
    }
    
    filteredActivities = sortData(filteredActivities, sortBy, sortOrder);
    updateActivitiesDisplay();
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('typeFilter').value = 'all';
    document.getElementById('userFilter').value = 'all';
    document.getElementById('dateFilter').value = 'all';
    
    filteredActivities = [...activities];
    currentPage = 1;
    updateActivitiesDisplay();
}

// تحديث عرض الأنشطة
function updateActivitiesDisplay() {
    updateActivitiesStats();
    updateActivitiesTimeline();
    updatePagination();
}

// تحديث إحصائيات الأنشطة
function updateActivitiesStats() {
    const totalActivities = activities.length;
    
    // حساب أنشطة اليوم
    const today = new Date().toDateString();
    const todayActivities = activities.filter(activity => 
        new Date(activity.timestamp).toDateString() === today
    ).length;
    
    // حساب المستخدمين النشطين (الذين لديهم أنشطة في آخر 7 أيام)
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const activeUsers = new Set(
        activities
            .filter(activity => new Date(activity.timestamp) >= weekAgo)
            .map(activity => activity.user)
            .filter(user => user)
    ).size;
    
    // آخر نشاط
    const lastActivity = activities.length > 0 ? activities[0] : null;
    const lastActivityTime = lastActivity ? formatTime(lastActivity.timestamp) : '-';
    
    // تحديث العدادات
    updateCounter('totalActivitiesCount', totalActivities);
    updateCounter('todayActivitiesCount', todayActivities);
    updateCounter('activeUsersCount', activeUsers);
    
    const lastActivityElement = document.getElementById('lastActivityTime');
    if (lastActivityElement) {
        lastActivityElement.textContent = lastActivityTime;
    }
}

// تحديث الخط الزمني للأنشطة
function updateActivitiesTimeline() {
    const timeline = document.getElementById('activitiesTimeline');
    if (!timeline) return;
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageActivities = filteredActivities.slice(startIndex, endIndex);
    
    if (pageActivities.length === 0) {
        timeline.innerHTML = `
            <div class="no-activities">
                <i class="fas fa-history"></i>
                <p data-ar="لا توجد أنشطة" data-en="No activities found">لا توجد أنشطة</p>
            </div>
        `;
        updateLanguage();
        return;
    }
    
    // تجميع الأنشطة حسب التاريخ
    const groupedActivities = groupActivitiesByDate(pageActivities);
    
    timeline.innerHTML = Object.keys(groupedActivities).map(date => {
        const dayActivities = groupedActivities[date];
        
        return `
            <div class="timeline-day">
                <div class="timeline-date">
                    <h4>${formatDate(date)}</h4>
                    <span class="activities-count">${dayActivities.length} ${currentLanguage === 'ar' ? 'نشاط' : 'activities'}</span>
                </div>
                <div class="timeline-activities">
                    ${dayActivities.map(activity => `
                        <div class="timeline-item">
                            <div class="timeline-icon">
                                <i class="fas fa-${getActivityIcon(activity.type)}"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <h5>${activity.description}</h5>
                                    <span class="timeline-time">${formatTime(activity.timestamp)}</span>
                                </div>
                                <div class="timeline-meta">
                                    <span class="timeline-user">
                                        <i class="fas fa-user"></i>
                                        ${activity.user || 'غير معروف'}
                                    </span>
                                    <span class="timeline-type">
                                        <i class="fas fa-tag"></i>
                                        ${getActivityTypeDisplayName(activity.type)}
                                    </span>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }).join('');
}

// تجميع الأنشطة حسب التاريخ
function groupActivitiesByDate(activities) {
    const grouped = {};
    
    activities.forEach(activity => {
        const date = new Date(activity.timestamp).toDateString();
        if (!grouped[date]) {
            grouped[date] = [];
        }
        grouped[date].push(activity);
    });
    
    return grouped;
}

// الحصول على أيقونة النشاط
function getActivityIcon(type) {
    const icons = {
        'login': 'sign-in-alt',
        'logout': 'sign-out-alt',
        'customer_add': 'user-plus',
        'customer_edit': 'user-edit',
        'customer_delete': 'user-minus',
        'supplier_add': 'truck-loading',
        'supplier_edit': 'truck',
        'supplier_delete': 'truck-moving',
        'telesales_add': 'user-tie',
        'telesales_edit': 'user-cog',
        'employee_add': 'user-plus',
        'employee_edit': 'user-edit',
        'export_data': 'file-export',
        'import_data': 'file-import',
        'export_customers': 'users',
        'export_suppliers': 'truck',
        'export_employees': 'user-tie',
        'dashboard_view': 'tachometer-alt',
        'customers_view': 'users',
        'suppliers_view': 'truck',
        'telesales_view': 'headset',
        'reports_view': 'chart-bar',
        'activities_view': 'history',
        'default': 'info-circle'
    };
    
    return icons[type] || icons['default'];
}

// الحصول على اسم نوع النشاط للعرض
function getActivityTypeDisplayName(type) {
    const types = {
        'login': currentLanguage === 'ar' ? 'تسجيل دخول' : 'Login',
        'logout': currentLanguage === 'ar' ? 'تسجيل خروج' : 'Logout',
        'customer_add': currentLanguage === 'ar' ? 'إضافة عميل' : 'Add Customer',
        'customer_edit': currentLanguage === 'ar' ? 'تعديل عميل' : 'Edit Customer',
        'customer_delete': currentLanguage === 'ar' ? 'حذف عميل' : 'Delete Customer',
        'supplier_add': currentLanguage === 'ar' ? 'إضافة مورد' : 'Add Supplier',
        'supplier_edit': currentLanguage === 'ar' ? 'تعديل مورد' : 'Edit Supplier',
        'supplier_delete': currentLanguage === 'ar' ? 'حذف مورد' : 'Delete Supplier',
        'export_data': currentLanguage === 'ar' ? 'تصدير بيانات' : 'Export Data',
        'import_data': currentLanguage === 'ar' ? 'استيراد بيانات' : 'Import Data',
        'dashboard_view': currentLanguage === 'ar' ? 'عرض لوحة التحكم' : 'View Dashboard',
        'customers_view': currentLanguage === 'ar' ? 'عرض العملاء' : 'View Customers',
        'suppliers_view': currentLanguage === 'ar' ? 'عرض الموردين' : 'View Suppliers',
        'telesales_view': currentLanguage === 'ar' ? 'عرض التلي سيلز' : 'View Telesales',
        'reports_view': currentLanguage === 'ar' ? 'عرض التقارير' : 'View Reports',
        'activities_view': currentLanguage === 'ar' ? 'عرض الأنشطة' : 'View Activities'
    };
    
    return types[type] || type;
}

// تحديث ترقيم الصفحات
function updatePagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;
    
    const totalPages = Math.ceil(filteredActivities.length / itemsPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = `
        <button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // إضافة أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-dots">...</span>';
        }
    }
    
    paginationHTML += `
        <button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="pagination-info">
            ${currentPage} من ${totalPages} (${filteredActivities.length} نشاط)
        </div>
    `;
    
    pagination.innerHTML = paginationHTML;
}

// تغيير الصفحة
function changePage(page) {
    const totalPages = Math.ceil(filteredActivities.length / itemsPerPage);
    
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    updateActivitiesTimeline();
    updatePagination();
    
    // التمرير إلى أعلى القائمة
    document.querySelector('.activities-section').scrollIntoView({ behavior: 'smooth' });
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تصدير الأنشطة
function exportActivities(format) {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }
    
    const headers = [
        { key: 'type', label: 'نوع النشاط' },
        { key: 'description', label: 'الوصف' },
        { key: 'user', label: 'المستخدم' },
        { key: 'date', label: 'التاريخ' },
        { key: 'time', label: 'الوقت' }
    ];
    
    if (format === 'csv') {
        exportToCSV(filteredActivities, 'activities', headers);
    } else if (format === 'json') {
        exportToJSON(filteredActivities, 'activities');
    }
    
    logActivity('export_activities', `تصدير سجل الأنشطة بصيغة ${format.toUpperCase()}`);
}

// طباعة الأنشطة
function printActivities() {
    const headers = [
        { key: 'description', label: 'النشاط' },
        { key: 'user', label: 'المستخدم' },
        { key: 'date', label: 'التاريخ' },
        { key: 'time', label: 'الوقت' }
    ];
    
    printData('سجل الأنشطة', filteredActivities, headers);
    logActivity('print_activities', 'طباعة سجل الأنشطة');
}

// مسح سجل الأنشطة
function clearActivityLog() {
    if (!hasPermission('all')) {
        alert('ليس لديك صلاحية لمسح سجل الأنشطة');
        return;
    }
    
    if (confirm('هل أنت متأكد من مسح جميع الأنشطة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // الاحتفاظ بآخر 10 أنشطة فقط
        const recentActivities = activities.slice(0, 10);
        
        localStorage.setItem('activities', JSON.stringify(recentActivities));
        
        // إعادة تهيئة البيانات
        initializeActivities();
        updateActivitiesDisplay();
        
        // تسجيل النشاط
        logActivity('clear_activities', 'مسح سجل الأنشطة');
        
        alert('تم مسح سجل الأنشطة بنجاح');
    }
}
