// ===== إدارة فريق التلي سيلز =====

let employees = [];
let filteredEmployees = [];
let tasks = [];
let currentPage = 1;
let itemsPerPage = 10;
let sortBy = 'name';
let sortOrder = 'asc';

// تهيئة صفحة فريق التلي سيلز
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_telesales') && !hasPermission('all') && getCurrentUser().role !== 'telesales') {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // تهيئة البيانات
    initializeEmployees();
    initializeTasks();
    
    // ربط الأحداث
    bindEvents();
    
    // تحديث العرض
    updateEmployeesDisplay();
    updateTasksDisplay();
    
    // تسجيل النشاط
    logActivity('telesales_view', 'عرض صفحة إدارة فريق التلي سيلز');
});

// تهيئة بيانات الموظفين
function initializeEmployees() {
    // تحميل الموظفين من التخزين المحلي
    employees = JSON.parse(localStorage.getItem('telesales')) || [];
    
    // إضافة بيانات تجريبية إذا لم توجد بيانات
    if (employees.length === 0) {
        employees = [
            {
                id: generateId(),
                name: 'سارة أحمد المطيري',
                department: 'telesales',
                phone: '0501234567',
                email: '<EMAIL>',
                joinDate: '2024-01-15',
                performance: 'excellent',
                performanceScore: 95,
                status: 'active',
                notes: 'موظفة متميزة في التواصل مع العملاء',
                totalCalls: 150,
                successfulCalls: 120,
                createdAt: '2024-01-15',
                createdBy: 'admin'
            },
            {
                id: generateId(),
                name: 'محمد عبدالله الخالد',
                department: 'telesales',
                phone: '0507654321',
                email: '<EMAIL>',
                joinDate: '2024-01-10',
                performance: 'good',
                performanceScore: 85,
                status: 'active',
                notes: 'موظف نشيط ومتعاون',
                totalCalls: 130,
                successfulCalls: 95,
                createdAt: '2024-01-10',
                createdBy: 'admin'
            },
            {
                id: generateId(),
                name: 'فاطمة سالم العتيبي',
                department: 'customer_service',
                phone: '0551234567',
                email: '<EMAIL>',
                joinDate: '2024-01-08',
                performance: 'good',
                performanceScore: 88,
                status: 'active',
                notes: 'متخصصة في خدمة العملاء',
                totalCalls: 110,
                successfulCalls: 85,
                createdAt: '2024-01-08',
                createdBy: 'supervisor'
            },
            {
                id: generateId(),
                name: 'أحمد محمد الشهري',
                department: 'sales',
                phone: '0558765432',
                email: '<EMAIL>',
                joinDate: '2024-01-05',
                performance: 'average',
                performanceScore: 75,
                status: 'vacation',
                notes: 'في إجازة مؤقتة',
                totalCalls: 90,
                successfulCalls: 60,
                createdAt: '2024-01-05',
                createdBy: 'supervisor'
            }
        ];
        
        saveEmployees();
    }
    
    filteredEmployees = [...employees];
}

// تهيئة بيانات المهام
function initializeTasks() {
    tasks = JSON.parse(localStorage.getItem('tasks')) || [];
    
    // إضافة مهام تجريبية إذا لم توجد
    if (tasks.length === 0) {
        const today = new Date().toISOString().split('T')[0];
        
        tasks = [
            {
                id: generateId(),
                title: 'متابعة العملاء الجدد',
                description: 'التواصل مع العملاء الذين تم إضافتهم هذا الأسبوع',
                assignedTo: employees[0]?.id || '',
                assignedBy: 'admin',
                dueDate: today,
                status: 'pending',
                priority: 'high',
                createdAt: new Date().toISOString()
            },
            {
                id: generateId(),
                title: 'تحديث بيانات العملاء',
                description: 'مراجعة وتحديث بيانات العملاء في النظام',
                assignedTo: employees[1]?.id || '',
                assignedBy: 'supervisor',
                dueDate: today,
                status: 'in_progress',
                priority: 'medium',
                createdAt: new Date().toISOString()
            },
            {
                id: generateId(),
                title: 'إعداد تقرير أسبوعي',
                description: 'إعداد تقرير الأداء الأسبوعي للفريق',
                assignedTo: employees[2]?.id || '',
                assignedBy: 'admin',
                dueDate: today,
                status: 'completed',
                priority: 'low',
                createdAt: new Date().toISOString()
            }
        ];
        
        saveTasks();
    }
}

// حفظ الموظفين في التخزين المحلي
function saveEmployees() {
    localStorage.setItem('telesales', JSON.stringify(employees));
}

// حفظ المهام في التخزين المحلي
function saveTasks() {
    localStorage.setItem('tasks', JSON.stringify(tasks));
}

// ربط الأحداث
function bindEvents() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
    
    // الفلاتر
    const filters = ['departmentFilter', 'statusFilter', 'performanceFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', handleFilter);
        }
    });
    
    // الترتيب
    const sortBy = document.getElementById('sortBy');
    if (sortBy) {
        sortBy.addEventListener('change', handleSort);
    }
}

// معالج البحث
function handleSearch(event) {
    const searchTerm = event.target.value.trim();
    
    if (searchTerm === '') {
        filteredEmployees = [...employees];
    } else {
        filteredEmployees = searchData(employees, searchTerm, [
            'name', 'department', 'phone', 'email', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateEmployeesDisplay();
}

// معالج الفلاتر
function handleFilter() {
    const departmentFilter = document.getElementById('departmentFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const performanceFilter = document.getElementById('performanceFilter').value;
    
    filteredEmployees = filterData(employees, {
        department: departmentFilter,
        status: statusFilter,
        performance: performanceFilter
    });
    
    // تطبيق البحث إذا كان موجوداً
    const searchTerm = document.getElementById('searchInput').value.trim();
    if (searchTerm) {
        filteredEmployees = searchData(filteredEmployees, searchTerm, [
            'name', 'department', 'phone', 'email', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateEmployeesDisplay();
}

// معالج الترتيب
function handleSort() {
    sortBy = document.getElementById('sortBy').value;
    filteredEmployees = sortData(filteredEmployees, sortBy, sortOrder);
    updateEmployeesDisplay();
}

// تبديل ترتيب الفرز
function toggleSortOrder() {
    sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    
    const sortOrderBtn = document.getElementById('sortOrderBtn');
    if (sortOrderBtn) {
        const icon = sortOrderBtn.querySelector('i');
        icon.className = sortOrder === 'asc' ? 'fas fa-sort-amount-down' : 'fas fa-sort-amount-up';
    }
    
    filteredEmployees = sortData(filteredEmployees, sortBy, sortOrder);
    updateEmployeesDisplay();
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('departmentFilter').value = 'all';
    document.getElementById('statusFilter').value = 'all';
    document.getElementById('performanceFilter').value = 'all';
    
    filteredEmployees = [...employees];
    currentPage = 1;
    updateEmployeesDisplay();
}

// تحديث عرض الموظفين
function updateEmployeesDisplay() {
    updateEmployeesStats();
    updateEmployeesTable();
    updatePagination();
}

// تحديث إحصائيات الموظفين
function updateEmployeesStats() {
    const totalEmployees = employees.length;
    const activeEmployees = employees.filter(e => e.status === 'active').length;
    
    // حساب مهام اليوم
    const today = new Date().toDateString();
    const todayTasks = tasks.filter(t => 
        new Date(t.dueDate).toDateString() === today
    ).length;
    
    // حساب متوسط الأداء
    const avgPerformance = employees.length > 0 
        ? Math.round(employees.reduce((sum, emp) => sum + (emp.performanceScore || 0), 0) / employees.length)
        : 0;
    
    // تحديث العدادات
    updateCounter('totalEmployeesCount', totalEmployees);
    updateCounter('activeEmployeesCount', activeEmployees);
    updateCounter('todayTasksCount', todayTasks);
    
    const avgElement = document.getElementById('avgPerformanceScore');
    if (avgElement) {
        avgElement.textContent = avgPerformance + '%';
    }
    
    // تحديث شارة التنقل
    updateNavBadge('telesalesBadge', totalEmployees);
}

// تحديث جدول الموظفين
function updateEmployeesTable() {
    const tbody = document.getElementById('employeesTableBody');
    if (!tbody) return;
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageEmployees = filteredEmployees.slice(startIndex, endIndex);
    
    if (pageEmployees.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 40px; color: var(--gray);">
                    <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p data-ar="لا توجد موظفين" data-en="No employees found">لا توجد موظفين</p>
                </td>
            </tr>
        `;
        updateLanguage();
        return;
    }
    
    tbody.innerHTML = pageEmployees.map(employee => `
        <tr>
            <td><strong>${employee.name}</strong></td>
            <td>${getDepartmentDisplayName(employee.department)}</td>
            <td>${employee.phone}</td>
            <td>${employee.email}</td>
            <td>${formatDate(employee.joinDate)}</td>
            <td>
                <div class="performance-indicator">
                    <span class="performance-badge performance-${employee.performance}">
                        ${getPerformanceDisplayName(employee.performance)}
                    </span>
                    <span class="performance-score">${employee.performanceScore || 0}%</span>
                </div>
            </td>
            <td>
                <span class="status-badge status-${employee.status}">
                    ${getStatusDisplayName(employee.status)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewEmployee('${employee.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${hasPermission('edit_telesales') || hasPermission('all') ? `
                        <button class="action-btn edit" onclick="editEmployee('${employee.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : ''}
                    <button class="action-btn tasks" onclick="viewEmployeeTasks('${employee.id}')" title="المهام">
                        <i class="fas fa-tasks"></i>
                    </button>
                    ${hasPermission('all') ? `
                        <button class="action-btn delete" onclick="deleteEmployee('${employee.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// الحصول على اسم القسم للعرض
function getDepartmentDisplayName(department) {
    const departments = {
        'telesales': currentLanguage === 'ar' ? 'تلي سيلز' : 'Telesales',
        'customer_service': currentLanguage === 'ar' ? 'خدمة العملاء' : 'Customer Service',
        'sales': currentLanguage === 'ar' ? 'المبيعات' : 'Sales'
    };
    
    return departments[department] || department;
}

// الحصول على اسم الأداء للعرض
function getPerformanceDisplayName(performance) {
    const performances = {
        'excellent': currentLanguage === 'ar' ? 'ممتاز' : 'Excellent',
        'good': currentLanguage === 'ar' ? 'جيد' : 'Good',
        'average': currentLanguage === 'ar' ? 'متوسط' : 'Average',
        'poor': currentLanguage === 'ar' ? 'ضعيف' : 'Poor'
    };
    
    return performances[performance] || performance;
}

// الحصول على اسم الحالة للعرض
function getStatusDisplayName(status) {
    const statuses = {
        'active': currentLanguage === 'ar' ? 'نشط' : 'Active',
        'inactive': currentLanguage === 'ar' ? 'غير نشط' : 'Inactive',
        'vacation': currentLanguage === 'ar' ? 'في إجازة' : 'On Vacation'
    };
    
    return statuses[status] || status;
}

// تحديث عرض المهام
function updateTasksDisplay() {
    const tasksGrid = document.getElementById('tasksGrid');
    if (!tasksGrid) return;
    
    const today = new Date().toDateString();
    const todayTasks = tasks.filter(task => 
        new Date(task.dueDate).toDateString() === today
    );
    
    if (todayTasks.length === 0) {
        tasksGrid.innerHTML = `
            <div class="no-tasks">
                <i class="fas fa-tasks"></i>
                <p data-ar="لا توجد مهام لهذا اليوم" data-en="No tasks for today">لا توجد مهام لهذا اليوم</p>
            </div>
        `;
        updateLanguage();
        return;
    }
    
    tasksGrid.innerHTML = todayTasks.map(task => {
        const assignedEmployee = employees.find(emp => emp.id === task.assignedTo);
        return `
            <div class="task-card task-${task.status}">
                <div class="task-header">
                    <h4>${task.title}</h4>
                    <span class="task-priority priority-${task.priority}">
                        ${getPriorityDisplayName(task.priority)}
                    </span>
                </div>
                <p class="task-description">${task.description}</p>
                <div class="task-footer">
                    <div class="task-assignee">
                        <i class="fas fa-user"></i>
                        ${assignedEmployee ? assignedEmployee.name : 'غير محدد'}
                    </div>
                    <div class="task-status">
                        <span class="status-badge status-${task.status}">
                            ${getTaskStatusDisplayName(task.status)}
                        </span>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// الحصول على اسم الأولوية للعرض
function getPriorityDisplayName(priority) {
    const priorities = {
        'high': currentLanguage === 'ar' ? 'عالية' : 'High',
        'medium': currentLanguage === 'ar' ? 'متوسطة' : 'Medium',
        'low': currentLanguage === 'ar' ? 'منخفضة' : 'Low'
    };
    
    return priorities[priority] || priority;
}

// الحصول على اسم حالة المهمة للعرض
function getTaskStatusDisplayName(status) {
    const statuses = {
        'pending': currentLanguage === 'ar' ? 'معلقة' : 'Pending',
        'in_progress': currentLanguage === 'ar' ? 'قيد التنفيذ' : 'In Progress',
        'completed': currentLanguage === 'ar' ? 'مكتملة' : 'Completed',
        'cancelled': currentLanguage === 'ar' ? 'ملغية' : 'Cancelled'
    };
    
    return statuses[status] || status;
}

// تحديث ترقيم الصفحات
function updatePagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;
    
    const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = `
        <button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // إضافة أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-dots">...</span>';
        }
    }
    
    paginationHTML += `
        <button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="pagination-info">
            ${currentPage} من ${totalPages} (${filteredEmployees.length} موظف)
        </div>
    `;
    
    pagination.innerHTML = paginationHTML;
}

// تغيير الصفحة
function changePage(page) {
    const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);
    
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    updateEmployeesTable();
    updatePagination();
    
    // التمرير إلى أعلى الجدول
    document.querySelector('.table-section').scrollIntoView({ behavior: 'smooth' });
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تصدير الموظفين
function exportEmployees(format) {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'department', label: 'القسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'email', label: 'البريد الإلكتروني' },
        { key: 'joinDate', label: 'تاريخ الانضمام' },
        { key: 'performance', label: 'الأداء' },
        { key: 'performanceScore', label: 'نقاط الأداء' },
        { key: 'status', label: 'الحالة' }
    ];
    
    if (format === 'csv') {
        exportToCSV(filteredEmployees, 'employees', headers);
    } else if (format === 'json') {
        exportToJSON(filteredEmployees, 'employees');
    }
    
    logActivity('export_employees', `تصدير بيانات الموظفين بصيغة ${format.toUpperCase()}`);
}

// طباعة الموظفين
function printEmployees() {
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'department', label: 'القسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'email', label: 'البريد الإلكتروني' },
        { key: 'performance', label: 'الأداء' },
        { key: 'status', label: 'الحالة' }
    ];
    
    printData('قائمة الموظفين', filteredEmployees, headers);
    logActivity('print_employees', 'طباعة قائمة الموظفين');
}

// استيراد الموظفين
function importEmployees() {
    if (!hasPermission('add_telesales') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لاستيراد البيانات');
        return;
    }
    
    const fileInput = document.getElementById('importFileInput');
    fileInput.click();
}

// معالجة ملف الاستيراد
function handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const fileName = file.name.toLowerCase();
    
    if (fileName.endsWith('.csv')) {
        importFromCSV(file);
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        alert('استيراد ملفات Excel قيد التطوير. يرجى استخدام ملف CSV حالياً.');
    } else {
        alert('نوع الملف غير مدعوم. يرجى اختيار ملف CSV أو Excel.');
    }
    
    // مسح قيمة input
    event.target.value = '';
}

// استيراد من CSV
function importFromCSV(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
            
            const importedEmployees = [];
            
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;
                
                const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
                
                if (values.length >= 6) {
                    const employee = {
                        id: generateId(),
                        name: values[0] || '',
                        department: values[1] || 'telesales',
                        phone: values[2] || '',
                        email: values[3] || '',
                        joinDate: values[4] || new Date().toISOString().split('T')[0],
                        performance: values[5] || 'average',
                        performanceScore: parseInt(values[6]) || 75,
                        status: values[7] || 'active',
                        notes: values[8] || '',
                        totalCalls: 0,
                        successfulCalls: 0,
                        createdAt: new Date().toISOString(),
                        createdBy: getCurrentUser().username
                    };
                    
                    importedEmployees.push(employee);
                }
            }
            
            if (importedEmployees.length > 0) {
                employees.push(...importedEmployees);
                saveEmployees();
                filteredEmployees = [...employees];
                updateEmployeesDisplay();
                
                alert(`تم استيراد ${importedEmployees.length} موظف بنجاح`);
                logActivity('import_employees', `استيراد ${importedEmployees.length} موظف من ملف CSV`);
            } else {
                alert('لم يتم العثور على بيانات صالحة في الملف');
            }
            
        } catch (error) {
            console.error('خطأ في استيراد الملف:', error);
            alert('حدث خطأ أثناء استيراد الملف. يرجى التأكد من تنسيق الملف.');
        }
    };
    
    reader.readAsText(file, 'UTF-8');
}

// عرض نافذة إضافة موظف جديد
function showAddEmployeeModal() {
    if (!hasPermission('add_telesales') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لإضافة موظفين جدد');
        return;
    }
    
    alert('نافذة إضافة موظف جديد قيد التطوير');
}

// عرض نافذة إضافة مهمة جديدة
function showAddTaskModal() {
    if (!hasPermission('add_tasks') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لإضافة مهام جديدة');
        return;
    }
    
    alert('نافذة إضافة مهمة جديدة قيد التطوير');
}

// عرض تفاصيل الموظف
function viewEmployee(employeeId) {
    alert('عرض تفاصيل الموظف قيد التطوير');
}

// تعديل الموظف
function editEmployee(employeeId) {
    if (!hasPermission('edit_telesales') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتعديل الموظفين');
        return;
    }
    
    alert('تعديل الموظف قيد التطوير');
}

// عرض مهام الموظف
function viewEmployeeTasks(employeeId) {
    alert('عرض مهام الموظف قيد التطوير');
}

// حذف الموظف
function deleteEmployee(employeeId) {
    if (!hasPermission('all')) {
        alert('ليس لديك صلاحية لحذف الموظفين');
        return;
    }
    
    alert('حذف الموظف قيد التطوير');
}
