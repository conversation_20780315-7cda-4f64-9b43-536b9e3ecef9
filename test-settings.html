<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإعدادات - نظام إدارة العملاء والموردين</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div style="padding: 20px; max-width: 800px; margin: 0 auto;">
        <h1>اختبار الإعدادات والملف الشخصي</h1>
        
        <div style="margin: 20px 0;">
            <h2>الأزرار التجريبية:</h2>
            <button class="btn-primary" onclick="testSettings()" style="margin: 10px;">
                <i class="fas fa-cog"></i>
                اختبار صفحة الإعدادات
            </button>
            
            <button class="btn-secondary" onclick="testProfile()" style="margin: 10px;">
                <i class="fas fa-user"></i>
                اختبار الملف الشخصي
            </button>
            
            <button class="btn-export" onclick="testThemes()" style="margin: 10px;">
                <i class="fas fa-palette"></i>
                اختبار الثيمات
            </button>
        </div>
        
        <div style="margin: 20px 0;">
            <h2>معاينة الثيمات:</h2>
            <div style="display: flex; gap: 20px; margin: 20px 0;">
                <div class="theme-preview-card" onclick="applyTheme('blue')">
                    <h3>الثيم الأزرق</h3>
                    <div style="background: #2563eb; width: 50px; height: 20px; margin: 5px 0;"></div>
                    <div style="background: #3b82f6; width: 50px; height: 20px; margin: 5px 0;"></div>
                    <div style="background: #1d4ed8; width: 50px; height: 20px; margin: 5px 0;"></div>
                </div>
                
                <div class="theme-preview-card" onclick="applyTheme('green')">
                    <h3>الثيم الأخضر</h3>
                    <div style="background: #059669; width: 50px; height: 20px; margin: 5px 0;"></div>
                    <div style="background: #10b981; width: 50px; height: 20px; margin: 5px 0;"></div>
                    <div style="background: #047857; width: 50px; height: 20px; margin: 5px 0;"></div>
                </div>
                
                <div class="theme-preview-card" onclick="applyTheme('purple')">
                    <h3>الثيم البنفسجي</h3>
                    <div style="background: #7c3aed; width: 50px; height: 20px; margin: 5px 0;"></div>
                    <div style="background: #8b5cf6; width: 50px; height: 20px; margin: 5px 0;"></div>
                    <div style="background: #6d28d9; width: 50px; height: 20px; margin: 5px 0;"></div>
                </div>
            </div>
        </div>
        
        <div style="margin: 20px 0;">
            <h2>اختبار أحجام الخط:</h2>
            <button onclick="changeFontSize('small')" style="margin: 5px;">صغير</button>
            <button onclick="changeFontSize('medium')" style="margin: 5px;">متوسط</button>
            <button onclick="changeFontSize('large')" style="margin: 5px;">كبير</button>
        </div>
        
        <div style="margin: 20px 0;">
            <h2>اختبار كثافة البيانات:</h2>
            <button onclick="changeDensity('compact')" style="margin: 5px;">مضغوط</button>
            <button onclick="changeDensity('comfortable')" style="margin: 5px;">مريح</button>
            <button onclick="changeDensity('spacious')" style="margin: 5px;">واسع</button>
        </div>
        
        <div style="margin: 20px 0;">
            <h2>جدول تجريبي:</h2>
            <table class="customers-table" style="width: 100%;">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>أحمد محمد</td>
                        <td><EMAIL></td>
                        <td>0501234567</td>
                        <td><span class="status-badge status-active">نشط</span></td>
                    </tr>
                    <tr>
                        <td>فاطمة أحمد</td>
                        <td><EMAIL></td>
                        <td>0507654321</td>
                        <td><span class="status-badge status-potential">محتمل</span></td>
                    </tr>
                    <tr>
                        <td>محمد عبدالله</td>
                        <td><EMAIL></td>
                        <td>0551234567</td>
                        <td><span class="status-badge status-inactive">غير نشط</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div style="margin: 20px 0;">
            <h2>روابط التنقل:</h2>
            <a href="index.html" class="btn-secondary" style="margin: 5px; text-decoration: none;">
                <i class="fas fa-home"></i>
                الصفحة الرئيسية
            </a>
            <a href="settings.html" class="btn-primary" style="margin: 5px; text-decoration: none;">
                <i class="fas fa-cog"></i>
                صفحة الإعدادات
            </a>
        </div>
    </div>

    <script>
        // إعداد مستخدم تجريبي
        if (!localStorage.getItem('currentUser')) {
            const testUser = {
                username: 'test',
                name: 'مستخدم تجريبي',
                role: 'admin',
                email: '<EMAIL>',
                phone: '0501234567',
                loginTime: new Date().toISOString(),
                avatar: null
            };
            localStorage.setItem('currentUser', JSON.stringify(testUser));
        }

        function testSettings() {
            alert('سيتم فتح صفحة الإعدادات...');
            window.location.href = 'settings.html';
        }

        function testProfile() {
            // محاكاة دالة showProfile
            alert('سيتم فتح الملف الشخصي...\nهذه محاكاة - يجب تشغيلها من صفحة تحتوي على dashboard.js');
        }

        function testThemes() {
            alert('جرب النقر على معاينات الثيمات أدناه لتغيير الألوان');
        }

        function applyTheme(theme) {
            const root = document.documentElement;
            
            switch (theme) {
                case 'blue':
                    root.style.setProperty('--primary-blue', '#2563eb');
                    root.style.setProperty('--secondary-blue', '#3b82f6');
                    root.style.setProperty('--accent-blue', '#1d4ed8');
                    break;
                case 'green':
                    root.style.setProperty('--primary-blue', '#059669');
                    root.style.setProperty('--secondary-blue', '#10b981');
                    root.style.setProperty('--accent-blue', '#047857');
                    break;
                case 'purple':
                    root.style.setProperty('--primary-blue', '#7c3aed');
                    root.style.setProperty('--secondary-blue', '#8b5cf6');
                    root.style.setProperty('--accent-blue', '#6d28d9');
                    break;
            }
            
            alert(`تم تطبيق الثيم ${theme === 'blue' ? 'الأزرق' : theme === 'green' ? 'الأخضر' : 'البنفسجي'}`);
        }

        function changeFontSize(size) {
            const root = document.documentElement;
            switch (size) {
                case 'small':
                    root.style.setProperty('--base-font-size', '14px');
                    break;
                case 'medium':
                    root.style.setProperty('--base-font-size', '16px');
                    break;
                case 'large':
                    root.style.setProperty('--base-font-size', '18px');
                    break;
            }
            
            alert(`تم تغيير حجم الخط إلى ${size === 'small' ? 'صغير' : size === 'medium' ? 'متوسط' : 'كبير'}`);
        }

        function changeDensity(density) {
            const body = document.body;
            body.classList.remove('density-compact', 'density-comfortable', 'density-spacious');
            body.classList.add(`density-${density}`);
            
            alert(`تم تغيير كثافة البيانات إلى ${density === 'compact' ? 'مضغوط' : density === 'comfortable' ? 'مريح' : 'واسع'}`);
        }
    </script>

    <style>
        .theme-preview-card {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .theme-preview-card:hover {
            border-color: var(--primary-blue);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .theme-preview-card h3 {
            margin: 0 0 10px 0;
            color: var(--dark-gray);
        }
    </style>
</body>
</html>
