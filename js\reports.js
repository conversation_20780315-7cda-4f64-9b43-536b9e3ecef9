// ===== إدارة التقارير والتحليلات =====

let reportsData = {};
let charts = {};
let currentDateRange = 'month';
let currentReportType = 'overview';

// تهيئة صفحة التقارير
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;

    if (!hasPermission('view_reports') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }

    // تهيئة البيانات
    initializeReportsData();

    // تحميل التقارير
    loadReports();

    // تهيئة الرسوم البيانية
    initializeCharts();

    // ربط الأحداث
    bindReportEvents();

    // تسجيل النشاط
    logActivity('reports_view', 'عرض صفحة التقارير والتحليلات');
});

// تهيئة بيانات التقارير
function initializeReportsData() {
    // تحميل البيانات من التخزين المحلي
    const customers = JSON.parse(localStorage.getItem('customers')) || [];
    const suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
    const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
    const employees = JSON.parse(localStorage.getItem('employees')) || [];
    const activities = JSON.parse(localStorage.getItem('activities')) || [];

    reportsData = {
        customers,
        suppliers,
        quotations,
        employees,
        activities
    };
}

// تحميل التقارير
function loadReports() {
    updateQuickStats();
    updateReportsTables();
    updateCharts();
}

// تحديث الإحصائيات السريعة
function updateQuickStats() {
    const { customers, quotations } = reportsData;

    // حساب الإيرادات الإجمالية
    const totalRevenue = quotations
        .filter(q => q.status === 'accepted')
        .reduce((sum, q) => sum + (q.totalAmount || 0), 0);

    // عدد العملاء
    const totalCustomers = customers.length;

    // معدل التحويل
    const totalQuotations = quotations.length;
    const acceptedQuotations = quotations.filter(q => q.status === 'accepted').length;
    const conversionRate = totalQuotations > 0 ? (acceptedQuotations / totalQuotations * 100).toFixed(1) : 0;

    // متوسط حجم الصفقة
    const avgDealSize = acceptedQuotations > 0 ? (totalRevenue / acceptedQuotations).toFixed(0) : 0;

    // تحديث العناصر
    updateElement('totalRevenue', formatCurrency(totalRevenue));
    updateElement('totalCustomersCount', totalCustomers);
    updateElement('conversionRate', conversionRate + '%');
    updateElement('avgDealSize', formatCurrency(avgDealSize));
}

// تحديث جداول التقارير
function updateReportsTables() {
    updateCustomersReportTable();
    updateQuotationsReportTable();
    updatePerformanceReportTable();
}

// تحديث جدول تقرير العملاء
function updateCustomersReportTable() {
    const { customers, quotations } = reportsData;
    const tbody = document.getElementById('customersReportBody');

    if (!tbody) return;

    const customerReports = customers.map(customer => {
        const customerQuotations = quotations.filter(q => q.customerId === customer.id);
        const totalValue = customerQuotations.reduce((sum, q) => sum + (q.totalAmount || 0), 0);
        const lastActivity = customerQuotations.length > 0
            ? Math.max(...customerQuotations.map(q => new Date(q.createdAt).getTime()))
            : new Date(customer.createdAt).getTime();

        return {
            ...customer,
            quotationsCount: customerQuotations.length,
            totalValue,
            lastActivity: new Date(lastActivity)
        };
    });

    tbody.innerHTML = customerReports.map(customer => `
        <tr>
            <td><strong>${customer.name}</strong></td>
            <td>${customer.city || 'غير محدد'}</td>
            <td>${customer.quotationsCount}</td>
            <td><strong>${formatCurrency(customer.totalValue)}</strong></td>
            <td>${formatDate(customer.lastActivity)}</td>
            <td>
                <span class="status-badge status-${customer.status}">
                    ${getStatusDisplayName(customer.status)}
                </span>
            </td>
        </tr>
    `).join('');
}

// تحديث جدول تقرير عروض الأسعار
function updateQuotationsReportTable() {
    const { quotations } = reportsData;
    const tbody = document.getElementById('quotationsReportBody');

    if (!tbody) return;

    tbody.innerHTML = quotations.map(quotation => {
        const successRate = calculateQuotationSuccessRate(quotation);

        return `
            <tr>
                <td><strong>${quotation.quoteNumber}</strong></td>
                <td>${quotation.customerName}</td>
                <td>${formatDate(quotation.quoteDate)}</td>
                <td><strong>${formatCurrency(quotation.totalAmount)}</strong></td>
                <td>
                    <span class="status-badge status-${quotation.status}">
                        ${getQuotationStatusDisplayName(quotation.status)}
                    </span>
                </td>
                <td>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${successRate}%"></div>
                        <span class="progress-text">${successRate}%</span>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديث جدول تقرير الأداء
function updatePerformanceReportTable() {
    const { employees } = reportsData;
    const tbody = document.getElementById('performanceReportBody');

    if (!tbody) return;

    tbody.innerHTML = employees.map(employee => `
        <tr>
            <td><strong>${employee.name}</strong></td>
            <td>${getDepartmentDisplayName(employee.department)}</td>
            <td>${employee.totalCalls || 0}</td>
            <td>${employee.successfulCalls || 0}</td>
            <td>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${employee.achievementRate || 0}%"></div>
                    <span class="progress-text">${employee.achievementRate || 0}%</span>
                </div>
            </td>
            <td>
                <span class="performance-badge performance-${employee.performance}">
                    ${getPerformanceDisplayName(employee.performance)}
                </span>
            </td>
        </tr>
    `).join('');
}

// تهيئة الرسوم البيانية
function initializeCharts() {
    initializeSalesTrendChart();
    initializeCustomerDistributionChart();
}

// رسم بياني لاتجاه المبيعات
function initializeSalesTrendChart() {
    const ctx = document.getElementById('salesTrendChart');
    if (!ctx) return;

    const { quotations } = reportsData;

    // تجميع البيانات حسب الشهر
    const monthlyData = {};
    quotations.forEach(quotation => {
        if (quotation.status === 'accepted') {
            const month = new Date(quotation.quoteDate).toISOString().slice(0, 7);
            monthlyData[month] = (monthlyData[month] || 0) + (quotation.totalAmount || 0);
        }
    });

    const labels = Object.keys(monthlyData).sort();
    const data = labels.map(label => monthlyData[label]);

    charts.salesTrend = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels.map(label => formatMonthLabel(label)),
            datasets: [{
                label: 'المبيعات (ريال)',
                data: data,
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });
}

// رسم بياني لتوزيع العملاء
function initializeCustomerDistributionChart() {
    const ctx = document.getElementById('customerDistributionChart');
    if (!ctx) return;

    const { customers } = reportsData;

    // تجميع العملاء حسب المدينة
    const cityData = {};
    customers.forEach(customer => {
        const city = customer.city || 'غير محدد';
        cityData[city] = (cityData[city] || 0) + 1;
    });

    const labels = Object.keys(cityData);
    const data = Object.values(cityData);
    const colors = [
        '#2563eb', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
        '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6b7280'
    ];

    charts.customerDistribution = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// تحديث التقارير
function updateReports() {
    currentDateRange = document.getElementById('dateRange').value;
    currentReportType = document.getElementById('reportType').value;

    // إعادة تحميل البيانات مع الفلاتر الجديدة
    loadReports();

    showNotification('تم تحديث التقارير بنجاح', 'success');
}

// إنشاء تقرير
function generateReport() {
    showNotification('جاري إنشاء التقرير المخصص...', 'info');

    // محاكاة إنشاء التقرير
    setTimeout(() => {
        showNotification('تم إنشاء التقرير بنجاح', 'success');
    }, 2000);
}

// تصدير جميع التقارير
function exportAllReports() {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }

    showNotification('جاري تصدير جميع التقارير...', 'info');

    // محاكاة التصدير
    setTimeout(() => {
        showNotification('تم تصدير جميع التقارير بنجاح', 'success');
    }, 3000);
}

// تبديل التبويبات
function switchTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.style.display = 'none';
    });

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // إظهار التبويب المحدد
    const targetTab = document.getElementById(tabName + 'Tab');
    if (targetTab) {
        targetTab.style.display = 'block';
    }

    // إضافة الفئة النشطة للزر المحدد
    event.target.classList.add('active');
}

// ربط الأحداث
function bindReportEvents() {
    // فلاتر التقارير
    const dateRange = document.getElementById('dateRange');
    const reportType = document.getElementById('reportType');
    const cityFilter = document.getElementById('cityFilter');
    const statusFilter = document.getElementById('statusFilter');

    if (dateRange) dateRange.addEventListener('change', updateReports);
    if (reportType) reportType.addEventListener('change', updateReports);
    if (cityFilter) cityFilter.addEventListener('change', updateReports);
    if (statusFilter) statusFilter.addEventListener('change', updateReports);
}

// دوال مساعدة
function formatMonthLabel(monthString) {
    const date = new Date(monthString + '-01');
    return date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
}

function calculateQuotationSuccessRate(quotation) {
    // حساب معدل نجاح العرض بناءً على عوامل مختلفة
    let rate = 50; // معدل أساسي

    if (quotation.status === 'accepted') rate = 100;
    else if (quotation.status === 'rejected') rate = 0;
    else if (quotation.status === 'expired') rate = 10;
    else if (quotation.status === 'sent') rate = 70;

    return rate;
}

function getStatusDisplayName(status) {
    const statuses = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'pending': 'معلق',
        'potential': 'محتمل'
    };
    return statuses[status] || status;
}

function getQuotationStatusDisplayName(status) {
    const statuses = {
        'draft': 'مسودة',
        'sent': 'مرسل',
        'accepted': 'مقبول',
        'rejected': 'مرفوض',
        'expired': 'منتهي الصلاحية'
    };
    return statuses[status] || status;
}

function getDepartmentDisplayName(department) {
    const departments = {
        'telesales': 'التلي سيلز',
        'sales': 'المبيعات',
        'marketing': 'التسويق',
        'support': 'الدعم الفني',
        'management': 'الإدارة'
    };
    return departments[department] || department;
}

function getPerformanceDisplayName(performance) {
    const performances = {
        'excellent': 'ممتاز',
        'good': 'جيد',
        'average': 'متوسط',
        'poor': 'ضعيف'
    };
    return performances[performance] || performance;
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

function formatDate(date) {
    return new Date(date).toLocaleDateString('ar-SA');
}

// دوال التصدير
function exportCustomersReport() {
    showNotification('جاري تصدير تقرير العملاء...', 'info');

    const { customers } = reportsData;
    const csvContent = generateCSV(customers, [
        { key: 'name', label: 'اسم العميل' },
        { key: 'city', label: 'المدينة' },
        { key: 'phone', label: 'رقم الهاتف' },
        { key: 'email', label: 'البريد الإلكتروني' },
        { key: 'status', label: 'الحالة' }
    ]);

    downloadFile(csvContent, 'customers_report.csv', 'text/csv');
    logActivity('export_customers_report', 'تصدير تقرير العملاء');
}

function exportQuotationsReport() {
    showNotification('جاري تصدير تقرير عروض الأسعار...', 'info');

    const { quotations } = reportsData;
    const csvContent = generateCSV(quotations, [
        { key: 'quoteNumber', label: 'رقم العرض' },
        { key: 'customerName', label: 'العميل' },
        { key: 'quoteDate', label: 'تاريخ العرض' },
        { key: 'totalAmount', label: 'القيمة الإجمالية' },
        { key: 'status', label: 'الحالة' }
    ]);

    downloadFile(csvContent, 'quotations_report.csv', 'text/csv');
    logActivity('export_quotations_report', 'تصدير تقرير عروض الأسعار');
}

function exportPerformanceReport() {
    showNotification('جاري تصدير تقرير الأداء...', 'info');

    const { employees } = reportsData;
    const csvContent = generateCSV(employees, [
        { key: 'name', label: 'اسم الموظف' },
        { key: 'department', label: 'القسم' },
        { key: 'totalCalls', label: 'عدد المكالمات' },
        { key: 'successfulCalls', label: 'المكالمات الناجحة' },
        { key: 'achievementRate', label: 'معدل النجاح' },
        { key: 'performance', label: 'التقييم' }
    ]);

    downloadFile(csvContent, 'performance_report.csv', 'text/csv');
    logActivity('export_performance_report', 'تصدير تقرير الأداء');
}

function exportChart(chartName) {
    showNotification(`جاري تصدير الرسم البياني: ${chartName}`, 'info');

    const chart = charts[chartName];
    if (chart) {
        const url = chart.toBase64Image();
        const link = document.createElement('a');
        link.download = `${chartName}_chart.png`;
        link.href = url;
        link.click();

        logActivity('export_chart', `تصدير الرسم البياني: ${chartName}`);
    }
}

function fullscreenChart(chartName) {
    showNotification(`عرض الرسم البياني بملء الشاشة: ${chartName}`, 'info');
    // يمكن تطبيق منطق العرض بملء الشاشة هنا
}

// دالة مساعدة لإنشاء CSV
function generateCSV(data, headers) {
    const csvRows = [];

    // إضافة الرؤوس
    csvRows.push(headers.map(h => h.label).join(','));

    // إضافة البيانات
    data.forEach(item => {
        const row = headers.map(header => {
            const value = item[header.key] || '';
            return `"${value}"`;
        });
        csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
}

// دالة تحميل الملف
function downloadFile(content, fileName, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    URL.revokeObjectURL(url);
}

// معالج تغيير النطاق الزمني
function handleDateRangeChange() {
    const dateRange = document.getElementById('dateRange').value;
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    
    if (dateRange === 'custom') {
        startDateInput.style.display = 'block';
        endDateInput.style.display = 'block';
    } else {
        startDateInput.style.display = 'none';
        endDateInput.style.display = 'none';
        
        // تحديد التواريخ حسب النطاق المحدد
        const now = new Date();
        endDate = new Date(now);
        
        switch (dateRange) {
            case 'today':
                startDate = new Date(now);
                break;
            case 'week':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                break;
            case 'quarter':
                const quarter = Math.floor(now.getMonth() / 3);
                startDate = new Date(now.getFullYear(), quarter * 3, 1);
                break;
            case 'year':
                startDate = new Date(now.getFullYear(), 0, 1);
                break;
        }
    }
    
    currentDateRange = dateRange;
    updateReports();
}

// تحديث التقارير
function updateReports() {
    // تحديث البيانات
    initializeReportData();
    
    // تحديث التواريخ المخصصة إذا لزم الأمر
    if (currentDateRange === 'custom') {
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        
        if (startDateInput.value) {
            startDate = new Date(startDateInput.value);
        }
        
        if (endDateInput.value) {
            endDate = new Date(endDateInput.value);
        }
    }
    
    // تحديث الإحصائيات العامة
    updateGeneralStats();
    
    // تحديث تقارير محددة
    updateCustomerReport();
    updateSupplierReport();
    updatePerformanceReport();
    updateActivityReport();
    
    // تسجيل النشاط
    logActivity('reports_update', 'تحديث التقارير');
}

// تحديث الإحصائيات العامة
function updateGeneralStats() {
    const filteredData = filterDataByDateRange();
    
    updateCounter('totalCustomersReport', reportData.customers.length);
    updateCounter('totalSuppliersReport', reportData.suppliers.length);
    updateCounter('totalEmployeesReport', reportData.employees.length);
    updateCounter('totalActivitiesReport', filteredData.activities.length);
}

// تحديث تقرير العملاء
function updateCustomerReport() {
    const customers = reportData.customers;
    const filteredData = filterDataByDateRange();
    
    const activeCustomers = customers.filter(c => c.status === 'active').length;
    const potentialCustomers = customers.filter(c => c.status === 'potential').length;
    const newCustomers = filteredData.customers.length;
    
    updateCounter('activeCustomersReport', activeCustomers);
    updateCounter('potentialCustomersReport', potentialCustomers);
    updateCounter('newCustomersReport', newCustomers);
}

// تحديث تقرير الموردين
function updateSupplierReport() {
    const suppliers = reportData.suppliers;
    
    const activeSuppliers = suppliers.filter(s => s.status === 'active').length;
    const serviceTypes = [...new Set(suppliers.map(s => s.serviceType))].length;
    const citiesCovered = [...new Set(suppliers.map(s => s.city))].length;
    
    updateCounter('activeSuppliersReport', activeSuppliers);
    updateCounter('serviceTypesReport', serviceTypes);
    updateCounter('citiesCoveredReport', citiesCovered);
}

// تحديث تقرير الأداء
function updatePerformanceReport() {
    const employees = reportData.employees;
    const tasks = reportData.tasks;
    
    // حساب متوسط الأداء
    const avgPerformance = employees.length > 0 
        ? Math.round(employees.reduce((sum, emp) => sum + (emp.performanceScore || 0), 0) / employees.length)
        : 0;
    
    // حساب الموظفين المتميزين (أداء أكثر من 90%)
    const topPerformers = employees.filter(emp => (emp.performanceScore || 0) >= 90).length;
    
    // حساب المهام المكتملة
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    
    const avgElement = document.getElementById('avgPerformanceReport');
    if (avgElement) {
        avgElement.textContent = avgPerformance + '%';
    }
    
    updateCounter('topPerformersReport', topPerformers);
    updateCounter('completedTasksReport', completedTasks);
}

// تحديث تقرير الأنشطة
function updateActivityReport() {
    const activities = reportData.activities;
    const now = new Date();
    
    // أنشطة اليوم
    const today = now.toDateString();
    const todayActivities = activities.filter(activity => 
        new Date(activity.timestamp).toDateString() === today
    ).length;
    
    // أنشطة هذا الأسبوع
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const weekActivities = activities.filter(activity => 
        new Date(activity.timestamp) >= weekAgo
    ).length;
    
    // أكثر المستخدمين نشاطاً
    const userActivities = {};
    activities.forEach(activity => {
        if (activity.user) {
            userActivities[activity.user] = (userActivities[activity.user] || 0) + 1;
        }
    });
    
    const mostActiveUser = Object.keys(userActivities).reduce((a, b) => 
        userActivities[a] > userActivities[b] ? a : b, '-'
    );
    
    updateCounter('todayActivitiesReport', todayActivities);
    updateCounter('weekActivitiesReport', weekActivities);
    
    const mostActiveElement = document.getElementById('mostActiveUserReport');
    if (mostActiveElement) {
        mostActiveElement.textContent = mostActiveUser;
    }
}

// فلترة البيانات حسب النطاق الزمني
function filterDataByDateRange() {
    if (!startDate || !endDate) {
        return {
            customers: reportData.customers,
            suppliers: reportData.suppliers,
            employees: reportData.employees,
            activities: reportData.activities,
            tasks: reportData.tasks
        };
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999); // نهاية اليوم
    
    return {
        customers: reportData.customers.filter(item => {
            const date = new Date(item.createdAt || item.lastContact);
            return date >= start && date <= end;
        }),
        suppliers: reportData.suppliers.filter(item => {
            const date = new Date(item.createdAt);
            return date >= start && date <= end;
        }),
        employees: reportData.employees.filter(item => {
            const date = new Date(item.createdAt || item.joinDate);
            return date >= start && date <= end;
        }),
        activities: reportData.activities.filter(item => {
            const date = new Date(item.timestamp);
            return date >= start && date <= end;
        }),
        tasks: reportData.tasks.filter(item => {
            const date = new Date(item.createdAt);
            return date >= start && date <= end;
        })
    };
}

// تصدير تقرير العملاء
function exportCustomerReport() {
    const customers = reportData.customers;
    const reportTitle = `تقرير العملاء - ${formatDate(new Date())}`;
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'email', label: 'البريد الإلكتروني' },
        { key: 'city', label: 'المدينة' },
        { key: 'activityType', label: 'نوع النشاط' },
        { key: 'status', label: 'الحالة' },
        { key: 'lastContact', label: 'آخر تواصل' }
    ];
    
    exportToCSV(customers, 'customer_report', headers);
    logActivity('export_customer_report', 'تصدير تقرير العملاء');
}

// طباعة تقرير العملاء
function printCustomerReport() {
    const customers = reportData.customers;
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' }
    ];
    
    printData('تقرير العملاء', customers, headers);
    logActivity('print_customer_report', 'طباعة تقرير العملاء');
}

// تصدير تقرير الموردين
function exportSupplierReport() {
    const suppliers = reportData.suppliers;
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'serviceType', label: 'نوع الخدمة' },
        { key: 'contactMethod', label: 'وسيلة التواصل' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' }
    ];
    
    exportToCSV(suppliers, 'supplier_report', headers);
    logActivity('export_supplier_report', 'تصدير تقرير الموردين');
}

// طباعة تقرير الموردين
function printSupplierReport() {
    const suppliers = reportData.suppliers;
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'serviceType', label: 'نوع الخدمة' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' }
    ];
    
    printData('تقرير الموردين', suppliers, headers);
    logActivity('print_supplier_report', 'طباعة تقرير الموردين');
}

// تصدير تقرير الأداء
function exportPerformanceReport() {
    const employees = reportData.employees;
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'department', label: 'القسم' },
        { key: 'performance', label: 'مستوى الأداء' },
        { key: 'performanceScore', label: 'نقاط الأداء' },
        { key: 'totalCalls', label: 'إجمالي المكالمات' },
        { key: 'successfulCalls', label: 'المكالمات الناجحة' }
    ];
    
    exportToCSV(employees, 'performance_report', headers);
    logActivity('export_performance_report', 'تصدير تقرير الأداء');
}

// طباعة تقرير الأداء
function printPerformanceReport() {
    const employees = reportData.employees;
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'department', label: 'القسم' },
        { key: 'performance', label: 'الأداء' },
        { key: 'performanceScore', label: 'النقاط' }
    ];
    
    printData('تقرير الأداء', employees, headers);
    logActivity('print_performance_report', 'طباعة تقرير الأداء');
}

// تصدير تقرير الأنشطة
function exportActivityReport() {
    const filteredData = filterDataByDateRange();
    const activities = filteredData.activities;
    
    const headers = [
        { key: 'type', label: 'نوع النشاط' },
        { key: 'description', label: 'الوصف' },
        { key: 'user', label: 'المستخدم' },
        { key: 'date', label: 'التاريخ' },
        { key: 'time', label: 'الوقت' }
    ];
    
    exportToCSV(activities, 'activity_report', headers);
    logActivity('export_activity_report', 'تصدير تقرير الأنشطة');
}

// طباعة تقرير الأنشطة
function printActivityReport() {
    const filteredData = filterDataByDateRange();
    const activities = filteredData.activities;
    
    const headers = [
        { key: 'description', label: 'النشاط' },
        { key: 'user', label: 'المستخدم' },
        { key: 'date', label: 'التاريخ' },
        { key: 'time', label: 'الوقت' }
    ];
    
    printData('تقرير الأنشطة', activities, headers);
    logActivity('print_activity_report', 'طباعة تقرير الأنشطة');
}

// إنشاء تقرير مخصص
function generateCustomReport() {
    // إنشاء نافذة منبثقة لإعدادات التقرير المخصص
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-ar="إنشاء تقرير مخصص" data-en="Generate Custom Report">إنشاء تقرير مخصص</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label data-ar="نوع التقرير:" data-en="Report Type:">نوع التقرير:</label>
                    <select id="customReportType" class="filter-select">
                        <option value="customers" data-ar="العملاء" data-en="Customers">العملاء</option>
                        <option value="suppliers" data-ar="الموردين" data-en="Suppliers">الموردين</option>
                        <option value="employees" data-ar="الموظفين" data-en="Employees">الموظفين</option>
                        <option value="activities" data-ar="الأنشطة" data-en="Activities">الأنشطة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label data-ar="تنسيق التصدير:" data-en="Export Format:">تنسيق التصدير:</label>
                    <select id="customReportFormat" class="filter-select">
                        <option value="csv">CSV</option>
                        <option value="json">JSON</option>
                        <option value="print" data-ar="طباعة" data-en="Print">طباعة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label data-ar="من تاريخ:" data-en="From Date:">من تاريخ:</label>
                    <input type="date" id="customStartDate" class="filter-select">
                </div>
                
                <div class="form-group">
                    <label data-ar="إلى تاريخ:" data-en="To Date:">إلى تاريخ:</label>
                    <input type="date" id="customEndDate" class="filter-select">
                </div>
                
                <div class="modal-actions">
                    <button class="btn-primary" onclick="executeCustomReport()">
                        <i class="fas fa-chart-line"></i>
                        <span data-ar="إنشاء التقرير" data-en="Generate Report">إنشاء التقرير</span>
                    </button>
                    <button class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                        <span data-ar="إلغاء" data-en="Cancel">إلغاء</span>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    updateLanguage();
}

// تنفيذ التقرير المخصص
function executeCustomReport() {
    const reportType = document.getElementById('customReportType').value;
    const format = document.getElementById('customReportFormat').value;
    const startDate = document.getElementById('customStartDate').value;
    const endDate = document.getElementById('customEndDate').value;
    
    // فلترة البيانات حسب النوع والتاريخ
    let data = reportData[reportType] || [];
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        
        data = data.filter(item => {
            const itemDate = new Date(item.createdAt || item.timestamp || item.lastContact);
            return itemDate >= start && itemDate <= end;
        });
    }
    
    // تحديد الرؤوس حسب نوع التقرير
    let headers = [];
    let fileName = '';
    let title = '';
    
    switch (reportType) {
        case 'customers':
            headers = [
                { key: 'name', label: 'الاسم' },
                { key: 'phone', label: 'رقم الجوال' },
                { key: 'email', label: 'البريد الإلكتروني' },
                { key: 'city', label: 'المدينة' },
                { key: 'status', label: 'الحالة' }
            ];
            fileName = 'custom_customers_report';
            title = 'تقرير العملاء المخصص';
            break;
        case 'suppliers':
            headers = [
                { key: 'name', label: 'الاسم' },
                { key: 'serviceType', label: 'نوع الخدمة' },
                { key: 'city', label: 'المدينة' },
                { key: 'status', label: 'الحالة' }
            ];
            fileName = 'custom_suppliers_report';
            title = 'تقرير الموردين المخصص';
            break;
        case 'employees':
            headers = [
                { key: 'name', label: 'الاسم' },
                { key: 'department', label: 'القسم' },
                { key: 'performance', label: 'الأداء' },
                { key: 'status', label: 'الحالة' }
            ];
            fileName = 'custom_employees_report';
            title = 'تقرير الموظفين المخصص';
            break;
        case 'activities':
            headers = [
                { key: 'description', label: 'النشاط' },
                { key: 'user', label: 'المستخدم' },
                { key: 'date', label: 'التاريخ' },
                { key: 'time', label: 'الوقت' }
            ];
            fileName = 'custom_activities_report';
            title = 'تقرير الأنشطة المخصص';
            break;
    }
    
    // تنفيذ التصدير حسب التنسيق
    if (format === 'csv') {
        exportToCSV(data, fileName, headers);
    } else if (format === 'json') {
        exportToJSON(data, fileName);
    } else if (format === 'print') {
        printData(title, data, headers);
    }
    
    // إغلاق النافذة المنبثقة
    document.querySelector('.modal-overlay').remove();
    
    // تسجيل النشاط
    logActivity('custom_report', `إنشاء تقرير مخصص: ${title}`);
    
    alert('تم إنشاء التقرير المخصص بنجاح');
}
