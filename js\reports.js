// ===== إدارة التقارير =====

let reportData = {
    customers: [],
    suppliers: [],
    employees: [],
    activities: [],
    tasks: []
};

let currentDateRange = 'month';
let startDate = null;
let endDate = null;

// تهيئة صفحة التقارير
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_reports') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // تهيئة البيانات
    initializeReportData();
    
    // ربط الأحداث
    bindEvents();
    
    // تحديث التقارير
    updateReports();
    
    // تسجيل النشاط
    logActivity('reports_view', 'عرض صفحة التقارير');
});

// تهيئة بيانات التقارير
function initializeReportData() {
    reportData.customers = JSON.parse(localStorage.getItem('customers')) || [];
    reportData.suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
    reportData.employees = JSON.parse(localStorage.getItem('telesales')) || [];
    reportData.activities = JSON.parse(localStorage.getItem('activities')) || [];
    reportData.tasks = JSON.parse(localStorage.getItem('tasks')) || [];
}

// ربط الأحداث
function bindEvents() {
    // فلتر نوع التقرير
    const reportType = document.getElementById('reportType');
    if (reportType) {
        reportType.addEventListener('change', updateReports);
    }
    
    // فلتر النطاق الزمني
    const dateRange = document.getElementById('dateRange');
    if (dateRange) {
        dateRange.addEventListener('change', handleDateRangeChange);
    }
    
    // تواريخ مخصصة
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    
    if (startDateInput) {
        startDateInput.addEventListener('change', updateReports);
    }
    
    if (endDateInput) {
        endDateInput.addEventListener('change', updateReports);
    }
}

// معالج تغيير النطاق الزمني
function handleDateRangeChange() {
    const dateRange = document.getElementById('dateRange').value;
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    
    if (dateRange === 'custom') {
        startDateInput.style.display = 'block';
        endDateInput.style.display = 'block';
    } else {
        startDateInput.style.display = 'none';
        endDateInput.style.display = 'none';
        
        // تحديد التواريخ حسب النطاق المحدد
        const now = new Date();
        endDate = new Date(now);
        
        switch (dateRange) {
            case 'today':
                startDate = new Date(now);
                break;
            case 'week':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                break;
            case 'quarter':
                const quarter = Math.floor(now.getMonth() / 3);
                startDate = new Date(now.getFullYear(), quarter * 3, 1);
                break;
            case 'year':
                startDate = new Date(now.getFullYear(), 0, 1);
                break;
        }
    }
    
    currentDateRange = dateRange;
    updateReports();
}

// تحديث التقارير
function updateReports() {
    // تحديث البيانات
    initializeReportData();
    
    // تحديث التواريخ المخصصة إذا لزم الأمر
    if (currentDateRange === 'custom') {
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        
        if (startDateInput.value) {
            startDate = new Date(startDateInput.value);
        }
        
        if (endDateInput.value) {
            endDate = new Date(endDateInput.value);
        }
    }
    
    // تحديث الإحصائيات العامة
    updateGeneralStats();
    
    // تحديث تقارير محددة
    updateCustomerReport();
    updateSupplierReport();
    updatePerformanceReport();
    updateActivityReport();
    
    // تسجيل النشاط
    logActivity('reports_update', 'تحديث التقارير');
}

// تحديث الإحصائيات العامة
function updateGeneralStats() {
    const filteredData = filterDataByDateRange();
    
    updateCounter('totalCustomersReport', reportData.customers.length);
    updateCounter('totalSuppliersReport', reportData.suppliers.length);
    updateCounter('totalEmployeesReport', reportData.employees.length);
    updateCounter('totalActivitiesReport', filteredData.activities.length);
}

// تحديث تقرير العملاء
function updateCustomerReport() {
    const customers = reportData.customers;
    const filteredData = filterDataByDateRange();
    
    const activeCustomers = customers.filter(c => c.status === 'active').length;
    const potentialCustomers = customers.filter(c => c.status === 'potential').length;
    const newCustomers = filteredData.customers.length;
    
    updateCounter('activeCustomersReport', activeCustomers);
    updateCounter('potentialCustomersReport', potentialCustomers);
    updateCounter('newCustomersReport', newCustomers);
}

// تحديث تقرير الموردين
function updateSupplierReport() {
    const suppliers = reportData.suppliers;
    
    const activeSuppliers = suppliers.filter(s => s.status === 'active').length;
    const serviceTypes = [...new Set(suppliers.map(s => s.serviceType))].length;
    const citiesCovered = [...new Set(suppliers.map(s => s.city))].length;
    
    updateCounter('activeSuppliersReport', activeSuppliers);
    updateCounter('serviceTypesReport', serviceTypes);
    updateCounter('citiesCoveredReport', citiesCovered);
}

// تحديث تقرير الأداء
function updatePerformanceReport() {
    const employees = reportData.employees;
    const tasks = reportData.tasks;
    
    // حساب متوسط الأداء
    const avgPerformance = employees.length > 0 
        ? Math.round(employees.reduce((sum, emp) => sum + (emp.performanceScore || 0), 0) / employees.length)
        : 0;
    
    // حساب الموظفين المتميزين (أداء أكثر من 90%)
    const topPerformers = employees.filter(emp => (emp.performanceScore || 0) >= 90).length;
    
    // حساب المهام المكتملة
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    
    const avgElement = document.getElementById('avgPerformanceReport');
    if (avgElement) {
        avgElement.textContent = avgPerformance + '%';
    }
    
    updateCounter('topPerformersReport', topPerformers);
    updateCounter('completedTasksReport', completedTasks);
}

// تحديث تقرير الأنشطة
function updateActivityReport() {
    const activities = reportData.activities;
    const now = new Date();
    
    // أنشطة اليوم
    const today = now.toDateString();
    const todayActivities = activities.filter(activity => 
        new Date(activity.timestamp).toDateString() === today
    ).length;
    
    // أنشطة هذا الأسبوع
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const weekActivities = activities.filter(activity => 
        new Date(activity.timestamp) >= weekAgo
    ).length;
    
    // أكثر المستخدمين نشاطاً
    const userActivities = {};
    activities.forEach(activity => {
        if (activity.user) {
            userActivities[activity.user] = (userActivities[activity.user] || 0) + 1;
        }
    });
    
    const mostActiveUser = Object.keys(userActivities).reduce((a, b) => 
        userActivities[a] > userActivities[b] ? a : b, '-'
    );
    
    updateCounter('todayActivitiesReport', todayActivities);
    updateCounter('weekActivitiesReport', weekActivities);
    
    const mostActiveElement = document.getElementById('mostActiveUserReport');
    if (mostActiveElement) {
        mostActiveElement.textContent = mostActiveUser;
    }
}

// فلترة البيانات حسب النطاق الزمني
function filterDataByDateRange() {
    if (!startDate || !endDate) {
        return {
            customers: reportData.customers,
            suppliers: reportData.suppliers,
            employees: reportData.employees,
            activities: reportData.activities,
            tasks: reportData.tasks
        };
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999); // نهاية اليوم
    
    return {
        customers: reportData.customers.filter(item => {
            const date = new Date(item.createdAt || item.lastContact);
            return date >= start && date <= end;
        }),
        suppliers: reportData.suppliers.filter(item => {
            const date = new Date(item.createdAt);
            return date >= start && date <= end;
        }),
        employees: reportData.employees.filter(item => {
            const date = new Date(item.createdAt || item.joinDate);
            return date >= start && date <= end;
        }),
        activities: reportData.activities.filter(item => {
            const date = new Date(item.timestamp);
            return date >= start && date <= end;
        }),
        tasks: reportData.tasks.filter(item => {
            const date = new Date(item.createdAt);
            return date >= start && date <= end;
        })
    };
}

// تصدير تقرير العملاء
function exportCustomerReport() {
    const customers = reportData.customers;
    const reportTitle = `تقرير العملاء - ${formatDate(new Date())}`;
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'email', label: 'البريد الإلكتروني' },
        { key: 'city', label: 'المدينة' },
        { key: 'activityType', label: 'نوع النشاط' },
        { key: 'status', label: 'الحالة' },
        { key: 'lastContact', label: 'آخر تواصل' }
    ];
    
    exportToCSV(customers, 'customer_report', headers);
    logActivity('export_customer_report', 'تصدير تقرير العملاء');
}

// طباعة تقرير العملاء
function printCustomerReport() {
    const customers = reportData.customers;
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' }
    ];
    
    printData('تقرير العملاء', customers, headers);
    logActivity('print_customer_report', 'طباعة تقرير العملاء');
}

// تصدير تقرير الموردين
function exportSupplierReport() {
    const suppliers = reportData.suppliers;
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'serviceType', label: 'نوع الخدمة' },
        { key: 'contactMethod', label: 'وسيلة التواصل' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' }
    ];
    
    exportToCSV(suppliers, 'supplier_report', headers);
    logActivity('export_supplier_report', 'تصدير تقرير الموردين');
}

// طباعة تقرير الموردين
function printSupplierReport() {
    const suppliers = reportData.suppliers;
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'serviceType', label: 'نوع الخدمة' },
        { key: 'city', label: 'المدينة' },
        { key: 'status', label: 'الحالة' }
    ];
    
    printData('تقرير الموردين', suppliers, headers);
    logActivity('print_supplier_report', 'طباعة تقرير الموردين');
}

// تصدير تقرير الأداء
function exportPerformanceReport() {
    const employees = reportData.employees;
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'department', label: 'القسم' },
        { key: 'performance', label: 'مستوى الأداء' },
        { key: 'performanceScore', label: 'نقاط الأداء' },
        { key: 'totalCalls', label: 'إجمالي المكالمات' },
        { key: 'successfulCalls', label: 'المكالمات الناجحة' }
    ];
    
    exportToCSV(employees, 'performance_report', headers);
    logActivity('export_performance_report', 'تصدير تقرير الأداء');
}

// طباعة تقرير الأداء
function printPerformanceReport() {
    const employees = reportData.employees;
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'department', label: 'القسم' },
        { key: 'performance', label: 'الأداء' },
        { key: 'performanceScore', label: 'النقاط' }
    ];
    
    printData('تقرير الأداء', employees, headers);
    logActivity('print_performance_report', 'طباعة تقرير الأداء');
}

// تصدير تقرير الأنشطة
function exportActivityReport() {
    const filteredData = filterDataByDateRange();
    const activities = filteredData.activities;
    
    const headers = [
        { key: 'type', label: 'نوع النشاط' },
        { key: 'description', label: 'الوصف' },
        { key: 'user', label: 'المستخدم' },
        { key: 'date', label: 'التاريخ' },
        { key: 'time', label: 'الوقت' }
    ];
    
    exportToCSV(activities, 'activity_report', headers);
    logActivity('export_activity_report', 'تصدير تقرير الأنشطة');
}

// طباعة تقرير الأنشطة
function printActivityReport() {
    const filteredData = filterDataByDateRange();
    const activities = filteredData.activities;
    
    const headers = [
        { key: 'description', label: 'النشاط' },
        { key: 'user', label: 'المستخدم' },
        { key: 'date', label: 'التاريخ' },
        { key: 'time', label: 'الوقت' }
    ];
    
    printData('تقرير الأنشطة', activities, headers);
    logActivity('print_activity_report', 'طباعة تقرير الأنشطة');
}

// إنشاء تقرير مخصص
function generateCustomReport() {
    // إنشاء نافذة منبثقة لإعدادات التقرير المخصص
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-ar="إنشاء تقرير مخصص" data-en="Generate Custom Report">إنشاء تقرير مخصص</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label data-ar="نوع التقرير:" data-en="Report Type:">نوع التقرير:</label>
                    <select id="customReportType" class="filter-select">
                        <option value="customers" data-ar="العملاء" data-en="Customers">العملاء</option>
                        <option value="suppliers" data-ar="الموردين" data-en="Suppliers">الموردين</option>
                        <option value="employees" data-ar="الموظفين" data-en="Employees">الموظفين</option>
                        <option value="activities" data-ar="الأنشطة" data-en="Activities">الأنشطة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label data-ar="تنسيق التصدير:" data-en="Export Format:">تنسيق التصدير:</label>
                    <select id="customReportFormat" class="filter-select">
                        <option value="csv">CSV</option>
                        <option value="json">JSON</option>
                        <option value="print" data-ar="طباعة" data-en="Print">طباعة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label data-ar="من تاريخ:" data-en="From Date:">من تاريخ:</label>
                    <input type="date" id="customStartDate" class="filter-select">
                </div>
                
                <div class="form-group">
                    <label data-ar="إلى تاريخ:" data-en="To Date:">إلى تاريخ:</label>
                    <input type="date" id="customEndDate" class="filter-select">
                </div>
                
                <div class="modal-actions">
                    <button class="btn-primary" onclick="executeCustomReport()">
                        <i class="fas fa-chart-line"></i>
                        <span data-ar="إنشاء التقرير" data-en="Generate Report">إنشاء التقرير</span>
                    </button>
                    <button class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                        <span data-ar="إلغاء" data-en="Cancel">إلغاء</span>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    updateLanguage();
}

// تنفيذ التقرير المخصص
function executeCustomReport() {
    const reportType = document.getElementById('customReportType').value;
    const format = document.getElementById('customReportFormat').value;
    const startDate = document.getElementById('customStartDate').value;
    const endDate = document.getElementById('customEndDate').value;
    
    // فلترة البيانات حسب النوع والتاريخ
    let data = reportData[reportType] || [];
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        
        data = data.filter(item => {
            const itemDate = new Date(item.createdAt || item.timestamp || item.lastContact);
            return itemDate >= start && itemDate <= end;
        });
    }
    
    // تحديد الرؤوس حسب نوع التقرير
    let headers = [];
    let fileName = '';
    let title = '';
    
    switch (reportType) {
        case 'customers':
            headers = [
                { key: 'name', label: 'الاسم' },
                { key: 'phone', label: 'رقم الجوال' },
                { key: 'email', label: 'البريد الإلكتروني' },
                { key: 'city', label: 'المدينة' },
                { key: 'status', label: 'الحالة' }
            ];
            fileName = 'custom_customers_report';
            title = 'تقرير العملاء المخصص';
            break;
        case 'suppliers':
            headers = [
                { key: 'name', label: 'الاسم' },
                { key: 'serviceType', label: 'نوع الخدمة' },
                { key: 'city', label: 'المدينة' },
                { key: 'status', label: 'الحالة' }
            ];
            fileName = 'custom_suppliers_report';
            title = 'تقرير الموردين المخصص';
            break;
        case 'employees':
            headers = [
                { key: 'name', label: 'الاسم' },
                { key: 'department', label: 'القسم' },
                { key: 'performance', label: 'الأداء' },
                { key: 'status', label: 'الحالة' }
            ];
            fileName = 'custom_employees_report';
            title = 'تقرير الموظفين المخصص';
            break;
        case 'activities':
            headers = [
                { key: 'description', label: 'النشاط' },
                { key: 'user', label: 'المستخدم' },
                { key: 'date', label: 'التاريخ' },
                { key: 'time', label: 'الوقت' }
            ];
            fileName = 'custom_activities_report';
            title = 'تقرير الأنشطة المخصص';
            break;
    }
    
    // تنفيذ التصدير حسب التنسيق
    if (format === 'csv') {
        exportToCSV(data, fileName, headers);
    } else if (format === 'json') {
        exportToJSON(data, fileName);
    } else if (format === 'print') {
        printData(title, data, headers);
    }
    
    // إغلاق النافذة المنبثقة
    document.querySelector('.modal-overlay').remove();
    
    // تسجيل النشاط
    logActivity('custom_report', `إنشاء تقرير مخصص: ${title}`);
    
    alert('تم إنشاء التقرير المخصص بنجاح');
}
