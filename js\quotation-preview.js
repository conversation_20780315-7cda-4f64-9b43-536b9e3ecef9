// ===== معاينة عرض السعر =====

let currentQuotation = null;
let quotationId = null;

// تهيئة صفحة المعاينة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_quotations') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // الحصول على معرف العرض من URL
    quotationId = getQuotationIdFromURL();
    
    // تحميل بيانات العرض
    loadQuotationData();
    
    // ربط الأحداث
    bindPreviewEvents();
    
    // تسجيل النشاط
    logActivity('quotation_preview', `عرض معاينة عرض السعر: ${quotationId}`);
});

// الحصول على معرف العرض من URL
function getQuotationIdFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('id') || 'QT-2024-001';
}

// تحميل بيانات العرض
function loadQuotationData() {
    // تحميل العروض من التخزين المحلي
    const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
    
    // البحث عن العرض المطلوب
    currentQuotation = quotations.find(q => q.quoteNumber === quotationId);
    
    if (!currentQuotation) {
        // إنشاء بيانات تجريبية إذا لم يتم العثور على العرض
        currentQuotation = generateSampleQuotation();
    }
    
    // تحديث واجهة المستخدم
    updateQuotationDisplay();
}

// إنشاء بيانات تجريبية للعرض
function generateSampleQuotation() {
    return {
        id: 'quote_' + Date.now(),
        quoteNumber: quotationId,
        customerName: 'شركة الأمل للخدمات اللوجستية',
        customerContact: {
            name: 'أحمد محمد العلي',
            position: 'مدير المشتريات',
            phone: '+966 12 987 6543',
            email: '<EMAIL>',
            address: 'جدة، المملكة العربية السعودية'
        },
        quoteDate: new Date().toISOString(),
        validUntil: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
        deliveryTime: '7-10 أيام عمل',
        status: 'sent',
        items: [
            {
                id: 1,
                title: 'نظام إدارة المخازن الذكي',
                description: 'نظام متكامل لإدارة المخازن مع تتبع المخزون في الوقت الفعلي',
                quantity: 1,
                unit: 'نظام',
                unitPrice: 25000,
                total: 25000
            },
            {
                id: 2,
                title: 'تطبيق الهاتف المحمول',
                description: 'تطبيق iOS و Android لإدارة العمليات عن بُعد',
                quantity: 2,
                unit: 'تطبيق',
                unitPrice: 8000,
                total: 16000
            },
            {
                id: 3,
                title: 'خدمات التدريب والدعم',
                description: 'تدريب الفريق وخدمات الدعم الفني لمدة 6 أشهر',
                quantity: 1,
                unit: 'باقة',
                unitPrice: 3500,
                total: 3500
            },
            {
                id: 4,
                title: 'خدمات التركيب والتشغيل',
                description: 'تركيب النظام وتشغيله في موقع العميل',
                quantity: 1,
                unit: 'خدمة',
                unitPrice: 1250,
                total: 1250
            }
        ],
        subtotal: 45750,
        discount: 2287.50,
        discountPercentage: 5,
        tax: 6519.38,
        taxPercentage: 15,
        grandTotal: 49981.88,
        notes: [
            'جميع الأسعار شاملة ضريبة القيمة المضافة (15%)',
            'مدة صلاحية العرض: 15 يوم من تاريخ الإصدار',
            'يتم التسليم خلال 7-10 أيام عمل من تاريخ الموافقة',
            'الدفع: 50% مقدم، 50% عند التسليم',
            'ضمان لمدة سنة واحدة على جميع الخدمات'
        ],
        createdBy: 'خالد أحمد السعيد',
        createdByTitle: 'مدير المبيعات'
    };
}

// تحديث عرض العرض
function updateQuotationDisplay() {
    if (!currentQuotation) return;
    
    // تحديث معلومات الرأس
    updateElement('quotationTitle', `عرض سعر رقم: ${currentQuotation.quoteNumber}`);
    updateElement('quotationStatus', getStatusDisplayName(currentQuotation.status));
    updateElement('quoteNumber', currentQuotation.quoteNumber);
    updateElement('quoteDate', formatDate(currentQuotation.quoteDate));
    updateElement('validUntil', formatDate(currentQuotation.validUntil));
    updateElement('deliveryTime', currentQuotation.deliveryTime);
    
    // تحديث معلومات العميل
    updateElement('customerName', currentQuotation.customerName);
    
    // تحديث ملخص العرض
    updateElement('totalItems', currentQuotation.items.length);
    updateElement('totalAmount', formatCurrency(currentQuotation.grandTotal));
    updateElement('currentStatus', getStatusDisplayName(currentQuotation.status));
    
    // تحديث جدول البنود
    updateItemsTable();
    
    // تحديث الحسابات المالية
    updateCalculations();
    
    // تحديث حالة العرض في شريط الأدوات
    updateStatusBadge();
}

// تحديث جدول البنود
function updateItemsTable() {
    const tbody = document.getElementById('itemsTableBody');
    if (!tbody || !currentQuotation.items) return;
    
    tbody.innerHTML = currentQuotation.items.map((item, index) => `
        <tr>
            <td class="item-no">${index + 1}</td>
            <td class="item-description">
                <div class="item-title">${item.title}</div>
                <div class="item-details">${item.description}</div>
            </td>
            <td class="item-quantity">${item.quantity}</td>
            <td class="item-unit">${item.unit}</td>
            <td class="item-price">${formatCurrency(item.unitPrice)}</td>
            <td class="item-total">${formatCurrency(item.total)}</td>
        </tr>
    `).join('');
}

// تحديث الحسابات المالية
function updateCalculations() {
    updateElement('subtotal', formatCurrency(currentQuotation.subtotal));
    updateElement('discount', formatCurrency(-currentQuotation.discount));
    updateElement('tax', formatCurrency(currentQuotation.tax));
    updateElement('grandTotal', formatCurrency(currentQuotation.grandTotal));
}

// تحديث شارة الحالة
function updateStatusBadge() {
    const statusElement = document.getElementById('quotationStatus');
    if (statusElement) {
        statusElement.className = `quotation-status status-${currentQuotation.status}`;
        statusElement.textContent = getStatusDisplayName(currentQuotation.status);
    }
}

// ربط الأحداث
function bindPreviewEvents() {
    // إغلاق القوائم المنسدلة عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.dropdown')) {
            closeAllDropdowns();
        }
    });
    
    // منع إغلاق القائمة عند النقر داخلها
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        menu.addEventListener('click', function(event) {
            event.stopPropagation();
        });
    });
}

// دوال الإجراءات
function goBack() {
    if (document.referrer && document.referrer.includes(window.location.origin)) {
        window.history.back();
    } else {
        window.location.href = 'quotations.html';
    }
}

function editQuotation() {
    if (!hasPermission('edit_quotations') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتعديل عروض الأسعار');
        return;
    }
    
    window.location.href = `quotation-form.html?id=${currentQuotation.quoteNumber}&mode=edit`;
}

function printQuotation() {
    // إخفاء شريط الأدوات مؤقتاً
    const toolbar = document.querySelector('.preview-toolbar');
    if (toolbar) toolbar.style.display = 'none';
    
    // طباعة الصفحة
    window.print();
    
    // إظهار شريط الأدوات مرة أخرى
    setTimeout(() => {
        if (toolbar) toolbar.style.display = 'flex';
    }, 100);
    
    logActivity('quotation_printed', `طباعة عرض السعر: ${currentQuotation.quoteNumber}`);
}

function downloadPDF() {
    showNotification('جاري تحضير ملف PDF...', 'info');
    
    // محاكاة تحميل PDF
    setTimeout(() => {
        // في التطبيق الحقيقي، سيتم استخدام مكتبة مثل jsPDF أو html2pdf
        const link = document.createElement('a');
        link.href = '#'; // سيتم استبدالها برابط PDF الفعلي
        link.download = `quotation-${currentQuotation.quoteNumber}.pdf`;
        // link.click(); // تم تعطيلها للتجربة
        
        showNotification('تم تحضير ملف PDF بنجاح', 'success');
        logActivity('quotation_pdf_downloaded', `تحميل PDF لعرض السعر: ${currentQuotation.quoteNumber}`);
    }, 2000);
}

function sendEmail() {
    const modal = document.getElementById('emailModal');
    if (modal) {
        modal.classList.add('show');
        
        // تحديث بيانات النموذج
        updateElement('emailTo', currentQuotation.customerContact.email);
        updateElement('emailSubject', `عرض سعر رقم ${currentQuotation.quoteNumber}`);
    }
}

function closeEmailModal() {
    const modal = document.getElementById('emailModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

function sendQuotationEmail() {
    const emailTo = document.getElementById('emailTo').value;
    const emailCc = document.getElementById('emailCc').value;
    const emailSubject = document.getElementById('emailSubject').value;
    const emailMessage = document.getElementById('emailMessage').value;
    
    if (!emailTo || !emailSubject || !emailMessage) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    showNotification('جاري إرسال البريد الإلكتروني...', 'info');
    
    // محاكاة إرسال البريد الإلكتروني
    setTimeout(() => {
        // تحديث حالة العرض إلى "مرسل"
        currentQuotation.status = 'sent';
        currentQuotation.sentDate = new Date().toISOString();
        currentQuotation.sentTo = emailTo;
        
        // حفظ التغييرات
        saveQuotationChanges();
        
        // تحديث العرض
        updateStatusBadge();
        
        // إغلاق النافذة
        closeEmailModal();
        
        showNotification('تم إرسال عرض السعر بنجاح', 'success');
        logActivity('quotation_emailed', `إرسال عرض السعر بالبريد الإلكتروني: ${currentQuotation.quoteNumber} إلى ${emailTo}`);
    }, 2000);
}

function toggleMoreActions() {
    const menu = document.getElementById('moreActionsMenu');
    if (menu) {
        menu.classList.toggle('show');
    }
}

function closeAllDropdowns() {
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        menu.classList.remove('show');
    });
}

function duplicateQuotation() {
    if (!hasPermission('create_quotations') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لإنشاء عروض أسعار');
        return;
    }
    
    showNotification('جاري نسخ العرض...', 'info');
    
    // إنشاء نسخة من العرض
    const newQuotation = {
        ...currentQuotation,
        id: 'quote_' + Date.now(),
        quoteNumber: generateQuoteNumber(),
        quoteDate: new Date().toISOString(),
        validUntil: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'draft'
    };
    
    // حفظ العرض الجديد
    const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
    quotations.push(newQuotation);
    localStorage.setItem('quotations', JSON.stringify(quotations));
    
    showNotification('تم نسخ العرض بنجاح', 'success');
    logActivity('quotation_duplicated', `نسخ عرض السعر: ${currentQuotation.quoteNumber} إلى ${newQuotation.quoteNumber}`);
    
    // الانتقال إلى العرض الجديد
    setTimeout(() => {
        window.location.href = `quotation-preview.html?id=${newQuotation.quoteNumber}`;
    }, 1000);
}

function convertToInvoice() {
    if (!hasPermission('create_invoices') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لإنشاء فواتير');
        return;
    }
    
    showNotification('جاري تحويل العرض إلى فاتورة...', 'info');
    
    // محاكاة تحويل العرض إلى فاتورة
    setTimeout(() => {
        showNotification('تم تحويل العرض إلى فاتورة بنجاح', 'success');
        logActivity('quotation_converted_to_invoice', `تحويل عرض السعر إلى فاتورة: ${currentQuotation.quoteNumber}`);
    }, 2000);
}

function shareQuotation() {
    if (navigator.share) {
        navigator.share({
            title: `عرض سعر رقم ${currentQuotation.quoteNumber}`,
            text: `عرض سعر من شركة التقنية المتقدمة`,
            url: window.location.href
        });
    } else {
        // نسخ الرابط إلى الحافظة
        navigator.clipboard.writeText(window.location.href).then(() => {
            showNotification('تم نسخ رابط العرض إلى الحافظة', 'success');
        });
    }
    
    logActivity('quotation_shared', `مشاركة عرض السعر: ${currentQuotation.quoteNumber}`);
}

function deleteQuotation() {
    if (!hasPermission('delete_quotations') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لحذف عروض الأسعار');
        return;
    }
    
    if (confirm(`هل أنت متأكد من حذف عرض السعر "${currentQuotation.quoteNumber}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // حذف العرض من التخزين المحلي
        const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
        const updatedQuotations = quotations.filter(q => q.quoteNumber !== currentQuotation.quoteNumber);
        localStorage.setItem('quotations', JSON.stringify(updatedQuotations));
        
        showNotification('تم حذف عرض السعر بنجاح', 'success');
        logActivity('quotation_deleted', `حذف عرض السعر: ${currentQuotation.quoteNumber}`);
        
        // العودة إلى قائمة العروض
        setTimeout(() => {
            window.location.href = 'quotations.html';
        }, 1000);
    }
}

// دوال مساعدة
function saveQuotationChanges() {
    const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
    const index = quotations.findIndex(q => q.quoteNumber === currentQuotation.quoteNumber);
    
    if (index !== -1) {
        quotations[index] = currentQuotation;
        localStorage.setItem('quotations', JSON.stringify(quotations));
    }
}

function getStatusDisplayName(status) {
    const statuses = {
        'draft': 'مسودة',
        'sent': 'مرسل',
        'accepted': 'مقبول',
        'rejected': 'مرفوض',
        'expired': 'منتهي الصلاحية'
    };
    return statuses[status] || status;
}

function generateQuoteNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = String(now.getTime()).slice(-4);
    
    return `QT-${year}${month}${day}-${time}`;
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            element.value = value;
        } else {
            element.textContent = value;
        }
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
