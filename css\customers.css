/* ===== تصميم صفحة العملاء ===== */

/* ===== رأس الصفحة ===== */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 25px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
}

.page-title h2 {
    font-size: 1.8rem;
    color: var(--dark-gray);
    margin-bottom: 8px;
    font-weight: 700;
}

.page-title p {
    color: var(--gray);
    font-size: 1rem;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* ===== قسم الفلاتر ===== */
.filters-section {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    margin-bottom: 25px;
}

.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray);
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.filters-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-select {
    flex: 1;
    min-width: 150px;
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--dark-gray);
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-blue);
}

.export-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-export {
    background: var(--light-blue);
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.btn-export:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-export.excel-btn {
    background: #e8f5e8;
    color: #217346;
    border-color: #217346;
}

.btn-export.excel-btn:hover {
    background: #217346;
    color: var(--white);
    box-shadow: 0 4px 12px rgba(33, 115, 70, 0.3);
}

.btn-export.pdf-btn {
    background: #fdeaea;
    color: #dc3545;
    border-color: #dc3545;
}

.btn-export.pdf-btn:hover {
    background: #dc3545;
    color: var(--white);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-export:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    pointer-events: none;
}

.btn-export:disabled:hover {
    transform: none;
    box-shadow: none;
}

.btn-export .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== إحصائيات سريعة ===== */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-item {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow);
}

.stat-item i {
    font-size: 2rem;
    color: var(--primary-blue);
    width: 50px;
    text-align: center;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 5px;
}

.stat-content p {
    color: var(--gray);
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== قسم الجدول ===== */
.table-section {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
    background: var(--light-gray);
}

.table-header h3 {
    font-size: 1.2rem;
    color: var(--dark-gray);
    font-weight: 700;
    margin: 0;
}

.table-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-select {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: var(--white);
    color: var(--dark-gray);
    font-size: 0.9rem;
    cursor: pointer;
}

.sort-order-btn {
    background: var(--light-blue);
    color: var(--primary-blue);
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.sort-order-btn:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.table-container {
    overflow-x: auto;
}

.customers-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.customers-table th {
    background: var(--light-gray);
    color: var(--dark-gray);
    font-weight: 700;
    padding: 15px 12px;
    text-align: right;
    border-bottom: 2px solid #e2e8f0;
    white-space: nowrap;
}

.customers-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #e2e8f0;
    color: var(--dark-gray);
}

.customers-table tbody tr {
    transition: var(--transition);
}

.customers-table tbody tr:hover {
    background: var(--light-blue);
}

/* ===== حالات العملاء ===== */
.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.status-active {
    background: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

.status-potential {
    background: #fef3c7;
    color: #92400e;
}

/* ===== أزرار الإجراءات ===== */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.action-btn {
    background: none;
    border: none;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.action-btn.view {
    color: var(--primary-blue);
}

.action-btn.view:hover {
    background: var(--light-blue);
}

.action-btn.edit {
    color: var(--warning);
}

.action-btn.edit:hover {
    background: #fef3c7;
}

.action-btn.delete {
    color: var(--danger);
}

.action-btn.delete:hover {
    background: #fee2e2;
}

/* ===== ترقيم الصفحات ===== */
.table-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e2e8f0;
}

.pagination-btn {
    background: var(--white);
    border: 2px solid #e2e8f0;
    color: var(--dark-gray);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.pagination-btn:hover {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
}

.pagination-btn.active {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: var(--gray);
    font-size: 0.9rem;
    margin: 0 15px;
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }
    
    .filters-row {
        flex-direction: column;
    }
    
    .export-actions {
        justify-content: center;
    }
    
    .stats-row {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .table-controls {
        justify-content: center;
    }
    
    .customers-table {
        font-size: 0.8rem;
    }
    
    .customers-table th,
    .customers-table td {
        padding: 10px 8px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
}

/* ===== أزرار الاستيراد ===== */
.btn-import {
    background: var(--success);
    color: var(--white);
    border: none;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-import:hover {
    background: #059669;
    transform: translateY(-1px);
}

/* ===== مؤشرات الأداء ===== */
.performance-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.performance-badge {
    padding: 3px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.performance-excellent {
    background: #d1fae5;
    color: #065f46;
}

.performance-good {
    background: #dbeafe;
    color: #1e40af;
}

.performance-average {
    background: #fef3c7;
    color: #92400e;
}

.performance-poor {
    background: #fee2e2;
    color: #991b1b;
}

.performance-score {
    font-size: 0.8rem;
    color: var(--gray);
    font-weight: 600;
}

/* ===== بطاقات المهام ===== */
.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.task-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0 2px 4px var(--shadow);
    border-left: 4px solid var(--primary-blue);
    transition: var(--transition);
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow);
}

.task-card.task-pending {
    border-left-color: var(--warning);
}

.task-card.task-in_progress {
    border-left-color: var(--primary-blue);
}

.task-card.task-completed {
    border-left-color: var(--success);
}

.task-card.task-cancelled {
    border-left-color: var(--danger);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.task-header h4 {
    margin: 0;
    color: var(--dark-gray);
    font-weight: 700;
    flex: 1;
}

.task-priority {
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-high {
    background: #fee2e2;
    color: #991b1b;
}

.priority-medium {
    background: #fef3c7;
    color: #92400e;
}

.priority-low {
    background: #d1fae5;
    color: #065f46;
}

.task-description {
    color: var(--gray);
    margin-bottom: 15px;
    line-height: 1.5;
}

.task-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;
}

.task-assignee {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--gray);
    font-size: 0.9rem;
}

.task-assignee i {
    color: var(--primary-blue);
}

.no-tasks {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    color: var(--gray);
}

.no-tasks i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* ===== أزرار إضافية للإجراءات ===== */
.action-btn.tasks {
    color: var(--accent-blue);
}

.action-btn.tasks:hover {
    background: #dbeafe;
}

@media (max-width: 480px) {
    .stats-row {
        grid-template-columns: 1fr;
    }

    .table-pagination {
        flex-wrap: wrap;
        gap: 5px;
    }

    .pagination-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .tasks-grid {
        grid-template-columns: 1fr;
    }

    .task-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .task-footer {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
}

/* ===== بطاقات التقارير ===== */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.report-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 6px var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.report-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px var(--shadow);
}

.report-header {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
}

.report-header i {
    font-size: 1.5rem;
    opacity: 0.8;
}

.report-content {
    padding: 25px;
}

.report-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
}

.report-stat:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--gray);
    font-weight: 600;
    font-size: 0.95rem;
}

.stat-value {
    color: var(--dark-gray);
    font-weight: 700;
    font-size: 1.1rem;
}

.report-actions {
    padding: 20px 25px;
    background: var(--light-gray);
    display: flex;
    gap: 10px;
    justify-content: center;
}

.report-actions .btn-export {
    flex: 1;
    justify-content: center;
    font-size: 0.85rem;
    padding: 10px 15px;
}

/* ===== نوافذ التقارير المخصصة ===== */
.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.modal-actions .btn-primary,
.modal-actions .btn-secondary {
    flex: 1;
    justify-content: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-gray);
}

.form-group .filter-select {
    width: 100%;
}

/* ===== قسم الأنشطة ===== */
.activities-section {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    overflow: hidden;
    margin-top: 25px;
}

.activities-timeline {
    padding: 25px;
    max-height: 800px;
    overflow-y: auto;
}

.timeline-day {
    margin-bottom: 30px;
}

.timeline-day:last-child {
    margin-bottom: 0;
}

.timeline-date {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border-left: 4px solid var(--primary-blue);
}

.timeline-date h4 {
    margin: 0;
    color: var(--dark-gray);
    font-weight: 700;
}

.activities-count {
    background: var(--primary-blue);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.timeline-activities {
    position: relative;
    padding-right: 30px;
}

.timeline-activities::before {
    content: '';
    position: absolute;
    right: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e2e8f0;
}

.timeline-item {
    position: relative;
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
    padding: 20px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.timeline-item:hover {
    background: var(--light-blue);
    transform: translateX(-5px);
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-icon {
    position: absolute;
    right: -35px;
    top: 20px;
    width: 40px;
    height: 40px;
    background: var(--primary-blue);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    box-shadow: 0 2px 4px var(--shadow);
    z-index: 2;
}

.timeline-content {
    flex: 1;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.timeline-header h5 {
    margin: 0;
    color: var(--dark-gray);
    font-weight: 600;
    flex: 1;
    line-height: 1.4;
}

.timeline-time {
    color: var(--gray);
    font-size: 0.85rem;
    font-weight: 600;
    white-space: nowrap;
    margin-right: 15px;
}

.timeline-meta {
    display: flex;
    gap: 20px;
    align-items: center;
}

.timeline-user,
.timeline-type {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--gray);
    font-size: 0.85rem;
}

.timeline-user i,
.timeline-type i {
    color: var(--primary-blue);
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    .reports-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .report-actions {
        flex-direction: column;
    }

    .modal-actions {
        flex-direction: column;
    }

    .timeline-activities {
        padding-right: 0;
    }

    .timeline-activities::before {
        display: none;
    }

    .timeline-item {
        padding: 15px;
    }

    .timeline-icon {
        position: static;
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
        flex-shrink: 0;
    }

    .timeline-header {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .timeline-time {
        margin-right: 0;
    }

    .timeline-meta {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
}

/* ===== نوافذ العملاء المنبثقة ===== */
.customer-modal {
    max-width: 700px;
    width: 95%;
}

.customer-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.modal-footer {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding: 20px 25px;
    background: var(--light-gray);
    border-top: 1px solid #e2e8f0;
}

.modal-footer .btn-primary,
.modal-footer .btn-secondary {
    padding: 12px 25px;
    font-weight: 600;
}

/* ===== تفاصيل العميل ===== */
.customer-details {
    max-width: 600px;
    width: 95%;
}

.customer-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
}

.customer-avatar {
    font-size: 4rem;
    color: var(--primary-blue);
}

.customer-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    width: 100%;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: var(--gray);
    font-size: 0.9rem;
}

.detail-item span {
    color: var(--dark-gray);
    font-weight: 500;
}

@media (max-width: 768px) {
    .customer-modal {
        max-width: 100%;
        margin: 10px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .customer-details-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn-primary,
    .modal-footer .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* ===== صفحة الإعدادات ===== */
.settings-tabs {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    overflow: hidden;
}

.tabs-header {
    display: flex;
    background: var(--light-gray);
    border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 20px 15px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-weight: 600;
    color: var(--gray);
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: var(--light-blue);
    color: var(--primary-blue);
}

.tab-btn.active {
    background: var(--white);
    color: var(--primary-blue);
    border-bottom-color: var(--primary-blue);
}

.tab-btn i {
    font-size: 1.1rem;
}

.tabs-content {
    padding: 30px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.settings-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e2e8f0;
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-section h3 {
    color: var(--dark-gray);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.account-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-item label {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 0.95rem;
}

.setting-input {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
    background: var(--white);
}

.setting-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* ===== Toggle Switches ===== */
.setting-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

.setting-toggle label {
    margin: 0;
    cursor: pointer;
}

.toggle-input {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: #cbd5e1;
    border-radius: 24px;
    cursor: pointer;
    transition: var(--transition);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 20px;
    height: 20px;
    background: var(--white);
    border-radius: 50%;
    transition: var(--transition);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked + .toggle-slider {
    background: var(--primary-blue);
}

.toggle-input:checked + .toggle-slider::before {
    transform: translateX(-26px);
}

/* ===== Range Slider ===== */
.range-input {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
    margin: 10px 0;
}

.range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-blue);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-input::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-blue);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-value {
    display: inline-block;
    margin-right: 10px;
    font-weight: 600;
    color: var(--primary-blue);
    min-width: 40px;
}

/* ===== Color Themes ===== */
.color-themes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.theme-option:hover {
    border-color: var(--primary-blue);
    transform: translateY(-2px);
}

.theme-option.active {
    border-color: var(--primary-blue);
    background: var(--light-blue);
}

.theme-preview {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.theme-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.blue-theme .theme-color.primary { background: #2563eb; }
.blue-theme .theme-color.secondary { background: #3b82f6; }
.blue-theme .theme-color.accent { background: #1d4ed8; }

.green-theme .theme-color.primary { background: #059669; }
.green-theme .theme-color.secondary { background: #10b981; }
.green-theme .theme-color.accent { background: #047857; }

.purple-theme .theme-color.primary { background: #7c3aed; }
.purple-theme .theme-color.secondary { background: #8b5cf6; }
.purple-theme .theme-color.accent { background: #6d28d9; }

/* ===== Password Change ===== */
.password-change-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 400px;
}

.password-strength {
    margin-top: 10px;
}

.password-strength-bar {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.strength-fill {
    height: 100%;
    transition: var(--transition);
}

.strength-text {
    font-size: 0.85rem;
    font-weight: 600;
}

/* ===== Settings Footer ===== */
.settings-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 25px 30px;
    background: var(--light-gray);
    border-top: 1px solid #e2e8f0;
}

.settings-footer .btn-primary,
.settings-footer .btn-secondary {
    padding: 12px 25px;
    font-weight: 600;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .tabs-header {
        flex-direction: column;
    }

    .tab-btn {
        justify-content: flex-start;
        padding: 15px 20px;
    }

    .tabs-content {
        padding: 20px;
    }

    .settings-grid,
    .account-info-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .color-themes {
        grid-template-columns: repeat(2, 1fr);
    }

    .settings-footer {
        flex-direction: column;
        padding: 20px;
    }

    .settings-footer .btn-primary,
    .settings-footer .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .setting-toggle {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* ===== الملف الشخصي المحسن ===== */
.profile-modal-overlay {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
}

.profile-modal-enhanced {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    animation: profileModalSlideIn 0.4s ease-out;
}

@keyframes profileModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.profile-header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: 25px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 20px 20px 0 0;
}

.profile-header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.profile-header-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

.profile-header-avatar:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.5);
}

.profile-header-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-header-avatar i {
    font-size: 3rem;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.profile-header-info h3 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.profile-role {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
    font-weight: 500;
}

.profile-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.profile-tabs {
    display: flex;
    flex-direction: column;
}

.profile-tabs-header {
    display: flex;
    background: var(--light-gray);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    overflow: hidden;
}

.profile-tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 600;
    color: var(--gray);
    border-bottom: 3px solid transparent;
}

.profile-tab-btn:hover {
    background: var(--light-blue);
    color: var(--primary-blue);
}

.profile-tab-btn.active {
    background: var(--white);
    color: var(--primary-blue);
    border-bottom-color: var(--primary-blue);
}

.profile-tabs-content {
    background: var(--white);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    padding: 30px;
}

.profile-tab-panel {
    display: none;
}

.profile-tab-panel.active {
    display: block;
}

/* ===== قسم المعلومات الأساسية ===== */
.profile-info-section {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.profile-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    min-width: 150px;
}

.profile-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--primary-blue);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-blue);
}

.profile-avatar-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-avatar-large i {
    font-size: 4rem;
    color: var(--primary-blue);
}

.btn-small {
    padding: 8px 15px;
    font-size: 0.85rem;
}

.profile-details-section {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.profile-detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.profile-detail-item label {
    font-weight: 600;
    color: var(--gray);
    font-size: 0.9rem;
}

.profile-detail-item span {
    color: var(--dark-gray);
    font-weight: 500;
    font-size: 1rem;
}

.role-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    width: fit-content;
}

.role-admin {
    background: #fee2e2;
    color: #991b1b;
}

.role-supervisor {
    background: #fef3c7;
    color: #92400e;
}

.role-telesales {
    background: #d1fae5;
    color: #065f46;
}

/* ===== قسم الإحصائيات المحسنة ===== */
.profile-stats-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 25px;
    margin-bottom: 35px;
}

.profile-stat-card-enhanced {
    background: var(--white);
    padding: 25px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.4s ease;
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.profile-stat-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.profile-stat-card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(37, 99, 235, 0.15);
    border-color: var(--primary-blue);
}

.profile-stat-card-enhanced:hover::before {
    transform: scaleX(1);
}

.stat-icon-enhanced {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.stat-icon-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
}

.total-activities {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.month-activities {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.customers-added {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.daily-average {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content-enhanced h4 {
    font-size: 2rem;
    font-weight: 800;
    color: var(--dark-gray);
    margin-bottom: 8px;
    line-height: 1;
}

.stat-content-enhanced p {
    color: var(--gray);
    font-weight: 600;
    font-size: 0.95rem;
    margin: 0 0 12px 0;
}

.stat-progress {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    border-radius: 3px;
    transition: width 1s ease-in-out;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== الإحصائيات الإضافية ===== */
.additional-stats {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    padding: 25px;
    margin-top: 25px;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item-small {
    background: var(--white);
    padding: 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
    border: 1px solid #e2e8f0;
}

.stat-item-small:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-blue);
}

.stat-icon-small {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.stat-details h5 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin: 0 0 5px 0;
}

.stat-details span {
    color: var(--gray);
    font-weight: 600;
    font-size: 0.85rem;
}

.profile-activity-summary {
    background: var(--light-blue);
    padding: 20px;
    border-radius: var(--border-radius);
    border-right: 4px solid var(--primary-blue);
}

.profile-activity-summary h4 {
    color: var(--dark-gray);
    font-weight: 700;
    margin-bottom: 15px;
}

.activity-summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.activity-summary-item:last-child {
    border-bottom: none;
}

.activity-summary-item label {
    font-weight: 600;
    color: var(--gray);
}

.activity-summary-item span {
    font-weight: 600;
    color: var(--dark-gray);
}

/* ===== قسم تعديل البيانات ===== */
.profile-edit-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

/* ===== تصميم متجاوب للملف الشخصي ===== */
@media (max-width: 768px) {
    .profile-modal {
        max-width: 100%;
        margin: 10px;
    }

    .profile-tabs-header {
        flex-direction: column;
    }

    .profile-tab-btn {
        justify-content: flex-start;
        padding: 12px 20px;
    }

    .profile-info-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .profile-details-section {
        grid-template-columns: 1fr;
        width: 100%;
    }

    .profile-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .activity-summary-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .profile-stats-grid {
        grid-template-columns: 1fr;
    }

    .profile-tabs-content {
        padding: 20px;
    }
}

/* ===== نافذة اقتصاص الصورة ===== */
.crop-modal-content {
    max-width: 700px;
    width: 95%;
}

.crop-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.crop-preview {
    flex: 2;
    position: relative;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.crop-preview img {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: contain;
}

.crop-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.crop-selection {
    width: 150px;
    height: 150px;
    border: 2px solid var(--primary-blue);
    border-radius: 50%;
    background: transparent;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
}

.crop-result {
    flex: 1;
    text-align: center;
}

.crop-result h4 {
    color: var(--dark-gray);
    margin-bottom: 15px;
}

.crop-result-image {
    border: 2px solid #e2e8f0;
    border-radius: 50%;
    overflow: hidden;
    width: 150px;
    height: 150px;
    margin: 0 auto;
    background: var(--light-gray);
}

.crop-result-image canvas {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

/* ===== الإشعارات المحسنة ===== */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    min-width: 300px;
    max-width: 500px;
    opacity: 0;
    transition: all 0.4s ease;
    border-left: 4px solid var(--primary-blue);
}

.notification.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.notification-success {
    border-left-color: var(--success);
}

.notification-error {
    border-left-color: var(--danger);
}

.notification-warning {
    border-left-color: var(--warning);
}

.notification-info {
    border-left-color: var(--primary-blue);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    padding-left: 15px;
}

.notification-content i {
    font-size: 1.2rem;
}

.notification-success .notification-content i {
    color: var(--success);
}

.notification-error .notification-content i {
    color: var(--danger);
}

.notification-warning .notification-content i {
    color: var(--warning);
}

.notification-info .notification-content i {
    color: var(--primary-blue);
}

.notification-content span {
    flex: 1;
    color: var(--dark-gray);
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray);
    cursor: pointer;
    padding: 15px;
    transition: var(--transition);
}

.notification-close:hover {
    color: var(--dark-gray);
}

/* ===== تحسينات إضافية للتصميم المتجاوب ===== */
@media (max-width: 768px) {
    .profile-modal-enhanced {
        max-width: 100%;
        margin: 10px;
        border-radius: 16px;
    }

    .profile-header {
        padding: 20px;
        border-radius: 16px 16px 0 0;
    }

    .profile-header-content {
        gap: 15px;
    }

    .profile-header-avatar {
        width: 60px;
        height: 60px;
    }

    .profile-header-info h3 {
        font-size: 1.2rem;
    }

    .profile-stats-grid-enhanced {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stats-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .crop-container {
        flex-direction: column;
        gap: 20px;
    }

    .notification {
        min-width: 280px;
        margin: 0 10px;
    }
}

@media (max-width: 480px) {
    .profile-stat-card-enhanced {
        padding: 20px;
        gap: 15px;
    }

    .stat-icon-enhanced {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-content-enhanced h4 {
        font-size: 1.6rem;
    }

    .additional-stats {
        padding: 20px;
    }

    .stat-item-small {
        padding: 15px;
        gap: 12px;
    }

    .stat-icon-small {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* ===== نوافذ الموردين المحسنة ===== */
.supplier-modal {
    max-width: 800px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.supplier-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-section {
    background: var(--light-gray);
    padding: 20px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-blue);
}

.form-section h4 {
    color: var(--primary-blue);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 700;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 15px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* ===== تقييم النجوم ===== */
.rating-input {
    display: flex;
    gap: 5px;
    align-items: center;
}

.rating-input input[type="radio"] {
    display: none;
}

.star-label {
    cursor: pointer;
    font-size: 1.5rem;
    color: #e2e8f0;
    transition: var(--transition);
}

.star-label:hover,
.star-label:hover ~ .star-label {
    color: #fbbf24;
}

.rating-input input[type="radio"]:checked ~ .star-label,
.rating-input input[type="radio"]:checked + .star-label {
    color: #f59e0b;
}

/* ===== نافذة تفاصيل المورد ===== */
.supplier-details-modal {
    max-width: 700px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.supplier-details {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.supplier-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.supplier-info h2 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.service-type {
    margin: 0 0 10px 0;
    opacity: 0.9;
    font-size: 1rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 5px;
}

.rating-display .fas.fa-star {
    color: #fbbf24;
    font-size: 1rem;
}

.rating-display .fas.fa-star:not(.active) {
    color: rgba(255, 255, 255, 0.3);
}

.rating-text {
    margin-right: 8px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.details-section {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    border: 1px solid #e2e8f0;
}

.details-section h4 {
    color: var(--primary-blue);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 700;
    border-bottom: 2px solid var(--light-blue);
    padding-bottom: 10px;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.contact-item:hover {
    background: var(--light-blue);
    transform: translateY(-1px);
}

.contact-item i {
    color: var(--primary-blue);
    width: 20px;
    text-align: center;
}

.contact-item span:first-of-type {
    font-weight: 600;
    color: var(--gray);
    min-width: 80px;
}

.contact-item a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
}

.contact-item a:hover {
    text-decoration: underline;
}

.address-info p {
    margin: 8px 0;
    color: var(--dark-gray);
}

.address-info strong {
    color: var(--primary-blue);
    margin-left: 5px;
}

.notes-content {
    background: var(--light-gray);
    padding: 15px;
    border-radius: var(--border-radius);
    border-right: 4px solid var(--secondary-blue);
}

.notes-content p {
    margin: 0;
    color: var(--dark-gray);
    line-height: 1.6;
}

.additional-info p {
    margin: 8px 0;
    color: var(--dark-gray);
}

.additional-info strong {
    color: var(--primary-blue);
    margin-left: 5px;
}

/* ===== تصميم متجاوب للنوافذ الجديدة ===== */
@media (max-width: 768px) {
    .supplier-modal,
    .supplier-details-modal {
        max-width: 100%;
        margin: 10px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .supplier-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .contact-grid {
        grid-template-columns: 1fr;
    }

    .contact-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .contact-item span:first-of-type {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .form-section {
        padding: 15px;
    }

    .details-section {
        padding: 15px;
    }

    .supplier-header {
        padding: 15px;
    }

    .rating-input {
        justify-content: center;
    }
}

/* ===== نوافذ الموظفين المحسنة ===== */
.employee-modal {
    max-width: 850px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.employee-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.employee-details-modal {
    max-width: 750px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.employee-details {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.employee-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px;
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    color: var(--white);
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.employee-info h2 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.job-title {
    margin: 0 0 10px 0;
    opacity: 0.9;
    font-size: 1rem;
}

.performance-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.performance-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
}

.performance-excellent {
    background: rgba(16, 185, 129, 0.2);
    color: #065f46;
}

.performance-good {
    background: rgba(59, 130, 246, 0.2);
    color: #1e40af;
}

.performance-average {
    background: rgba(245, 158, 11, 0.2);
    color: #92400e;
}

.performance-poor {
    background: rgba(239, 68, 68, 0.2);
    color: #991b1b;
}

.performance-score {
    font-weight: 700;
    font-size: 1.1rem;
}

.work-info p,
.performance-info p {
    margin: 8px 0;
    color: var(--dark-gray);
}

.work-info strong,
.performance-info strong {
    color: var(--primary-blue);
    margin-left: 5px;
}

/* ===== معاينة الصورة ===== */
.photo-preview {
    width: 100px;
    height: 100px;
    border: 2px solid #e2e8f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: 10px;
    background: var(--light-gray);
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-preview i {
    font-size: 3rem;
    color: var(--gray);
}

/* ===== تصميم متجاوب للموظفين ===== */
@media (max-width: 768px) {
    .employee-modal,
    .employee-details-modal {
        max-width: 100%;
        margin: 10px;
    }

    .employee-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .performance-display {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .photo-preview {
        width: 80px;
        height: 80px;
    }

    .photo-preview i {
        font-size: 2.5rem;
    }
}

/* ===== نوافذ عروض الأسعار المحسنة ===== */
.quotation-modal {
    max-width: 1000px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.quotation-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.quotation-details-modal {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.quotation-details {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.quotation-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px;
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: var(--white);
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.quotation-info h2 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.customer-name {
    margin: 0 0 10px 0;
    opacity: 0.9;
    font-size: 1rem;
}

.quotation-dates {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.quotation-total {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 10px;
}

/* ===== إدارة بنود العرض ===== */
.items-container {
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 20px;
    background: var(--light-gray);
}

.items-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.item-row {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.item-number {
    background: var(--primary-blue);
    color: var(--white);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.item-fields {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 15px;
    align-items: end;
}

.item-actions {
    display: flex;
    align-items: end;
    padding-bottom: 8px;
}

.btn-danger-small {
    background: #ef4444;
    color: var(--white);
    border: none;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.btn-danger-small:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.no-items {
    text-align: center;
    padding: 40px;
    color: var(--gray);
}

.no-items i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* ===== الحسابات المالية ===== */
.financial-calculations {
    background: var(--light-blue);
    padding: 20px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-blue);
}

.total-amount {
    background: var(--primary-blue);
    color: var(--white);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-top: 15px;
}

.total-amount label {
    color: var(--white) !important;
    font-weight: 700;
    font-size: 1.1rem;
}

.total-amount input {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    font-weight: 700;
    font-size: 1.2rem;
}

.total-amount input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* ===== جدول البنود في التفاصيل ===== */
.items-table {
    width: 100%;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.items-table table {
    width: 100%;
    border-collapse: collapse;
}

.items-table th,
.items-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e2e8f0;
}

.items-table th {
    background: var(--primary-blue);
    color: var(--white);
    font-weight: 600;
}

.items-table tbody tr:hover {
    background: var(--light-gray);
}

/* ===== الملخص المالي ===== */
.financial-summary {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    border: 1px solid #e2e8f0;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-row.total {
    border-top: 2px solid var(--primary-blue);
    margin-top: 10px;
    padding-top: 15px;
    font-size: 1.1rem;
}

.summary-row.total span {
    color: var(--primary-blue);
}

/* ===== المحتوى النصي ===== */
.notes-content,
.terms-content {
    background: var(--light-gray);
    padding: 15px;
    border-radius: var(--border-radius);
    border-right: 4px solid var(--secondary-blue);
    line-height: 1.6;
}

.notes-content p,
.terms-content p {
    margin: 0;
    color: var(--dark-gray);
}

/* ===== تصميم متجاوب لعروض الأسعار ===== */
@media (max-width: 768px) {
    .quotation-modal,
    .quotation-details-modal {
        max-width: 100%;
        margin: 10px;
    }

    .quotation-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .quotation-dates {
        align-items: center;
    }

    .item-fields {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .item-row {
        flex-direction: column;
        align-items: stretch;
    }

    .item-number {
        align-self: center;
        margin-bottom: 10px;
    }

    .financial-calculations {
        padding: 15px;
    }

    .items-table {
        font-size: 0.9rem;
    }

    .items-table th,
    .items-table td {
        padding: 8px 10px;
    }
}

@media (max-width: 480px) {
    .quotation-header {
        padding: 15px;
    }

    .quotation-info h2 {
        font-size: 1.3rem;
    }

    .quotation-total {
        font-size: 1.3rem;
    }

    .items-container {
        padding: 15px;
    }

    .item-row {
        padding: 15px;
    }

    .summary-row {
        font-size: 0.9rem;
    }
}
