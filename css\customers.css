/* ===== تصميم صفحة العملاء ===== */

/* ===== رأس الصفحة ===== */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 25px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
}

.page-title h2 {
    font-size: 1.8rem;
    color: var(--dark-gray);
    margin-bottom: 8px;
    font-weight: 700;
}

.page-title p {
    color: var(--gray);
    font-size: 1rem;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* ===== قسم الفلاتر ===== */
.filters-section {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    margin-bottom: 25px;
}

.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray);
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.filters-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-select {
    flex: 1;
    min-width: 150px;
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--dark-gray);
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-blue);
}

.export-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-export {
    background: var(--light-blue);
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-export:hover {
    background: var(--primary-blue);
    color: var(--white);
}

/* ===== إحصائيات سريعة ===== */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-item {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow);
}

.stat-item i {
    font-size: 2rem;
    color: var(--primary-blue);
    width: 50px;
    text-align: center;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 5px;
}

.stat-content p {
    color: var(--gray);
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== قسم الجدول ===== */
.table-section {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
    background: var(--light-gray);
}

.table-header h3 {
    font-size: 1.2rem;
    color: var(--dark-gray);
    font-weight: 700;
    margin: 0;
}

.table-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-select {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: var(--white);
    color: var(--dark-gray);
    font-size: 0.9rem;
    cursor: pointer;
}

.sort-order-btn {
    background: var(--light-blue);
    color: var(--primary-blue);
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.sort-order-btn:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.table-container {
    overflow-x: auto;
}

.customers-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.customers-table th {
    background: var(--light-gray);
    color: var(--dark-gray);
    font-weight: 700;
    padding: 15px 12px;
    text-align: right;
    border-bottom: 2px solid #e2e8f0;
    white-space: nowrap;
}

.customers-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #e2e8f0;
    color: var(--dark-gray);
}

.customers-table tbody tr {
    transition: var(--transition);
}

.customers-table tbody tr:hover {
    background: var(--light-blue);
}

/* ===== حالات العملاء ===== */
.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.status-active {
    background: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

.status-potential {
    background: #fef3c7;
    color: #92400e;
}

/* ===== أزرار الإجراءات ===== */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.action-btn {
    background: none;
    border: none;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.action-btn.view {
    color: var(--primary-blue);
}

.action-btn.view:hover {
    background: var(--light-blue);
}

.action-btn.edit {
    color: var(--warning);
}

.action-btn.edit:hover {
    background: #fef3c7;
}

.action-btn.delete {
    color: var(--danger);
}

.action-btn.delete:hover {
    background: #fee2e2;
}

/* ===== ترقيم الصفحات ===== */
.table-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e2e8f0;
}

.pagination-btn {
    background: var(--white);
    border: 2px solid #e2e8f0;
    color: var(--dark-gray);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.pagination-btn:hover {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
}

.pagination-btn.active {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: var(--gray);
    font-size: 0.9rem;
    margin: 0 15px;
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }
    
    .filters-row {
        flex-direction: column;
    }
    
    .export-actions {
        justify-content: center;
    }
    
    .stats-row {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .table-controls {
        justify-content: center;
    }
    
    .customers-table {
        font-size: 0.8rem;
    }
    
    .customers-table th,
    .customers-table td {
        padding: 10px 8px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
}

@media (max-width: 480px) {
    .stats-row {
        grid-template-columns: 1fr;
    }
    
    .table-pagination {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .pagination-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}
