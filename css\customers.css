/* ===== تصميم صفحة العملاء ===== */

/* ===== رأس الصفحة ===== */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 25px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
}

.page-title h2 {
    font-size: 1.8rem;
    color: var(--dark-gray);
    margin-bottom: 8px;
    font-weight: 700;
}

.page-title p {
    color: var(--gray);
    font-size: 1rem;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* ===== قسم الفلاتر ===== */
.filters-section {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    margin-bottom: 25px;
}

.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray);
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.filters-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-select {
    flex: 1;
    min-width: 150px;
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--dark-gray);
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-blue);
}

.export-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-export {
    background: var(--light-blue);
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-export:hover {
    background: var(--primary-blue);
    color: var(--white);
}

/* ===== إحصائيات سريعة ===== */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-item {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow);
}

.stat-item i {
    font-size: 2rem;
    color: var(--primary-blue);
    width: 50px;
    text-align: center;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 5px;
}

.stat-content p {
    color: var(--gray);
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== قسم الجدول ===== */
.table-section {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
    background: var(--light-gray);
}

.table-header h3 {
    font-size: 1.2rem;
    color: var(--dark-gray);
    font-weight: 700;
    margin: 0;
}

.table-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-select {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: var(--white);
    color: var(--dark-gray);
    font-size: 0.9rem;
    cursor: pointer;
}

.sort-order-btn {
    background: var(--light-blue);
    color: var(--primary-blue);
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.sort-order-btn:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.table-container {
    overflow-x: auto;
}

.customers-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.customers-table th {
    background: var(--light-gray);
    color: var(--dark-gray);
    font-weight: 700;
    padding: 15px 12px;
    text-align: right;
    border-bottom: 2px solid #e2e8f0;
    white-space: nowrap;
}

.customers-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #e2e8f0;
    color: var(--dark-gray);
}

.customers-table tbody tr {
    transition: var(--transition);
}

.customers-table tbody tr:hover {
    background: var(--light-blue);
}

/* ===== حالات العملاء ===== */
.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.status-active {
    background: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

.status-potential {
    background: #fef3c7;
    color: #92400e;
}

/* ===== أزرار الإجراءات ===== */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.action-btn {
    background: none;
    border: none;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.action-btn.view {
    color: var(--primary-blue);
}

.action-btn.view:hover {
    background: var(--light-blue);
}

.action-btn.edit {
    color: var(--warning);
}

.action-btn.edit:hover {
    background: #fef3c7;
}

.action-btn.delete {
    color: var(--danger);
}

.action-btn.delete:hover {
    background: #fee2e2;
}

/* ===== ترقيم الصفحات ===== */
.table-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e2e8f0;
}

.pagination-btn {
    background: var(--white);
    border: 2px solid #e2e8f0;
    color: var(--dark-gray);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.pagination-btn:hover {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
}

.pagination-btn.active {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: var(--gray);
    font-size: 0.9rem;
    margin: 0 15px;
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }
    
    .filters-row {
        flex-direction: column;
    }
    
    .export-actions {
        justify-content: center;
    }
    
    .stats-row {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .table-controls {
        justify-content: center;
    }
    
    .customers-table {
        font-size: 0.8rem;
    }
    
    .customers-table th,
    .customers-table td {
        padding: 10px 8px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
}

/* ===== أزرار الاستيراد ===== */
.btn-import {
    background: var(--success);
    color: var(--white);
    border: none;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-import:hover {
    background: #059669;
    transform: translateY(-1px);
}

/* ===== مؤشرات الأداء ===== */
.performance-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.performance-badge {
    padding: 3px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.performance-excellent {
    background: #d1fae5;
    color: #065f46;
}

.performance-good {
    background: #dbeafe;
    color: #1e40af;
}

.performance-average {
    background: #fef3c7;
    color: #92400e;
}

.performance-poor {
    background: #fee2e2;
    color: #991b1b;
}

.performance-score {
    font-size: 0.8rem;
    color: var(--gray);
    font-weight: 600;
}

/* ===== بطاقات المهام ===== */
.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.task-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0 2px 4px var(--shadow);
    border-left: 4px solid var(--primary-blue);
    transition: var(--transition);
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow);
}

.task-card.task-pending {
    border-left-color: var(--warning);
}

.task-card.task-in_progress {
    border-left-color: var(--primary-blue);
}

.task-card.task-completed {
    border-left-color: var(--success);
}

.task-card.task-cancelled {
    border-left-color: var(--danger);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.task-header h4 {
    margin: 0;
    color: var(--dark-gray);
    font-weight: 700;
    flex: 1;
}

.task-priority {
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-high {
    background: #fee2e2;
    color: #991b1b;
}

.priority-medium {
    background: #fef3c7;
    color: #92400e;
}

.priority-low {
    background: #d1fae5;
    color: #065f46;
}

.task-description {
    color: var(--gray);
    margin-bottom: 15px;
    line-height: 1.5;
}

.task-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;
}

.task-assignee {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--gray);
    font-size: 0.9rem;
}

.task-assignee i {
    color: var(--primary-blue);
}

.no-tasks {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    color: var(--gray);
}

.no-tasks i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* ===== أزرار إضافية للإجراءات ===== */
.action-btn.tasks {
    color: var(--accent-blue);
}

.action-btn.tasks:hover {
    background: #dbeafe;
}

@media (max-width: 480px) {
    .stats-row {
        grid-template-columns: 1fr;
    }

    .table-pagination {
        flex-wrap: wrap;
        gap: 5px;
    }

    .pagination-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .tasks-grid {
        grid-template-columns: 1fr;
    }

    .task-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .task-footer {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
}

/* ===== بطاقات التقارير ===== */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.report-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 6px var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.report-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px var(--shadow);
}

.report-header {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
}

.report-header i {
    font-size: 1.5rem;
    opacity: 0.8;
}

.report-content {
    padding: 25px;
}

.report-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
}

.report-stat:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--gray);
    font-weight: 600;
    font-size: 0.95rem;
}

.stat-value {
    color: var(--dark-gray);
    font-weight: 700;
    font-size: 1.1rem;
}

.report-actions {
    padding: 20px 25px;
    background: var(--light-gray);
    display: flex;
    gap: 10px;
    justify-content: center;
}

.report-actions .btn-export {
    flex: 1;
    justify-content: center;
    font-size: 0.85rem;
    padding: 10px 15px;
}

/* ===== نوافذ التقارير المخصصة ===== */
.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.modal-actions .btn-primary,
.modal-actions .btn-secondary {
    flex: 1;
    justify-content: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-gray);
}

.form-group .filter-select {
    width: 100%;
}

/* ===== قسم الأنشطة ===== */
.activities-section {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow);
    overflow: hidden;
    margin-top: 25px;
}

.activities-timeline {
    padding: 25px;
    max-height: 800px;
    overflow-y: auto;
}

.timeline-day {
    margin-bottom: 30px;
}

.timeline-day:last-child {
    margin-bottom: 0;
}

.timeline-date {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border-left: 4px solid var(--primary-blue);
}

.timeline-date h4 {
    margin: 0;
    color: var(--dark-gray);
    font-weight: 700;
}

.activities-count {
    background: var(--primary-blue);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.timeline-activities {
    position: relative;
    padding-right: 30px;
}

.timeline-activities::before {
    content: '';
    position: absolute;
    right: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e2e8f0;
}

.timeline-item {
    position: relative;
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
    padding: 20px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.timeline-item:hover {
    background: var(--light-blue);
    transform: translateX(-5px);
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-icon {
    position: absolute;
    right: -35px;
    top: 20px;
    width: 40px;
    height: 40px;
    background: var(--primary-blue);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    box-shadow: 0 2px 4px var(--shadow);
    z-index: 2;
}

.timeline-content {
    flex: 1;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.timeline-header h5 {
    margin: 0;
    color: var(--dark-gray);
    font-weight: 600;
    flex: 1;
    line-height: 1.4;
}

.timeline-time {
    color: var(--gray);
    font-size: 0.85rem;
    font-weight: 600;
    white-space: nowrap;
    margin-right: 15px;
}

.timeline-meta {
    display: flex;
    gap: 20px;
    align-items: center;
}

.timeline-user,
.timeline-type {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--gray);
    font-size: 0.85rem;
}

.timeline-user i,
.timeline-type i {
    color: var(--primary-blue);
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    .reports-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .report-actions {
        flex-direction: column;
    }

    .modal-actions {
        flex-direction: column;
    }

    .timeline-activities {
        padding-right: 0;
    }

    .timeline-activities::before {
        display: none;
    }

    .timeline-item {
        padding: 15px;
    }

    .timeline-icon {
        position: static;
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
        flex-shrink: 0;
    }

    .timeline-header {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .timeline-time {
        margin-right: 0;
    }

    .timeline-meta {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
}

/* ===== نوافذ العملاء المنبثقة ===== */
.customer-modal {
    max-width: 700px;
    width: 95%;
}

.customer-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.modal-footer {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding: 20px 25px;
    background: var(--light-gray);
    border-top: 1px solid #e2e8f0;
}

.modal-footer .btn-primary,
.modal-footer .btn-secondary {
    padding: 12px 25px;
    font-weight: 600;
}

/* ===== تفاصيل العميل ===== */
.customer-details {
    max-width: 600px;
    width: 95%;
}

.customer-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
}

.customer-avatar {
    font-size: 4rem;
    color: var(--primary-blue);
}

.customer-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    width: 100%;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: var(--gray);
    font-size: 0.9rem;
}

.detail-item span {
    color: var(--dark-gray);
    font-weight: 500;
}

@media (max-width: 768px) {
    .customer-modal {
        max-width: 100%;
        margin: 10px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .customer-details-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn-primary,
    .modal-footer .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}
