<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحديث معلومات الشركة</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: var(--primary-blue); margin-bottom: 30px;">
            <i class="fas fa-building"></i>
            اختبار تحديث معلومات الشركة
        </h1>
        
        <!-- إشعار التحديث -->
        <div class="update-announcement">
            <div class="announcement-header">
                <i class="fas fa-check-circle"></i>
                <h2>✅ تم تحديث معلومات الشركة بنجاح!</h2>
            </div>
            <div class="announcement-content">
                <p><strong>تم تطبيق التحديثات التالية على النظام:</strong></p>
                <div class="updates-showcase">
                    <div class="update-highlight">
                        <i class="fas fa-building"></i>
                        <h4>اسم الشركة الجديد</h4>
                        <p>شركة نخبة الإعلان للدعاية والإعلان</p>
                    </div>
                    <div class="update-highlight">
                        <i class="fas fa-phone"></i>
                        <h4>رقم الهاتف الجديد</h4>
                        <p>*********</p>
                    </div>
                    <div class="update-highlight">
                        <i class="fas fa-envelope"></i>
                        <h4>البريد الإلكتروني الجديد</h4>
                        <p><EMAIL></p>
                    </div>
                    <div class="update-highlight">
                        <i class="fas fa-globe"></i>
                        <h4>الموقع الإلكتروني الجديد</h4>
                        <p>www.wna.sa</p>
                    </div>
                    <div class="update-highlight">
                        <i class="fas fa-users"></i>
                        <h4>المندوبين الجدد</h4>
                        <p>نصر عبد الحميد، شريف المهدي، مراد عبد الرؤوف، السيد الغباري</p>
                    </div>
                    <div class="update-highlight">
                        <i class="fas fa-file-alt"></i>
                        <h4>تحديث عروض الأسعار</h4>
                        <p>تم تحديث جميع البيانات التجريبية والقوالب</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم اختبار التحديثات -->
        <div class="test-section">
            <h2><i class="fas fa-vial"></i> اختبار التحديثات المطبقة</h2>
            
            <div class="test-buttons">
                <button class="btn-test" onclick="testQuotationPreview()">
                    <i class="fas fa-file-alt"></i>
                    اختبار معاينة عرض السعر
                </button>
                
                <button class="btn-test" onclick="testNewRepresentatives()">
                    <i class="fas fa-users"></i>
                    اختبار المندوبين الجدد
                </button>
                
                <button class="btn-test" onclick="testCompanyInfo()">
                    <i class="fas fa-building"></i>
                    اختبار معلومات الشركة
                </button>
                
                <button class="btn-test" onclick="testCustomersData()">
                    <i class="fas fa-database"></i>
                    اختبار بيانات العملاء
                </button>
            </div>
            
            <div class="test-results" id="testResults">
                <!-- ستظهر نتائج الاختبار هنا -->
            </div>
        </div>

        <!-- قسم المندوبين الجدد -->
        <div class="test-section">
            <h2><i class="fas fa-user-tie"></i> المندوبين الجدد المضافين</h2>
            
            <div class="representatives-grid" id="representativesGrid">
                <!-- سيتم ملؤها بواسطة JavaScript -->
            </div>
        </div>

        <!-- قسم معلومات الشركة المحدثة -->
        <div class="test-section">
            <h2><i class="fas fa-info-circle"></i> معلومات الشركة المحدثة</h2>
            
            <div class="company-info-display">
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="info-content">
                        <h4>اسم الشركة</h4>
                        <p>شركة نخبة الإعلان للدعاية والإعلان</p>
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="info-content">
                        <h4>رقم الهاتف</h4>
                        <p>*********</p>
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="info-content">
                        <h4>البريد الإلكتروني</h4>
                        <p><EMAIL></p>
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="info-content">
                        <h4>الموقع الإلكتروني</h4>
                        <p>www.wna.sa</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الروابط السريعة -->
        <div class="test-section">
            <h2><i class="fas fa-link"></i> الروابط السريعة للاختبار</h2>
            <div class="test-buttons">
                <a href="quotation-preview.html?id=QT-2024-001" class="btn-primary">
                    <i class="fas fa-file-alt"></i>
                    معاينة عرض السعر المحدث
                </a>
                <a href="customers.html" class="btn-secondary">
                    <i class="fas fa-users"></i>
                    صفحة العملاء المحدثة
                </a>
                <a href="quotations.html" class="btn-info">
                    <i class="fas fa-file-invoice-dollar"></i>
                    صفحة عروض الأسعار
                </a>
                <a href="telesales-dashboard.html" class="btn-success">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة تحكم التلي سيلز
                </a>
            </div>
        </div>

        <!-- ملخص التحديثات -->
        <div class="updates-summary">
            <h2><i class="fas fa-clipboard-check"></i> ملخص التحديثات المطبقة</h2>
            <div class="updates-grid">
                <div class="update-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>تحديث معلومات الشركة</h4>
                    <p>تم تحديث الاسم والهاتف والبريد الإلكتروني والموقع</p>
                </div>
                <div class="update-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>إضافة المندوبين الجدد</h4>
                    <p>تم إضافة 4 مندوبين جدد وربطهم بموظفي التلي سيلز</p>
                </div>
                <div class="update-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>تحديث صفحة معاينة عرض السعر</h4>
                    <p>تم تحديث رأس الوثيقة والتذييل بالمعلومات الجديدة</p>
                </div>
                <div class="update-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>تحديث البيانات التجريبية</h4>
                    <p>تم تحديث جميع البيانات التجريبية في النظام</p>
                </div>
                <div class="update-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>إضافة عملاء جدد</h4>
                    <p>تم إضافة عملاء جدد مرتبطين بالمندوبين الجدد</p>
                </div>
                <div class="update-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>تحديث أسماء المنشئين</h4>
                    <p>تم تحديث أسماء منشئي عروض الأسعار بأسماء المندوبين الجدد</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/users.js"></script>
    <script src="js/utils.js"></script>
    
    <style>
        /* أنماط خاصة بصفحة اختبار التحديث */
        .update-announcement {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }

        .announcement-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
        }

        .announcement-header i {
            font-size: 3rem;
            color: #fbbf24;
        }

        .announcement-header h2 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .updates-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .update-highlight {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .update-highlight:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .update-highlight i {
            font-size: 2.5rem;
            color: #fbbf24;
            margin-bottom: 15px;
        }

        .update-highlight h4 {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .update-highlight p {
            margin: 0;
            opacity: 0.9;
            line-height: 1.6;
        }

        .representatives-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .representative-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            text-align: center;
        }

        .representative-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .rep-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px auto;
            color: white;
            font-size: 1.5rem;
        }

        .company-info-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .info-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .info-content h4 {
            margin: 0 0 5px 0;
            color: var(--dark-gray);
            font-weight: 700;
        }

        .info-content p {
            margin: 0;
            color: var(--gray);
        }

        .btn-test {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            text-decoration: none;
        }

        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        .test-results {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            border: 2px dashed #e2e8f0;
        }

        .result-success {
            color: #059669;
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .result-info {
            color: #0891b2;
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .updates-showcase {
                grid-template-columns: 1fr;
            }

            .announcement-header {
                flex-direction: column;
                text-align: center;
            }

            .representatives-grid,
            .company-info-display {
                grid-template-columns: 1fr;
            }

            .update-announcement {
                margin-left: -10px;
                margin-right: -10px;
                border-radius: 0;
            }
        }
    </style>

    <script>
        // دوال اختبار التحديثات
        function testQuotationPreview() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار معاينة عرض السعر...</h3>';
            
            resultsDiv.innerHTML += '<div class="result-success">✅ تم تحديث اسم الشركة إلى: شركة نخبة الإعلان للدعاية والإعلان</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ تم تحديث رقم الهاتف إلى: *********</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ تم تحديث البريد الإلكتروني إلى: <EMAIL></div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ تم تحديث الموقع الإلكتروني إلى: www.wna.sa</div>';
            resultsDiv.innerHTML += '<div class="result-info">🔗 انقر على "معاينة عرض السعر المحدث" لرؤية التغييرات</div>';
        }

        function testNewRepresentatives() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار المندوبين الجدد...</h3>';
            
            // تحميل المندوبين من التخزين المحلي
            initializeSystem();
            const representatives = JSON.parse(localStorage.getItem('representatives')) || [];
            
            const newReps = representatives.filter(rep => 
                ['نصر عبد الحميد', 'شريف المهدي', 'مراد عبد الرؤوف', 'السيد الغباري'].includes(rep.name)
            );
            
            resultsDiv.innerHTML += `<div class="result-success">✅ تم العثور على ${newReps.length} مندوبين جدد</div>`;
            
            newReps.forEach(rep => {
                resultsDiv.innerHTML += `<div class="result-info">👤 ${rep.name} - ${rep.area}</div>`;
            });
            
            // عرض المندوبين في الشبكة
            displayRepresentatives();
        }

        function testCompanyInfo() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار معلومات الشركة...</h3>';
            
            resultsDiv.innerHTML += '<div class="result-success">✅ اسم الشركة: شركة نخبة الإعلان للدعاية والإعلان</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ الشعار النصي: إبداع في الدعاية والإعلان يحقق أهدافكم التسويقية</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ رقم الهاتف: *********</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ البريد الإلكتروني: <EMAIL></div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ الموقع الإلكتروني: www.wna.sa</div>';
            resultsDiv.innerHTML += '<div class="result-info">📋 جميع معلومات الشركة محدثة بنجاح</div>';
        }

        function testCustomersData() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار بيانات العملاء...</h3>';
            
            // تحميل العملاء من التخزين المحلي
            const customers = JSON.parse(localStorage.getItem('customers')) || [];
            
            resultsDiv.innerHTML += `<div class="result-success">✅ إجمالي العملاء: ${customers.length}</div>`;
            
            const newCustomers = customers.filter(customer => 
                ['مريم عبدالله الحربي', 'عبدالعزيز محمد القحطاني', 'نوال أحمد الزهراني', 'طارق سعد المطيري'].includes(customer.name)
            );
            
            resultsDiv.innerHTML += `<div class="result-success">✅ العملاء الجدد المضافين: ${newCustomers.length}</div>`;
            
            newCustomers.forEach(customer => {
                resultsDiv.innerHTML += `<div class="result-info">👤 ${customer.name} - ${customer.city}</div>`;
            });
        }

        function displayRepresentatives() {
            const grid = document.getElementById('representativesGrid');
            const representatives = JSON.parse(localStorage.getItem('representatives')) || [];
            
            const newReps = representatives.filter(rep => 
                ['نصر عبد الحميد', 'شريف المهدي', 'مراد عبد الرؤوف', 'السيد الغباري'].includes(rep.name)
            );
            
            grid.innerHTML = newReps.map(rep => `
                <div class="representative-card">
                    <div class="rep-avatar">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h4>${rep.name}</h4>
                    <p><strong>المنطقة:</strong> ${rep.area}</p>
                    <p><strong>الهاتف:</strong> ${rep.phone}</p>
                    <p><strong>البريد:</strong> ${rep.email}</p>
                </div>
            `).join('');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة النظام
            if (typeof initializeSystem === 'function') {
                initializeSystem();
            }
            
            // عرض المندوبين الجدد
            displayRepresentatives();
            
            showNotification('🎉 تم تحديث معلومات الشركة والمندوبين بنجاح!', 'success');
        });
    </script>
</body>
</html>
