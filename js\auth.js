// ===== نظام المصادقة والصلاحيات المتقدم =====

// تهيئة المستخدمين في التخزين المحلي
function initializeUsers() {
    // استخدام النظام الجديد من users.js
    if (typeof initializeSystem === 'function') {
        initializeSystem();
    }
}

// تسجيل الدخول
function login(username, password) {
    const users = JSON.parse(localStorage.getItem('users')) || [];
    const user = users.find(u => u.username === username);

    if (!user) {
        return { success: false, message: 'اسم المستخدم غير صحيح' };
    }

    // التحقق من كلمة المرور باستخدام التشفير
    if (!verifyPassword(password, user.password)) {
        return { success: false, message: 'كلمة المرور غير صحيحة' };
    }

    if (user.status === 'inactive') {
        return { success: false, message: 'الحساب معطل، يرجى التواصل مع المدير' };
    }

    // حفظ بيانات المستخدم (بدون كلمة المرور)
    const userSession = {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role,
        permissions: user.permissions,
        telesalesId: user.telesalesId || null,
        loginTime: new Date().toISOString()
    };

    localStorage.setItem('currentUser', JSON.stringify(userSession));

    // تسجيل النشاط
    logActivity('user_login', `تسجيل دخول المستخدم: ${user.name}`);

    return { success: true, user: userSession };
}

// تسجيل الخروج
function logout() {
    const currentUser = getCurrentUser();
    if (currentUser) {
        logActivity('user_logout', `تسجيل خروج المستخدم: ${currentUser.name}`);
    }

    localStorage.removeItem('currentUser');
    window.location.href = 'login.html';
}

// الحصول على الصفحة الرئيسية حسب دور المستخدم
function getHomePage(userRole) {
    switch (userRole) {
        case 'admin':
            return 'dashboard.html';
        case 'telesales':
            return 'telesales-dashboard.html';
        case 'supplier_manager':
            return 'suppliers.html';
        default:
            return 'dashboard.html';
    }
}

// التحقق من إمكانية الوصول للصفحة
function canAccessPage(page, userRole) {
    const pagePermissions = {
        'dashboard.html': ['admin'],
        'customers.html': ['admin', 'telesales'],
        'suppliers.html': ['admin', 'supplier_manager'],
        'quotations.html': ['admin', 'telesales'],
        'telesales.html': ['admin'],
        'telesales-dashboard.html': ['telesales'],
        'reports.html': ['admin']
    };

    const allowedRoles = pagePermissions[page];
    return !allowedRoles || allowedRoles.includes(userRole);
}

// إعادة توجيه المستخدم للصفحة المناسبة
function redirectToHomePage() {
    const currentUser = getCurrentUser();
    if (currentUser) {
        const homePage = getHomePage(currentUser.role);
        window.location.href = homePage;
    } else {
        window.location.href = 'login.html';
    }
}

// الحصول على المستخدم الحالي
function getCurrentUser() {
    const userStr = localStorage.getItem('currentUser');
    return userStr ? JSON.parse(userStr) : null;
}

// التحقق من الصلاحيات
function hasPermission(permission) {
    const user = getCurrentUser();
    if (!user) return false;
    
    // المدير له صلاحية كاملة
    if (user.permissions.includes('all')) return true;
    
    return user.permissions.includes(permission);
}

// التحقق من تسجيل الدخول
function requireAuth() {
    const user = getCurrentUser();
    if (!user) {
        window.location.href = 'index.html';
        return false;
    }
    return true;
}

// تسجيل الأنشطة
function logActivity(type, description) {
    const activities = JSON.parse(localStorage.getItem('activities')) || [];
    const user = getCurrentUser();
    
    const activity = {
        id: Date.now(),
        type: type,
        description: description,
        user: user ? user.name : 'غير معروف',
        userId: user ? user.id : null,
        timestamp: new Date().toISOString(),
        date: new Date().toLocaleDateString('ar-SA'),
        time: new Date().toLocaleTimeString('ar-SA')
    };
    
    activities.unshift(activity);
    
    // الاحتفاظ بآخر 1000 نشاط فقط
    if (activities.length > 1000) {
        activities.splice(1000);
    }
    
    localStorage.setItem('activities', JSON.stringify(activities));
}

// الحصول على الأنشطة
function getActivities(limit = 50) {
    const activities = JSON.parse(localStorage.getItem('activities')) || [];
    return activities.slice(0, limit);
}

// معالج تسجيل الدخول
document.addEventListener('DOMContentLoaded', function() {
    initializeUsers();
    
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }
            
            // إظهار حالة التحميل
            const submitBtn = document.querySelector('.login-btn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
            submitBtn.disabled = true;
            
            // محاكاة تأخير الشبكة
            setTimeout(() => {
                const result = login(username, password);
                
                if (result.success) {
                    showAlert('تم تسجيل الدخول بنجاح', 'success');
                    setTimeout(() => {
                        const homePage = getHomePage(result.user.role);
                        window.location.href = homePage;
                    }, 1000);
                } else {
                    showAlert(result.message, 'error');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            }, 1000);
        });
    }
});

// إظهار رسائل التنبيه
function showAlert(message, type = 'info') {
    // إزالة التنبيهات السابقة
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
    `;
    
    const form = document.querySelector('.login-form');
    if (form) {
        form.insertBefore(alert, form.firstChild);
        
        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// تصدير الوظائف للاستخدام العام
window.auth = {
    login,
    logout,
    getCurrentUser,
    hasPermission,
    requireAuth,
    logActivity,
    getActivities,
    showAlert
};
