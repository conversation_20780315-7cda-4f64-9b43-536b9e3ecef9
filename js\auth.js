// ===== نظام المصادقة والصلاحيات =====

// المستخدمين الافتراضيين
const defaultUsers = [
    {
        id: 1,
        username: 'admin',
        password: 'admin123',
        role: 'manager',
        name: 'المدير العام',
        email: '<EMAIL>',
        permissions: ['all']
    },
    {
        id: 2,
        username: 'supervisor',
        password: 'super123',
        role: 'supervisor',
        name: 'المشرف',
        email: '<EMAIL>',
        permissions: ['view_all', 'edit_customers', 'edit_suppliers', 'view_telesales', 'export_data']
    },
    {
        id: 3,
        username: 'telesales',
        password: 'tele123',
        role: 'telesales',
        name: 'موظف تلي سيلز',
        email: '<EMAIL>',
        permissions: ['view_customers', 'add_customers', 'edit_own_tasks', 'view_suppliers']
    }
];

// تهيئة المستخدمين في التخزين المحلي
function initializeUsers() {
    if (!localStorage.getItem('users')) {
        localStorage.setItem('users', JSON.stringify(defaultUsers));
    }
}

// تسجيل الدخول
function login(username, password) {
    const users = JSON.parse(localStorage.getItem('users')) || defaultUsers;
    const user = users.find(u => u.username === username && u.password === password);
    
    if (user) {
        // حفظ بيانات المستخدم الحالي
        const currentUser = {
            id: user.id,
            username: user.username,
            name: user.name,
            role: user.role,
            email: user.email,
            permissions: user.permissions,
            loginTime: new Date().toISOString()
        };
        
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        
        // تسجيل عملية الدخول
        logActivity('login', `تسجيل دخول المستخدم: ${user.name}`);
        
        return { success: true, user: currentUser };
    }
    
    return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
}

// تسجيل الخروج
function logout() {
    const currentUser = getCurrentUser();
    if (currentUser) {
        logActivity('logout', `تسجيل خروج المستخدم: ${currentUser.name}`);
    }
    
    localStorage.removeItem('currentUser');
    window.location.href = 'index.html';
}

// الحصول على المستخدم الحالي
function getCurrentUser() {
    const userStr = localStorage.getItem('currentUser');
    return userStr ? JSON.parse(userStr) : null;
}

// التحقق من الصلاحيات
function hasPermission(permission) {
    const user = getCurrentUser();
    if (!user) return false;
    
    // المدير له صلاحية كاملة
    if (user.permissions.includes('all')) return true;
    
    return user.permissions.includes(permission);
}

// التحقق من تسجيل الدخول
function requireAuth() {
    const user = getCurrentUser();
    if (!user) {
        window.location.href = 'index.html';
        return false;
    }
    return true;
}

// تسجيل الأنشطة
function logActivity(type, description) {
    const activities = JSON.parse(localStorage.getItem('activities')) || [];
    const user = getCurrentUser();
    
    const activity = {
        id: Date.now(),
        type: type,
        description: description,
        user: user ? user.name : 'غير معروف',
        userId: user ? user.id : null,
        timestamp: new Date().toISOString(),
        date: new Date().toLocaleDateString('ar-SA'),
        time: new Date().toLocaleTimeString('ar-SA')
    };
    
    activities.unshift(activity);
    
    // الاحتفاظ بآخر 1000 نشاط فقط
    if (activities.length > 1000) {
        activities.splice(1000);
    }
    
    localStorage.setItem('activities', JSON.stringify(activities));
}

// الحصول على الأنشطة
function getActivities(limit = 50) {
    const activities = JSON.parse(localStorage.getItem('activities')) || [];
    return activities.slice(0, limit);
}

// معالج تسجيل الدخول
document.addEventListener('DOMContentLoaded', function() {
    initializeUsers();
    
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }
            
            // إظهار حالة التحميل
            const submitBtn = document.querySelector('.login-btn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
            submitBtn.disabled = true;
            
            // محاكاة تأخير الشبكة
            setTimeout(() => {
                const result = login(username, password);
                
                if (result.success) {
                    showAlert('تم تسجيل الدخول بنجاح', 'success');
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1000);
                } else {
                    showAlert(result.message, 'error');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            }, 1000);
        });
    }
});

// إظهار رسائل التنبيه
function showAlert(message, type = 'info') {
    // إزالة التنبيهات السابقة
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
    `;
    
    const form = document.querySelector('.login-form');
    if (form) {
        form.insertBefore(alert, form.firstChild);
        
        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// تصدير الوظائف للاستخدام العام
window.auth = {
    login,
    logout,
    getCurrentUser,
    hasPermission,
    requireAuth,
    logActivity,
    getActivities,
    showAlert
};
