<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار عروض الأسعار</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: var(--primary-blue); margin-bottom: 30px;">
            <i class="fas fa-bug"></i>
            اختبار أزرار عروض الأسعار
        </h1>
        
        <!-- قسم اختبار الأزرار -->
        <div class="test-section">
            <h2><i class="fas fa-vial"></i> اختبار الأزرار والوظائف</h2>
            
            <div class="test-buttons">
                <button class="btn-test" onclick="testPreviewButton()">
                    <i class="fas fa-file-alt"></i>
                    اختبار زر المعاينة
                </button>
                
                <button class="btn-test" onclick="testCreateQuotation()">
                    <i class="fas fa-plus"></i>
                    اختبار إنشاء عرض جديد
                </button>
                
                <button class="btn-test" onclick="testQuotationData()">
                    <i class="fas fa-database"></i>
                    اختبار البيانات
                </button>
                
                <button class="btn-test" onclick="openQuotationsPage()">
                    <i class="fas fa-external-link-alt"></i>
                    فتح صفحة عروض الأسعار
                </button>
            </div>
            
            <div class="test-results" id="testResults">
                <!-- ستظهر نتائج الاختبار هنا -->
            </div>
        </div>

        <!-- قسم المشاكل المحتملة -->
        <div class="test-section">
            <h2><i class="fas fa-exclamation-triangle"></i> المشاكل المحتملة والحلول</h2>
            
            <div class="problems-list">
                <div class="problem-item">
                    <h4><i class="fas fa-times-circle"></i> زر المعاينة لا يعمل</h4>
                    <p><strong>السبب:</strong> دالة previewQuotation لا تجد العرض أو معرف العرض غير صحيح</p>
                    <p><strong>الحل:</strong> التحقق من وجود البيانات في localStorage والتأكد من صحة معرف العرض</p>
                </div>
                
                <div class="problem-item">
                    <h4><i class="fas fa-times-circle"></i> إنشاء العرض لا يعمل</h4>
                    <p><strong>السبب:</strong> دالة generateId مفقودة أو مشكلة في حفظ البيانات</p>
                    <p><strong>الحل:</strong> إضافة دالة generateId وتصحيح دالة الحفظ</p>
                </div>
                
                <div class="problem-item">
                    <h4><i class="fas fa-times-circle"></i> البيانات لا تظهر</h4>
                    <p><strong>السبب:</strong> مشكلة في تحميل البيانات من localStorage</p>
                    <p><strong>الحل:</strong> إعادة تهيئة البيانات التجريبية</p>
                </div>
            </div>
        </div>

        <!-- قسم الروابط السريعة -->
        <div class="test-section">
            <h2><i class="fas fa-link"></i> الروابط السريعة</h2>
            <div class="test-buttons">
                <a href="quotations.html" class="btn-primary">
                    <i class="fas fa-file-invoice-dollar"></i>
                    صفحة عروض الأسعار
                </a>
                <a href="quotation-preview.html?id=Q-2024-001" class="btn-secondary">
                    <i class="fas fa-file-alt"></i>
                    معاينة العرض التجريبي
                </a>
                <a href="test-quotation-preview.html" class="btn-info">
                    <i class="fas fa-vial"></i>
                    اختبار المعاينة
                </a>
                <a href="dashboard.html" class="btn-success">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    
    <style>
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .test-section h2 {
            color: var(--primary-blue);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .btn-test {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            text-decoration: none;
        }

        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        .test-results {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            border: 2px dashed #e2e8f0;
        }

        .problems-list {
            display: grid;
            gap: 15px;
        }

        .problem-item {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 10px;
            padding: 20px;
        }

        .problem-item h4 {
            color: #dc2626;
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .problem-item p {
            margin: 5px 0;
            line-height: 1.6;
        }

        .result-success {
            color: #059669;
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .result-error {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .result-info {
            color: #0891b2;
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>

    <script>
        // دوال اختبار الأزرار
        function testPreviewButton() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار زر المعاينة...</h3>';
            
            try {
                // محاولة تحميل البيانات
                const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
                
                if (quotations.length === 0) {
                    resultsDiv.innerHTML += '<div class="result-error">❌ لا توجد عروض أسعار في البيانات</div>';
                    resultsDiv.innerHTML += '<div class="result-info">💡 سيتم إنشاء بيانات تجريبية...</div>';
                    
                    // إنشاء بيانات تجريبية
                    createSampleData();
                    resultsDiv.innerHTML += '<div class="result-success">✅ تم إنشاء البيانات التجريبية</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result-success">✅ تم العثور على ' + quotations.length + ' عروض أسعار</div>';
                }
                
                // اختبار دالة المعاينة
                const firstQuotation = quotations[0];
                if (firstQuotation) {
                    resultsDiv.innerHTML += '<div class="result-info">🔍 اختبار المعاينة للعرض: ' + firstQuotation.quoteNumber + '</div>';
                    
                    // محاولة فتح المعاينة
                    setTimeout(() => {
                        window.open(`quotation-preview.html?id=${firstQuotation.quoteNumber}`, '_blank');
                        resultsDiv.innerHTML += '<div class="result-success">✅ تم فتح المعاينة في نافذة جديدة</div>';
                    }, 1000);
                }
                
            } catch (error) {
                resultsDiv.innerHTML += '<div class="result-error">❌ خطأ: ' + error.message + '</div>';
            }
        }

        function testCreateQuotation() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار إنشاء عرض جديد...</h3>';
            
            try {
                // التحقق من وجود دالة generateId
                if (typeof generateId === 'function') {
                    resultsDiv.innerHTML += '<div class="result-success">✅ دالة generateId موجودة</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result-error">❌ دالة generateId مفقودة</div>';
                }
                
                // التحقق من وجود دالة getCurrentUser
                if (typeof getCurrentUser === 'function') {
                    resultsDiv.innerHTML += '<div class="result-success">✅ دالة getCurrentUser موجودة</div>';
                    const user = getCurrentUser();
                    resultsDiv.innerHTML += '<div class="result-info">👤 المستخدم الحالي: ' + user.name + '</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result-error">❌ دالة getCurrentUser مفقودة</div>';
                }
                
                // اختبار فتح نموذج إنشاء عرض
                resultsDiv.innerHTML += '<div class="result-info">📝 سيتم فتح صفحة عروض الأسعار لاختبار إنشاء عرض جديد</div>';
                
                setTimeout(() => {
                    window.open('quotations.html', '_blank');
                }, 2000);
                
            } catch (error) {
                resultsDiv.innerHTML += '<div class="result-error">❌ خطأ: ' + error.message + '</div>';
            }
        }

        function testQuotationData() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار البيانات...</h3>';
            
            try {
                const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
                const customers = JSON.parse(localStorage.getItem('customers')) || [];
                
                resultsDiv.innerHTML += '<div class="result-info">📊 عدد عروض الأسعار: ' + quotations.length + '</div>';
                resultsDiv.innerHTML += '<div class="result-info">👥 عدد العملاء: ' + customers.length + '</div>';
                
                if (quotations.length > 0) {
                    resultsDiv.innerHTML += '<div class="result-success">✅ البيانات متوفرة</div>';
                    
                    // عرض تفاصيل العرض الأول
                    const firstQuote = quotations[0];
                    resultsDiv.innerHTML += '<div class="result-info">📋 العرض الأول: ' + firstQuote.quoteNumber + ' - ' + firstQuote.customerName + '</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result-error">❌ لا توجد بيانات</div>';
                    resultsDiv.innerHTML += '<button onclick="createSampleData()" class="btn-test" style="margin-top: 10px;">إنشاء بيانات تجريبية</button>';
                }
                
            } catch (error) {
                resultsDiv.innerHTML += '<div class="result-error">❌ خطأ في قراءة البيانات: ' + error.message + '</div>';
            }
        }

        function openQuotationsPage() {
            window.open('quotations.html', '_blank');
        }

        function createSampleData() {
            // إنشاء بيانات تجريبية
            const sampleQuotations = [
                {
                    id: 'quote_1',
                    quoteNumber: 'Q-2024-001',
                    customerId: 'customer_1',
                    customerName: 'شركة التقنية المتقدمة',
                    quoteDate: '2024-01-15',
                    validUntil: '2024-02-15',
                    status: 'sent',
                    items: [
                        {
                            id: 'item_1',
                            description: 'تطوير موقع إلكتروني متجاوب',
                            quantity: 1,
                            unitPrice: 15000,
                            total: 15000
                        }
                    ],
                    subtotal: 15000,
                    taxRate: 15,
                    taxAmount: 2250,
                    discount: 0,
                    totalAmount: 17250,
                    notes: 'العرض شامل التصميم والبرمجة',
                    terms: 'الدفع على دفعتين',
                    createdAt: new Date().toISOString(),
                    createdBy: 'admin'
                }
            ];
            
            localStorage.setItem('quotations', JSON.stringify(sampleQuotations));
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML += '<div class="result-success">✅ تم إنشاء البيانات التجريبية بنجاح</div>';
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('🔧 صفحة اختبار أزرار عروض الأسعار جاهزة', 'info');
        });
    </script>
</body>
</html>
