<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الأنشطة - نظام إدارة العملاء والموردين</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- شريط التنقل العلوي -->
    <nav class="top-navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="menu-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="nav-title" data-ar="سجل الأنشطة" data-en="Activity Log">سجل الأنشطة</h1>
            </div>
            
            <div class="nav-right">
                <button class="lang-toggle" onclick="toggleLanguage()">
                    <i class="fas fa-globe"></i>
                    <span id="langText">English</span>
                </button>
                
                <div class="user-menu">
                    <button class="user-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-user-circle"></i>
                        <span id="userName">المستخدم</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" onclick="showProfile()">
                            <i class="fas fa-user"></i>
                            <span data-ar="الملف الشخصي" data-en="Profile">الملف الشخصي</span>
                        </a>
                        <a href="#" onclick="showSettings()">
                            <i class="fas fa-cog"></i>
                            <span data-ar="الإعدادات" data-en="Settings">الإعدادات</span>
                        </a>
                        <hr>
                        <a href="#" onclick="logout()" class="logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            <span data-ar="تسجيل الخروج" data-en="Logout">تسجيل الخروج</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-users-cog"></i>
            <span data-ar="نظام الإدارة" data-en="Management System">نظام الإدارة</span>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span data-ar="لوحة التحكم" data-en="Dashboard">لوحة التحكم</span>
            </a>
            
            <a href="customers.html" class="nav-item">
                <i class="fas fa-users"></i>
                <span data-ar="إدارة العملاء" data-en="Customer Management">إدارة العملاء</span>
                <span class="nav-badge" id="customersBadge">0</span>
            </a>
            
            <a href="suppliers.html" class="nav-item">
                <i class="fas fa-truck"></i>
                <span data-ar="إدارة الموردين" data-en="Supplier Management">إدارة الموردين</span>
                <span class="nav-badge" id="suppliersBadge">0</span>
            </a>
            
            <a href="telesales.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span data-ar="فريق التلي سيلز" data-en="Telesales Team">فريق التلي سيلز</span>
                <span class="nav-badge" id="telesalesBadge">0</span>
            </a>
            
            <div class="nav-divider"></div>
            
            <a href="reports.html" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span data-ar="التقارير" data-en="Reports">التقارير</span>
            </a>
            
            <a href="activities.html" class="nav-item active">
                <i class="fas fa-history"></i>
                <span data-ar="سجل الأنشطة" data-en="Activity Log">سجل الأنشطة</span>
            </a>
        </nav>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="content-container">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <div class="page-title">
                    <h2 data-ar="سجل الأنشطة" data-en="Activity Log">سجل الأنشطة</h2>
                    <p data-ar="تتبع جميع الأنشطة والعمليات في النظام" data-en="Track all activities and operations in the system">تتبع جميع الأنشطة والعمليات في النظام</p>
                </div>
                
                <div class="page-actions">
                    <button class="btn-primary" onclick="clearActivityLog()">
                        <i class="fas fa-trash-alt"></i>
                        <span data-ar="مسح السجل" data-en="Clear Log">مسح السجل</span>
                    </button>
                </div>
            </div>

            <!-- أدوات البحث والفلترة -->
            <div class="filters-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="البحث في الأنشطة..." data-ar-placeholder="البحث في الأنشطة..." data-en-placeholder="Search activities...">
                </div>
                
                <div class="filters-row">
                    <select id="typeFilter" class="filter-select">
                        <option value="all" data-ar="جميع الأنواع" data-en="All Types">جميع الأنواع</option>
                        <option value="login" data-ar="تسجيل دخول" data-en="Login">تسجيل دخول</option>
                        <option value="logout" data-ar="تسجيل خروج" data-en="Logout">تسجيل خروج</option>
                        <option value="customer" data-ar="أنشطة العملاء" data-en="Customer Activities">أنشطة العملاء</option>
                        <option value="supplier" data-ar="أنشطة الموردين" data-en="Supplier Activities">أنشطة الموردين</option>
                        <option value="employee" data-ar="أنشطة الموظفين" data-en="Employee Activities">أنشطة الموظفين</option>
                        <option value="export" data-ar="تصدير البيانات" data-en="Data Export">تصدير البيانات</option>
                        <option value="import" data-ar="استيراد البيانات" data-en="Data Import">استيراد البيانات</option>
                    </select>
                    
                    <select id="userFilter" class="filter-select">
                        <option value="all" data-ar="جميع المستخدمين" data-en="All Users">جميع المستخدمين</option>
                    </select>
                    
                    <select id="dateFilter" class="filter-select">
                        <option value="all" data-ar="جميع التواريخ" data-en="All Dates">جميع التواريخ</option>
                        <option value="today" data-ar="اليوم" data-en="Today">اليوم</option>
                        <option value="yesterday" data-ar="أمس" data-en="Yesterday">أمس</option>
                        <option value="week" data-ar="هذا الأسبوع" data-en="This Week">هذا الأسبوع</option>
                        <option value="month" data-ar="هذا الشهر" data-en="This Month">هذا الشهر</option>
                    </select>
                    
                    <button class="btn-secondary" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                        <span data-ar="مسح الفلاتر" data-en="Clear Filters">مسح الفلاتر</span>
                    </button>
                </div>
                
                <div class="export-actions">
                    <button class="btn-export" onclick="exportActivities('csv')">
                        <i class="fas fa-file-csv"></i>
                        <span data-ar="تصدير CSV" data-en="Export CSV">تصدير CSV</span>
                    </button>
                    
                    <button class="btn-export" onclick="exportActivities('json')">
                        <i class="fas fa-file-code"></i>
                        <span data-ar="تصدير JSON" data-en="Export JSON">تصدير JSON</span>
                    </button>
                    
                    <button class="btn-export" onclick="printActivities()">
                        <i class="fas fa-print"></i>
                        <span data-ar="طباعة" data-en="Print">طباعة</span>
                    </button>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-row">
                <div class="stat-item">
                    <i class="fas fa-history"></i>
                    <div class="stat-content">
                        <h3 id="totalActivitiesCount">0</h3>
                        <p data-ar="إجمالي الأنشطة" data-en="Total Activities">إجمالي الأنشطة</p>
                    </div>
                </div>
                
                <div class="stat-item">
                    <i class="fas fa-calendar-day"></i>
                    <div class="stat-content">
                        <h3 id="todayActivitiesCount">0</h3>
                        <p data-ar="أنشطة اليوم" data-en="Today's Activities">أنشطة اليوم</p>
                    </div>
                </div>
                
                <div class="stat-item">
                    <i class="fas fa-user-friends"></i>
                    <div class="stat-content">
                        <h3 id="activeUsersCount">0</h3>
                        <p data-ar="مستخدمين نشطون" data-en="Active Users">مستخدمين نشطون</p>
                    </div>
                </div>
                
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <div class="stat-content">
                        <h3 id="lastActivityTime">-</h3>
                        <p data-ar="آخر نشاط" data-en="Last Activity">آخر نشاط</p>
                    </div>
                </div>
            </div>

            <!-- قائمة الأنشطة -->
            <div class="activities-section">
                <div class="section-header">
                    <h3 data-ar="سجل الأنشطة التفصيلي" data-en="Detailed Activity Log">سجل الأنشطة التفصيلي</h3>
                    <div class="table-controls">
                        <select id="sortBy" class="sort-select">
                            <option value="timestamp" data-ar="ترتيب حسب الوقت" data-en="Sort by Time">ترتيب حسب الوقت</option>
                            <option value="type" data-ar="ترتيب حسب النوع" data-en="Sort by Type">ترتيب حسب النوع</option>
                            <option value="user" data-ar="ترتيب حسب المستخدم" data-en="Sort by User">ترتيب حسب المستخدم</option>
                        </select>
                        
                        <button class="sort-order-btn" onclick="toggleSortOrder()" id="sortOrderBtn">
                            <i class="fas fa-sort-amount-down"></i>
                        </button>
                    </div>
                </div>
                
                <div class="activities-timeline" id="activitiesTimeline">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                
                <div class="table-pagination" id="pagination">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
    </main>

    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/activities.js"></script>
</body>
</html>
