// ===== وظائف لوحة التحكم =====

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول
    if (!requireAuth()) return;
    
    // تهيئة لوحة التحكم
    initializeDashboard();
    
    // تحديث البيانات
    updateDashboardStats();
    updateRecentActivities();
    
    // تحديث البيانات كل 30 ثانية
    setInterval(updateDashboardStats, 30000);
});

// تهيئة لوحة التحكم
function initializeDashboard() {
    const currentUser = getCurrentUser();
    
    // تحديث اسم المستخدم
    const userNameElement = document.getElementById('userName');
    if (userNameElement) {
        userNameElement.textContent = currentUser.name;
    }
    
    // تحديث رسالة الترحيب
    const welcomeMessage = document.getElementById('welcomeMessage');
    if (welcomeMessage) {
        const currentHour = new Date().getHours();
        let greeting = '';
        
        if (currentHour < 12) {
            greeting = currentLanguage === 'ar' ? 'صباح الخير' : 'Good Morning';
        } else if (currentHour < 18) {
            greeting = currentLanguage === 'ar' ? 'مساء الخير' : 'Good Afternoon';
        } else {
            greeting = currentLanguage === 'ar' ? 'مساء الخير' : 'Good Evening';
        }
        
        welcomeMessage.textContent = `${greeting}, ${currentUser.name}`;
    }
    
    // إخفاء الروابط حسب الصلاحيات
    updateNavigationPermissions();
    
    // تسجيل دخول لوحة التحكم
    logActivity('dashboard_view', 'عرض لوحة التحكم');
}

// تحديث صلاحيات التنقل
function updateNavigationPermissions() {
    const currentUser = getCurrentUser();
    
    // روابط التنقل
    const customersLink = document.getElementById('customersLink');
    const suppliersLink = document.getElementById('suppliersLink');
    const telesalesLink = document.getElementById('telesalesLink');
    
    // التحقق من الصلاحيات
    if (!hasPermission('view_customers') && !hasPermission('all')) {
        if (customersLink) customersLink.style.display = 'none';
    }
    
    if (!hasPermission('view_suppliers') && !hasPermission('all')) {
        if (suppliersLink) suppliersLink.style.display = 'none';
    }
    
    if (!hasPermission('view_telesales') && !hasPermission('all') && currentUser.role !== 'telesales') {
        if (telesalesLink) telesalesLink.style.display = 'none';
    }
}

// تحديث إحصائيات لوحة التحكم
function updateDashboardStats() {
    // الحصول على البيانات من التخزين المحلي
    const customers = JSON.parse(localStorage.getItem('customers')) || [];
    const suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
    const telesales = JSON.parse(localStorage.getItem('telesales')) || [];
    const activities = JSON.parse(localStorage.getItem('activities')) || [];
    
    // تحديث العدادات
    updateCounter('totalCustomers', customers.length);
    updateCounter('totalSuppliers', suppliers.length);
    updateCounter('totalTelesales', telesales.length);
    
    // حساب أنشطة اليوم
    const today = new Date().toDateString();
    const todayActivities = activities.filter(activity => 
        new Date(activity.timestamp).toDateString() === today
    );
    updateCounter('todayActivities', todayActivities.length);
    
    // تحديث شارات التنقل
    updateNavBadge('customersBadge', customers.length);
    updateNavBadge('suppliersBadge', suppliers.length);
    updateNavBadge('telesalesBadge', telesales.length);
}

// تحديث العداد مع تأثير الحركة
function updateCounter(elementId, value) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const currentValue = parseInt(element.textContent) || 0;
    const increment = Math.ceil((value - currentValue) / 20);
    
    if (currentValue !== value) {
        const timer = setInterval(() => {
            const current = parseInt(element.textContent) || 0;
            if (current < value) {
                element.textContent = Math.min(current + increment, value);
            } else {
                element.textContent = value;
                clearInterval(timer);
            }
        }, 50);
    }
}

// تحديث شارة التنقل
function updateNavBadge(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
        element.style.display = value > 0 ? 'block' : 'none';
    }
}

// تحديث الأنشطة الأخيرة
function updateRecentActivities() {
    const activities = getActivities(5); // آخر 5 أنشطة
    const container = document.getElementById('recentActivities');
    
    if (!container) return;
    
    if (activities.length === 0) {
        container.innerHTML = `
            <div class="no-activities">
                <i class="fas fa-info-circle"></i>
                <p data-ar="لا توجد أنشطة حديثة" data-en="No recent activities">لا توجد أنشطة حديثة</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon">
                <i class="fas fa-${getActivityIcon(activity.type)}"></i>
            </div>
            <div class="activity-content">
                <h4>${activity.description}</h4>
                <p>${activity.user} - ${activity.date}</p>
            </div>
            <div class="activity-time">
                ${activity.time}
            </div>
        </div>
    `).join('');
    
    // تحديث النصوص حسب اللغة
    updateLanguage();
}

// الحصول على أيقونة النشاط
function getActivityIcon(type) {
    const icons = {
        'login': 'sign-in-alt',
        'logout': 'sign-out-alt',
        'customer_add': 'user-plus',
        'customer_edit': 'user-edit',
        'customer_delete': 'user-minus',
        'supplier_add': 'truck-loading',
        'supplier_edit': 'truck',
        'supplier_delete': 'truck-moving',
        'telesales_add': 'user-tie',
        'telesales_edit': 'user-cog',
        'export_data': 'file-export',
        'dashboard_view': 'tachometer-alt',
        'default': 'info-circle'
    };
    
    return icons[type] || icons['default'];
}

// تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    
    if (sidebar && mainContent) {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
        
        // حفظ حالة الشريط الجانبي
        const isCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    }
}

// تبديل قائمة المستخدم
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

// إغلاق قائمة المستخدم عند النقر خارجها
document.addEventListener('click', function(event) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (userMenu && dropdown && !userMenu.contains(event.target)) {
        dropdown.classList.remove('show');
    }
});

// عرض الملف الشخصي
function showProfile() {
    const currentUser = getCurrentUser();
    
    // إنشاء نافذة منبثقة للملف الشخصي
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-ar="الملف الشخصي" data-en="Profile">الملف الشخصي</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="profile-info">
                    <div class="profile-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="profile-details">
                        <h4>${currentUser.name}</h4>
                        <p class="role">${getRoleDisplayName(currentUser.role)}</p>
                        <p class="email">${currentUser.email}</p>
                        <p class="login-time">آخر تسجيل دخول: ${formatDate(currentUser.loginTime, true)}</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    updateLanguage();
}

// عرض الإعدادات
function showSettings() {
    alert('صفحة الإعدادات قيد التطوير');
}

// عرض التقارير
function showReports() {
    alert('صفحة التقارير قيد التطوير');
}

// عرض سجل الأنشطة
function showActivities() {
    alert('صفحة سجل الأنشطة قيد التطوير');
}

// الحصول على اسم الدور للعرض
function getRoleDisplayName(role) {
    const roles = {
        'manager': currentLanguage === 'ar' ? 'مدير' : 'Manager',
        'supervisor': currentLanguage === 'ar' ? 'مشرف' : 'Supervisor',
        'telesales': currentLanguage === 'ar' ? 'تلي سيلز' : 'Telesales'
    };
    
    return roles[role] || role;
}

// استعادة حالة الشريط الجانبي
window.addEventListener('load', function() {
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (isCollapsed) {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar && mainContent) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
        }
    }
});

// تصدير الوظائف للاستخدام العام
window.dashboard = {
    toggleSidebar,
    toggleUserMenu,
    showProfile,
    showSettings,
    showReports,
    showActivities,
    updateDashboardStats
};
