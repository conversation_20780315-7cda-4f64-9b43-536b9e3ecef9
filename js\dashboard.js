// ===== وظائف لوحة التحكم =====

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول
    if (!requireAuth()) return;
    
    // تهيئة لوحة التحكم
    initializeDashboard();
    
    // تحديث البيانات
    updateDashboardStats();
    updateRecentActivities();
    
    // تحديث البيانات كل 30 ثانية
    setInterval(updateDashboardStats, 30000);
});

// تهيئة لوحة التحكم
function initializeDashboard() {
    const currentUser = getCurrentUser();
    
    // تحديث اسم المستخدم
    const userNameElement = document.getElementById('userName');
    if (userNameElement) {
        userNameElement.textContent = currentUser.name;
    }
    
    // تحديث رسالة الترحيب
    const welcomeMessage = document.getElementById('welcomeMessage');
    if (welcomeMessage) {
        const currentHour = new Date().getHours();
        let greeting = '';
        
        if (currentHour < 12) {
            greeting = currentLanguage === 'ar' ? 'صباح الخير' : 'Good Morning';
        } else if (currentHour < 18) {
            greeting = currentLanguage === 'ar' ? 'مساء الخير' : 'Good Afternoon';
        } else {
            greeting = currentLanguage === 'ar' ? 'مساء الخير' : 'Good Evening';
        }
        
        welcomeMessage.textContent = `${greeting}, ${currentUser.name}`;
    }
    
    // إخفاء الروابط حسب الصلاحيات
    updateNavigationPermissions();
    
    // تسجيل دخول لوحة التحكم
    logActivity('dashboard_view', 'عرض لوحة التحكم');
}

// تحديث صلاحيات التنقل
function updateNavigationPermissions() {
    const currentUser = getCurrentUser();
    
    // روابط التنقل
    const customersLink = document.getElementById('customersLink');
    const suppliersLink = document.getElementById('suppliersLink');
    const telesalesLink = document.getElementById('telesalesLink');
    
    // التحقق من الصلاحيات
    if (!hasPermission('view_customers') && !hasPermission('all')) {
        if (customersLink) customersLink.style.display = 'none';
    }
    
    if (!hasPermission('view_suppliers') && !hasPermission('all')) {
        if (suppliersLink) suppliersLink.style.display = 'none';
    }
    
    if (!hasPermission('view_telesales') && !hasPermission('all') && currentUser.role !== 'telesales') {
        if (telesalesLink) telesalesLink.style.display = 'none';
    }
}

// تحديث إحصائيات لوحة التحكم
function updateDashboardStats() {
    // الحصول على البيانات من التخزين المحلي
    const customers = JSON.parse(localStorage.getItem('customers')) || [];
    const suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
    const telesales = JSON.parse(localStorage.getItem('telesales')) || [];
    const activities = JSON.parse(localStorage.getItem('activities')) || [];
    
    // تحديث العدادات
    updateCounter('totalCustomers', customers.length);
    updateCounter('totalSuppliers', suppliers.length);
    updateCounter('totalTelesales', telesales.length);
    
    // حساب أنشطة اليوم
    const today = new Date().toDateString();
    const todayActivities = activities.filter(activity => 
        new Date(activity.timestamp).toDateString() === today
    );
    updateCounter('todayActivities', todayActivities.length);
    
    // تحديث شارات التنقل
    updateNavBadge('customersBadge', customers.length);
    updateNavBadge('suppliersBadge', suppliers.length);
    updateNavBadge('telesalesBadge', telesales.length);
}

// تحديث العداد مع تأثير الحركة
function updateCounter(elementId, value) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const currentValue = parseInt(element.textContent) || 0;
    const increment = Math.ceil((value - currentValue) / 20);
    
    if (currentValue !== value) {
        const timer = setInterval(() => {
            const current = parseInt(element.textContent) || 0;
            if (current < value) {
                element.textContent = Math.min(current + increment, value);
            } else {
                element.textContent = value;
                clearInterval(timer);
            }
        }, 50);
    }
}

// تحديث شارة التنقل
function updateNavBadge(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
        element.style.display = value > 0 ? 'block' : 'none';
    }
}

// تحديث الأنشطة الأخيرة
function updateRecentActivities() {
    const activities = getActivities(5); // آخر 5 أنشطة
    const container = document.getElementById('recentActivities');
    
    if (!container) return;
    
    if (activities.length === 0) {
        container.innerHTML = `
            <div class="no-activities">
                <i class="fas fa-info-circle"></i>
                <p data-ar="لا توجد أنشطة حديثة" data-en="No recent activities">لا توجد أنشطة حديثة</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon">
                <i class="fas fa-${getActivityIcon(activity.type)}"></i>
            </div>
            <div class="activity-content">
                <h4>${activity.description}</h4>
                <p>${activity.user} - ${activity.date}</p>
            </div>
            <div class="activity-time">
                ${activity.time}
            </div>
        </div>
    `).join('');
    
    // تحديث النصوص حسب اللغة
    updateLanguage();
}

// الحصول على أيقونة النشاط
function getActivityIcon(type) {
    const icons = {
        'login': 'sign-in-alt',
        'logout': 'sign-out-alt',
        'customer_add': 'user-plus',
        'customer_edit': 'user-edit',
        'customer_delete': 'user-minus',
        'supplier_add': 'truck-loading',
        'supplier_edit': 'truck',
        'supplier_delete': 'truck-moving',
        'telesales_add': 'user-tie',
        'telesales_edit': 'user-cog',
        'export_data': 'file-export',
        'dashboard_view': 'tachometer-alt',
        'default': 'info-circle'
    };
    
    return icons[type] || icons['default'];
}

// تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    
    if (sidebar && mainContent) {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
        
        // حفظ حالة الشريط الجانبي
        const isCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    }
}

// تبديل قائمة المستخدم
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

// إغلاق قائمة المستخدم عند النقر خارجها
document.addEventListener('click', function(event) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (userMenu && dropdown && !userMenu.contains(event.target)) {
        dropdown.classList.remove('show');
    }
});

// عرض الملف الشخصي
function showProfile() {
    const currentUser = getCurrentUser();
    const activities = JSON.parse(localStorage.getItem('activities')) || [];
    const userActivities = activities.filter(activity => activity.user === currentUser.username);

    // حساب الإحصائيات
    const totalActivities = userActivities.length;
    const thisMonthActivities = userActivities.filter(activity => {
        const activityDate = new Date(activity.timestamp);
        const now = new Date();
        return activityDate.getMonth() === now.getMonth() && activityDate.getFullYear() === now.getFullYear();
    }).length;

    const customersAdded = userActivities.filter(activity => activity.type === 'customer_add').length;
    const dailyAverage = totalActivities > 0 ? Math.round(totalActivities / 30) : 0;

    // أكثر الأنشطة تكراراً
    const activityTypes = {};
    userActivities.forEach(activity => {
        activityTypes[activity.type] = (activityTypes[activity.type] || 0) + 1;
    });
    const mostFrequentActivity = Object.keys(activityTypes).reduce((a, b) =>
        activityTypes[a] > activityTypes[b] ? a : b, 'لا يوجد'
    );

    // إنشاء نافذة منبثقة للملف الشخصي
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content profile-modal">
            <div class="modal-header">
                <h3 data-ar="الملف الشخصي" data-en="Profile">الملف الشخصي</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- تبويبات الملف الشخصي -->
                <div class="profile-tabs">
                    <div class="profile-tabs-header">
                        <button class="profile-tab-btn active" onclick="switchProfileTab('info')" data-tab="info">
                            <i class="fas fa-user"></i>
                            <span data-ar="المعلومات الأساسية" data-en="Basic Info">المعلومات الأساسية</span>
                        </button>
                        <button class="profile-tab-btn" onclick="switchProfileTab('stats')" data-tab="stats">
                            <i class="fas fa-chart-bar"></i>
                            <span data-ar="الإحصائيات" data-en="Statistics">الإحصائيات</span>
                        </button>
                        <button class="profile-tab-btn" onclick="switchProfileTab('edit')" data-tab="edit">
                            <i class="fas fa-edit"></i>
                            <span data-ar="تعديل البيانات" data-en="Edit Data">تعديل البيانات</span>
                        </button>
                    </div>

                    <div class="profile-tabs-content">
                        <!-- المعلومات الأساسية -->
                        <div class="profile-tab-panel active" id="info-panel">
                            <div class="profile-info-section">
                                <div class="profile-avatar-section">
                                    <div class="profile-avatar-large">
                                        ${currentUser.avatar ? `<img src="${currentUser.avatar}" alt="صورة المستخدم">` : '<i class="fas fa-user-circle"></i>'}
                                    </div>
                                    <button class="btn-secondary btn-small" onclick="changeProfileImage()">
                                        <i class="fas fa-camera"></i>
                                        <span data-ar="تغيير الصورة" data-en="Change Photo">تغيير الصورة</span>
                                    </button>
                                </div>

                                <div class="profile-details-section">
                                    <div class="profile-detail-item">
                                        <label data-ar="الاسم الكامل:" data-en="Full Name:">الاسم الكامل:</label>
                                        <span>${currentUser.name || 'غير محدد'}</span>
                                    </div>

                                    <div class="profile-detail-item">
                                        <label data-ar="اسم المستخدم:" data-en="Username:">اسم المستخدم:</label>
                                        <span>${currentUser.username}</span>
                                    </div>

                                    <div class="profile-detail-item">
                                        <label data-ar="الدور:" data-en="Role:">الدور:</label>
                                        <span class="role-badge role-${currentUser.role}">${getRoleDisplayName(currentUser.role)}</span>
                                    </div>

                                    <div class="profile-detail-item">
                                        <label data-ar="المسمى الوظيفي:" data-en="Job Title:">المسمى الوظيفي:</label>
                                        <span>${currentUser.jobTitle || getRoleDisplayName(currentUser.role)}</span>
                                    </div>

                                    <div class="profile-detail-item">
                                        <label data-ar="البريد الإلكتروني:" data-en="Email:">البريد الإلكتروني:</label>
                                        <span>${currentUser.email || 'غير محدد'}</span>
                                    </div>

                                    <div class="profile-detail-item">
                                        <label data-ar="رقم الجوال:" data-en="Phone:">رقم الجوال:</label>
                                        <span>${currentUser.phone || 'غير محدد'}</span>
                                    </div>

                                    <div class="profile-detail-item">
                                        <label data-ar="تاريخ الانضمام:" data-en="Join Date:">تاريخ الانضمام:</label>
                                        <span>${formatDate(currentUser.joinDate || currentUser.createdAt, true)}</span>
                                    </div>

                                    <div class="profile-detail-item">
                                        <label data-ar="آخر تسجيل دخول:" data-en="Last Login:">آخر تسجيل دخول:</label>
                                        <span>${formatDate(currentUser.loginTime, true)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الإحصائيات -->
                        <div class="profile-tab-panel" id="stats-panel">
                            <div class="profile-stats-grid">
                                <div class="profile-stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-history"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h4>${totalActivities}</h4>
                                        <p data-ar="إجمالي الأنشطة" data-en="Total Activities">إجمالي الأنشطة</p>
                                    </div>
                                </div>

                                <div class="profile-stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-month"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h4>${thisMonthActivities}</h4>
                                        <p data-ar="أنشطة هذا الشهر" data-en="This Month's Activities">أنشطة هذا الشهر</p>
                                    </div>
                                </div>

                                <div class="profile-stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h4>${customersAdded}</h4>
                                        <p data-ar="عملاء مضافين" data-en="Customers Added">عملاء مضافين</p>
                                    </div>
                                </div>

                                <div class="profile-stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h4>${dailyAverage}</h4>
                                        <p data-ar="معدل يومي" data-en="Daily Average">معدل يومي</p>
                                    </div>
                                </div>
                            </div>

                            <div class="profile-activity-summary">
                                <h4 data-ar="ملخص الأنشطة" data-en="Activity Summary">ملخص الأنشطة</h4>
                                <div class="activity-summary-item">
                                    <label data-ar="أكثر الأنشطة تكراراً:" data-en="Most Frequent Activity:">أكثر الأنشطة تكراراً:</label>
                                    <span>${getActivityTypeDisplayName(mostFrequentActivity)}</span>
                                </div>
                                <div class="activity-summary-item">
                                    <label data-ar="معدل النشاط الأسبوعي:" data-en="Weekly Activity Rate:">معدل النشاط الأسبوعي:</label>
                                    <span>${Math.round(totalActivities / 4)} نشاط</span>
                                </div>
                            </div>
                        </div>

                        <!-- تعديل البيانات -->
                        <div class="profile-tab-panel" id="edit-panel">
                            <form id="profileEditForm" class="profile-edit-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="editFullName" data-ar="الاسم الكامل" data-en="Full Name">الاسم الكامل</label>
                                        <input type="text" id="editFullName" name="fullName" value="${currentUser.name || ''}" placeholder="أدخل الاسم الكامل">
                                    </div>

                                    <div class="form-group">
                                        <label for="editJobTitle" data-ar="المسمى الوظيفي" data-en="Job Title">المسمى الوظيفي</label>
                                        <input type="text" id="editJobTitle" name="jobTitle" value="${currentUser.jobTitle || ''}" placeholder="أدخل المسمى الوظيفي">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="editEmail" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</label>
                                        <input type="email" id="editEmail" name="email" value="${currentUser.email || ''}" placeholder="<EMAIL>">
                                    </div>

                                    <div class="form-group">
                                        <label for="editPhone" data-ar="رقم الجوال" data-en="Phone">رقم الجوال</label>
                                        <input type="tel" id="editPhone" name="phone" value="${currentUser.phone || ''}" placeholder="05xxxxxxxx">
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="button" class="btn-primary" onclick="saveProfileChanges()">
                                        <i class="fas fa-save"></i>
                                        <span data-ar="حفظ التغييرات" data-en="Save Changes">حفظ التغييرات</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    updateLanguage();

    // إضافة ملف الصورة المخفي
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.id = 'profileImageInput';
    fileInput.accept = 'image/png,image/jpeg,image/jpg';
    fileInput.style.display = 'none';
    fileInput.onchange = handleProfileImageUpload;
    document.body.appendChild(fileInput);
}

// عرض الإعدادات
function showSettings() {
    alert('صفحة الإعدادات قيد التطوير');
}

// عرض التقارير
function showReports() {
    window.location.href = 'reports.html';
}

// عرض سجل الأنشطة
function showActivities() {
    window.location.href = 'activities.html';
}

// الحصول على اسم الدور للعرض
function getRoleDisplayName(role) {
    const roles = {
        'manager': currentLanguage === 'ar' ? 'مدير' : 'Manager',
        'supervisor': currentLanguage === 'ar' ? 'مشرف' : 'Supervisor',
        'telesales': currentLanguage === 'ar' ? 'تلي سيلز' : 'Telesales'
    };
    
    return roles[role] || role;
}

// استعادة حالة الشريط الجانبي
window.addEventListener('load', function() {
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (isCollapsed) {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar && mainContent) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
        }
    }
});

// تبديل تبويبات الملف الشخصي
function switchProfileTab(tabName) {
    // إزالة الفئة النشطة من جميع التبويبات
    document.querySelectorAll('.profile-tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.profile-tab-panel').forEach(panel => panel.classList.remove('active'));

    // إضافة الفئة النشطة للتبويب المحدد
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-panel`).classList.add('active');
}

// تغيير صورة الملف الشخصي
function changeProfileImage() {
    const fileInput = document.getElementById('profileImageInput');
    if (fileInput) {
        fileInput.click();
    }
}

// معالجة رفع صورة الملف الشخصي
function handleProfileImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.match(/^image\/(png|jpeg|jpg)$/)) {
        alert('يرجى اختيار صورة بصيغة PNG أو JPG فقط');
        return;
    }

    // التحقق من حجم الملف (أقل من 2MB)
    if (file.size > 2 * 1024 * 1024) {
        alert('حجم الصورة يجب أن يكون أقل من 2 ميجابايت');
        return;
    }

    // قراءة الملف وعرضه
    const reader = new FileReader();
    reader.onload = function(e) {
        const imageData = e.target.result;

        // تحديث صورة المستخدم
        const currentUser = getCurrentUser();
        currentUser.avatar = imageData;
        localStorage.setItem('currentUser', JSON.stringify(currentUser));

        // تحديث الصورة في النافذة المنبثقة
        const avatarSection = document.querySelector('.profile-avatar-large');
        if (avatarSection) {
            avatarSection.innerHTML = `<img src="${imageData}" alt="صورة المستخدم">`;
        }

        // تسجيل النشاط
        logActivity('profile_image_update', 'تحديث صورة الملف الشخصي');

        alert('تم تحديث صورة الملف الشخصي بنجاح');
    };

    reader.readAsDataURL(file);

    // مسح قيمة input
    event.target.value = '';
}

// حفظ تغييرات الملف الشخصي
function saveProfileChanges() {
    const form = document.getElementById('profileEditForm');
    const formData = new FormData(form);

    const fullName = formData.get('fullName').trim();
    const jobTitle = formData.get('jobTitle').trim();
    const email = formData.get('email').trim();
    const phone = formData.get('phone').trim();

    // التحقق من الحقول المطلوبة
    if (!fullName) {
        alert('يرجى إدخال الاسم الكامل');
        document.getElementById('editFullName').focus();
        return;
    }

    // التحقق من صحة البريد الإلكتروني
    if (email && !validateEmail(email)) {
        alert('البريد الإلكتروني غير صحيح');
        document.getElementById('editEmail').focus();
        return;
    }

    // التحقق من صحة رقم الجوال
    if (phone && !validatePhone(phone)) {
        alert('رقم الجوال غير صحيح');
        document.getElementById('editPhone').focus();
        return;
    }

    // تحديث بيانات المستخدم
    const currentUser = getCurrentUser();
    currentUser.name = fullName;
    currentUser.jobTitle = jobTitle;
    currentUser.email = email;
    currentUser.phone = phone;

    // حفظ البيانات
    localStorage.setItem('currentUser', JSON.stringify(currentUser));

    // تحديث اسم المستخدم في الواجهة
    const userNameElement = document.getElementById('userName');
    if (userNameElement) {
        userNameElement.textContent = fullName;
    }

    // تحديث البيانات في تبويب المعلومات الأساسية
    updateProfileInfoDisplay(currentUser);

    // تسجيل النشاط
    logActivity('profile_update', 'تحديث بيانات الملف الشخصي');

    alert('تم حفظ التغييرات بنجاح');
}

// تحديث عرض معلومات الملف الشخصي
function updateProfileInfoDisplay(user) {
    const detailItems = document.querySelectorAll('.profile-detail-item');
    detailItems.forEach(item => {
        const label = item.querySelector('label').textContent;
        const span = item.querySelector('span');

        if (label.includes('الاسم الكامل') || label.includes('Full Name')) {
            span.textContent = user.name || 'غير محدد';
        } else if (label.includes('المسمى الوظيفي') || label.includes('Job Title')) {
            span.textContent = user.jobTitle || getRoleDisplayName(user.role);
        } else if (label.includes('البريد الإلكتروني') || label.includes('Email')) {
            span.textContent = user.email || 'غير محدد';
        } else if (label.includes('رقم الجوال') || label.includes('Phone')) {
            span.textContent = user.phone || 'غير محدد';
        }
    });
}

// الحصول على اسم نوع النشاط للعرض (نسخة مبسطة)
function getActivityTypeDisplayName(type) {
    const types = {
        'login': currentLanguage === 'ar' ? 'تسجيل دخول' : 'Login',
        'logout': currentLanguage === 'ar' ? 'تسجيل خروج' : 'Logout',
        'customer_add': currentLanguage === 'ar' ? 'إضافة عميل' : 'Add Customer',
        'customer_edit': currentLanguage === 'ar' ? 'تعديل عميل' : 'Edit Customer',
        'supplier_add': currentLanguage === 'ar' ? 'إضافة مورد' : 'Add Supplier',
        'dashboard_view': currentLanguage === 'ar' ? 'عرض لوحة التحكم' : 'View Dashboard'
    };

    return types[type] || type;
}

// تصدير الوظائف للاستخدام العام
window.dashboard = {
    toggleSidebar,
    toggleUserMenu,
    showProfile,
    showSettings,
    showReports,
    showActivities,
    updateDashboardStats,
    switchProfileTab,
    changeProfileImage,
    saveProfileChanges
};
