<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المرحلة الأولى - تحسين الملف الشخصي وتصدير Excel</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div style="padding: 20px; max-width: 1200px; margin: 0 auto;">
        <h1 style="text-align: center; color: var(--primary-blue); margin-bottom: 30px;">
            <i class="fas fa-test-tube"></i>
            اختبار المرحلة الأولى - التحسينات الجديدة
        </h1>
        
        <!-- قسم اختبار الملف الشخصي المحسن -->
        <div class="test-section">
            <h2><i class="fas fa-user-circle"></i> اختبار الملف الشخصي المحسن</h2>
            <div class="test-buttons">
                <button class="btn-primary" onclick="testEnhancedProfile()">
                    <i class="fas fa-user"></i>
                    عرض الملف الشخصي المحسن
                </button>
                <button class="btn-secondary" onclick="testImageCrop()">
                    <i class="fas fa-crop"></i>
                    اختبار اقتصاص الصورة
                </button>
                <button class="btn-export" onclick="testNotifications()">
                    <i class="fas fa-bell"></i>
                    اختبار الإشعارات
                </button>
            </div>
            <div class="test-description">
                <p><strong>الميزات الجديدة:</strong></p>
                <ul>
                    <li>نافذة أكبر (900px) مع تصميم محسن</li>
                    <li>إحصائيات مع مؤشرات تقدم ملونة</li>
                    <li>إحصائيات إضافية (أداء الأسبوع، معدل الإنجاز، أكثر الأيام نشاطاً)</li>
                    <li>اقتصاص الصورة مع معاينة فورية</li>
                    <li>إشعارات محسنة مع أيقونات ملونة</li>
                    <li>انتقالات CSS سلسة وتأثيرات بصرية</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار تصدير Excel -->
        <div class="test-section">
            <h2><i class="fas fa-file-excel"></i> اختبار تصدير Excel المتقدم</h2>
            <div class="test-buttons">
                <button class="btn-export excel-btn" onclick="testExcelExport()">
                    <i class="fas fa-file-excel"></i>
                    تصدير بيانات تجريبية إلى Excel
                </button>
                <button class="btn-export pdf-btn" onclick="testPDFExport()">
                    <i class="fas fa-file-pdf"></i>
                    تصدير بيانات تجريبية إلى PDF
                </button>
                <button class="btn-secondary" onclick="generateTestData()">
                    <i class="fas fa-database"></i>
                    إنشاء بيانات تجريبية
                </button>
            </div>
            <div class="test-description">
                <p><strong>الميزات الجديدة:</strong></p>
                <ul>
                    <li>تصدير Excel مع تنسيق احترافي (ألوان، خطوط، حدود)</li>
                    <li>رؤوس أعمدة ثنائية اللغة</li>
                    <li>معلومات التصدير (تاريخ، مستخدم، عدد السجلات)</li>
                    <li>تنسيق الخلايا مع ألوان متدرجة</li>
                    <li>مؤشرات تحميل أثناء التصدير</li>
                    <li>تصدير PDF مع جداول منسقة</li>
                </ul>
            </div>
        </div>

        <!-- قسم البيانات التجريبية -->
        <div class="test-section">
            <h2><i class="fas fa-table"></i> البيانات التجريبية</h2>
            <div id="testDataTable" class="table-container">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>

        <!-- قسم الإحصائيات -->
        <div class="test-section">
            <h2><i class="fas fa-chart-bar"></i> معاينة الإحصائيات المحسنة</h2>
            <div class="profile-stats-grid-enhanced">
                <div class="profile-stat-card-enhanced">
                    <div class="stat-icon-enhanced total-activities">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="stat-content-enhanced">
                        <h4>156</h4>
                        <p>إجمالي الأنشطة</p>
                        <div class="stat-progress">
                            <div class="progress-bar" style="width: 78%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-stat-card-enhanced">
                    <div class="stat-icon-enhanced month-activities">
                        <i class="fas fa-calendar-month"></i>
                    </div>
                    <div class="stat-content-enhanced">
                        <h4>42</h4>
                        <p>أنشطة هذا الشهر</p>
                        <div class="stat-progress">
                            <div class="progress-bar" style="width: 84%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-stat-card-enhanced">
                    <div class="stat-icon-enhanced customers-added">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="stat-content-enhanced">
                        <h4>18</h4>
                        <p>عملاء مضافين</p>
                        <div class="stat-progress">
                            <div class="progress-bar" style="width: 90%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-stat-card-enhanced">
                    <div class="stat-icon-enhanced daily-average">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content-enhanced">
                        <h4>5.2</h4>
                        <p>معدل يومي</p>
                        <div class="stat-progress">
                            <div class="progress-bar" style="width: 52%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط التنقل -->
        <div class="test-section">
            <h2><i class="fas fa-link"></i> روابط التنقل</h2>
            <div class="test-buttons">
                <a href="index.html" class="btn-secondary">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
                <a href="dashboard.html" class="btn-primary">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="customers.html" class="btn-export">
                    <i class="fas fa-users"></i>
                    صفحة العملاء
                </a>
                <a href="settings.html" class="btn-secondary">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/customers.js"></script>

    <script>
        // إعداد مستخدم تجريبي
        if (!localStorage.getItem('currentUser')) {
            const testUser = {
                username: 'test_admin',
                name: 'أحمد محمد المدير',
                role: 'admin',
                email: '<EMAIL>',
                phone: '0501234567',
                jobTitle: 'مدير النظام',
                loginTime: new Date().toISOString(),
                joinDate: '2024-01-01',
                avatar: null
            };
            localStorage.setItem('currentUser', JSON.stringify(testUser));
        }

        // إنشاء بيانات تجريبية للأنشطة
        if (!localStorage.getItem('activities')) {
            const activities = [];
            const activityTypes = ['login', 'customer_add', 'customer_edit', 'export_excel', 'dashboard_view'];
            const currentUser = JSON.parse(localStorage.getItem('currentUser'));
            
            for (let i = 0; i < 50; i++) {
                const date = new Date();
                date.setDate(date.getDate() - Math.floor(Math.random() * 30));
                
                activities.push({
                    id: 'activity_' + i,
                    type: activityTypes[Math.floor(Math.random() * activityTypes.length)],
                    description: `نشاط تجريبي رقم ${i + 1}`,
                    user: currentUser.username,
                    timestamp: date.toISOString()
                });
            }
            
            localStorage.setItem('activities', JSON.stringify(activities));
        }

        // اختبار الملف الشخصي المحسن
        function testEnhancedProfile() {
            if (typeof showProfile === 'function') {
                showProfile();
            } else {
                alert('يجب تحميل dashboard.js أولاً');
            }
        }

        // اختبار اقتصاص الصورة
        function testImageCrop() {
            // إنشاء صورة تجريبية
            const canvas = document.createElement('canvas');
            canvas.width = 300;
            canvas.height = 300;
            const ctx = canvas.getContext('2d');
            
            // رسم خلفية ملونة
            const gradient = ctx.createLinearGradient(0, 0, 300, 300);
            gradient.addColorStop(0, '#2563eb');
            gradient.addColorStop(1, '#3b82f6');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 300, 300);
            
            // رسم نص
            ctx.fillStyle = 'white';
            ctx.font = '24px Cairo';
            ctx.textAlign = 'center';
            ctx.fillText('صورة تجريبية', 150, 150);
            
            const imageData = canvas.toDataURL('image/png');
            
            if (typeof showImageCropModal === 'function') {
                showImageCropModal(imageData, (croppedImage) => {
                    showNotification('تم اقتصاص الصورة بنجاح!', 'success');
                });
            } else {
                alert('يجب تحميل dashboard.js أولاً');
            }
        }

        // اختبار الإشعارات
        function testNotifications() {
            if (typeof showNotification === 'function') {
                showNotification('هذا إشعار نجاح!', 'success');
                setTimeout(() => showNotification('هذا إشعار خطأ!', 'error'), 1000);
                setTimeout(() => showNotification('هذا إشعار تحذير!', 'warning'), 2000);
                setTimeout(() => showNotification('هذا إشعار معلومات!', 'info'), 3000);
            } else {
                alert('يجب تحميل dashboard.js أولاً');
            }
        }

        // اختبار تصدير Excel
        async function testExcelExport() {
            const testData = generateSampleData();
            const headers = [
                { key: 'name', label: 'الاسم' },
                { key: 'email', label: 'البريد الإلكتروني' },
                { key: 'phone', label: 'الهاتف' },
                { key: 'city', label: 'المدينة' },
                { key: 'date', label: 'التاريخ' }
            ];

            try {
                const success = await exportToExcel(testData, 'بيانات_تجريبية', headers, {
                    sheetName: 'البيانات التجريبية',
                    title: 'تقرير البيانات التجريبية'
                });
                
                if (success) {
                    showNotification('تم تصدير البيانات إلى Excel بنجاح!', 'success');
                }
            } catch (error) {
                showNotification('حدث خطأ في التصدير: ' + error.message, 'error');
            }
        }

        // اختبار تصدير PDF
        async function testPDFExport() {
            const testData = generateSampleData();
            const headers = [
                { key: 'name', label: 'الاسم' },
                { key: 'email', label: 'البريد' },
                { key: 'city', label: 'المدينة' }
            ];

            try {
                const success = await exportToPDF(testData, 'تقرير_تجريبي', headers, {
                    title: 'تقرير البيانات التجريبية'
                });
                
                if (success) {
                    showNotification('تم تصدير التقرير إلى PDF بنجاح!', 'success');
                }
            } catch (error) {
                showNotification('حدث خطأ في التصدير: ' + error.message, 'error');
            }
        }

        // إنشاء بيانات تجريبية
        function generateSampleData() {
            return [
                { name: 'أحمد محمد', email: '<EMAIL>', phone: '0501234567', city: 'الرياض', date: '2024-01-15' },
                { name: 'فاطمة أحمد', email: '<EMAIL>', phone: '0507654321', city: 'جدة', date: '2024-01-14' },
                { name: 'محمد عبدالله', email: '<EMAIL>', phone: '0551234567', city: 'الدمام', date: '2024-01-13' },
                { name: 'نورا سالم', email: '<EMAIL>', phone: '0561234567', city: 'مكة', date: '2024-01-12' },
                { name: 'خالد أحمد', email: '<EMAIL>', phone: '0571234567', city: 'المدينة', date: '2024-01-11' }
            ];
        }

        // عرض البيانات التجريبية
        function generateTestData() {
            const data = generateSampleData();
            const tableContainer = document.getElementById('testDataTable');
            
            let tableHTML = `
                <table class="customers-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>المدينة</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.forEach(item => {
                tableHTML += `
                    <tr>
                        <td>${item.name}</td>
                        <td>${item.email}</td>
                        <td>${item.phone}</td>
                        <td>${item.city}</td>
                        <td>${item.date}</td>
                    </tr>
                `;
            });
            
            tableHTML += '</tbody></table>';
            tableContainer.innerHTML = tableHTML;
            
            showNotification('تم إنشاء البيانات التجريبية!', 'info');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            generateTestData();
        });
    </script>

    <style>
        .test-section {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 8px var(--shadow);
            border-left: 4px solid var(--primary-blue);
        }

        .test-section h2 {
            color: var(--primary-blue);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .test-description {
            background: var(--light-gray);
            padding: 20px;
            border-radius: var(--border-radius);
            border-right: 4px solid var(--secondary-blue);
        }

        .test-description ul {
            margin: 10px 0;
            padding-right: 20px;
        }

        .test-description li {
            margin-bottom: 8px;
            color: var(--dark-gray);
        }

        @media (max-width: 768px) {
            .test-buttons {
                flex-direction: column;
            }
            
            .test-buttons .btn-primary,
            .test-buttons .btn-secondary,
            .test-buttons .btn-export {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</body>
</html>
