<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح تسجيل الدخول</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: var(--primary-blue); margin-bottom: 30px;">
            <i class="fas fa-key"></i>
            اختبار إصلاح تسجيل الدخول
        </h1>
        
        <!-- إشعار الإصلاح -->
        <div class="fix-announcement">
            <div class="announcement-header">
                <i class="fas fa-check-circle"></i>
                <h2>✅ تم إصلاح مشكلة تسجيل الدخول!</h2>
            </div>
            <div class="announcement-content">
                <p><strong>المشاكل التي تم إصلاحها:</strong></p>
                <div class="fixes-showcase">
                    <div class="fix-highlight">
                        <i class="fas fa-file-plus"></i>
                        <h4>إنشاء صفحة تسجيل الدخول</h4>
                        <p>تم إنشاء صفحة login.html جديدة ومحسنة</p>
                    </div>
                    <div class="fix-highlight">
                        <i class="fas fa-sync-alt"></i>
                        <h4>تحديث دالة login</h4>
                        <p>تم تحديث دالة تسجيل الدخول لتدعم async/await</p>
                    </div>
                    <div class="fix-highlight">
                        <i class="fas fa-route"></i>
                        <h4>إصلاح التوجيه</h4>
                        <p>تم إصلاح index.html ليوجه إلى صفحة تسجيل الدخول</p>
                    </div>
                    <div class="fix-highlight">
                        <i class="fas fa-user-check"></i>
                        <h4>حسابات تجريبية</h4>
                        <p>تم إضافة حسابات تجريبية للاختبار السريع</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم اختبار تسجيل الدخول -->
        <div class="test-section">
            <h2><i class="fas fa-vial"></i> اختبار تسجيل الدخول</h2>
            
            <div class="test-buttons">
                <button class="btn-test" onclick="testLoginPage()">
                    <i class="fas fa-sign-in-alt"></i>
                    اختبار صفحة تسجيل الدخول
                </button>
                
                <button class="btn-test" onclick="testLoginFunction()">
                    <i class="fas fa-code"></i>
                    اختبار دالة تسجيل الدخول
                </button>
                
                <button class="btn-test" onclick="testUserAccounts()">
                    <i class="fas fa-users"></i>
                    اختبار الحسابات التجريبية
                </button>
                
                <button class="btn-test" onclick="testRedirection()">
                    <i class="fas fa-route"></i>
                    اختبار إعادة التوجيه
                </button>
            </div>
            
            <div class="test-results" id="testResults">
                <!-- ستظهر نتائج الاختبار هنا -->
            </div>
        </div>

        <!-- قسم الحسابات التجريبية -->
        <div class="test-section">
            <h2><i class="fas fa-user-friends"></i> الحسابات التجريبية المتاحة</h2>
            
            <div class="accounts-grid">
                <div class="account-card admin">
                    <div class="account-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="account-info">
                        <h4>مدير النظام</h4>
                        <p><strong>اسم المستخدم:</strong> admin</p>
                        <p><strong>كلمة المرور:</strong> Admin@2024</p>
                        <p><strong>الصلاحيات:</strong> جميع الصلاحيات</p>
                    </div>
                    <button class="test-login-btn" onclick="testLogin('admin', 'Admin@2024')">
                        <i class="fas fa-play"></i>
                        اختبار تسجيل الدخول
                    </button>
                </div>

                <div class="account-card telesales">
                    <div class="account-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="account-info">
                        <h4>موظفة تلي سيلز</h4>
                        <p><strong>اسم المستخدم:</strong> suhailah.azhari</p>
                        <p><strong>كلمة المرور:</strong> Suhailah@2024</p>
                        <p><strong>الصلاحيات:</strong> العملاء وعروض الأسعار</p>
                    </div>
                    <button class="test-login-btn" onclick="testLogin('suhailah.azhari', 'Suhailah@2024')">
                        <i class="fas fa-play"></i>
                        اختبار تسجيل الدخول
                    </button>
                </div>

                <div class="account-card supplier">
                    <div class="account-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="account-info">
                        <h4>مدير موردين</h4>
                        <p><strong>اسم المستخدم:</strong> emad.supplier</p>
                        <p><strong>كلمة المرور:</strong> Emad@2024</p>
                        <p><strong>الصلاحيات:</strong> إدارة الموردين</p>
                    </div>
                    <button class="test-login-btn" onclick="testLogin('emad.supplier', 'Emad@2024')">
                        <i class="fas fa-play"></i>
                        اختبار تسجيل الدخول
                    </button>
                </div>
            </div>
        </div>

        <!-- قسم الروابط السريعة -->
        <div class="test-section">
            <h2><i class="fas fa-link"></i> الروابط السريعة</h2>
            <div class="test-buttons">
                <a href="login.html" class="btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    صفحة تسجيل الدخول الجديدة
                </a>
                <a href="index.html" class="btn-secondary">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
                <a href="dashboard.html" class="btn-info">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة تحكم المدير
                </a>
                <a href="telesales-dashboard.html" class="btn-success">
                    <i class="fas fa-headset"></i>
                    لوحة تحكم التلي سيلز
                </a>
            </div>
        </div>

        <!-- ملخص الإصلاحات -->
        <div class="summary-section">
            <h2><i class="fas fa-clipboard-check"></i> ملخص الإصلاحات</h2>
            <div class="summary-stats">
                <div class="summary-stat">
                    <div class="stat-number">4</div>
                    <div class="stat-label">مشاكل تم إصلاحها</div>
                </div>
                <div class="summary-stat">
                    <div class="stat-number">1</div>
                    <div class="stat-label">صفحة جديدة تم إنشاؤها</div>
                </div>
                <div class="summary-stat">
                    <div class="stat-number">3</div>
                    <div class="stat-label">ملفات تم تحديثها</div>
                </div>
                <div class="summary-stat">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">معدل نجاح الإصلاح</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/users.js"></script>
    <script src="js/utils.js"></script>
    
    <style>
        /* أنماط خاصة بصفحة اختبار تسجيل الدخول */
        .fix-announcement {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(5, 150, 105, 0.3);
        }

        .announcement-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
        }

        .announcement-header i {
            font-size: 3rem;
            color: #fbbf24;
        }

        .announcement-header h2 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .fixes-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .fix-highlight {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .fix-highlight:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .fix-highlight i {
            font-size: 2.5rem;
            color: #fbbf24;
            margin-bottom: 15px;
        }

        .fix-highlight h4 {
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .fix-highlight p {
            margin: 0;
            opacity: 0.9;
            line-height: 1.6;
        }

        .accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .account-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            text-align: center;
        }

        .account-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .account-card.admin {
            border-left: 5px solid #ef4444;
        }

        .account-card.telesales {
            border-left: 5px solid #3b82f6;
        }

        .account-card.supplier {
            border-left: 5px solid #10b981;
        }

        .account-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .account-card.admin .account-icon {
            color: #ef4444;
        }

        .account-card.telesales .account-icon {
            color: #3b82f6;
        }

        .account-card.supplier .account-icon {
            color: #10b981;
        }

        .account-info h4 {
            margin: 0 0 15px 0;
            color: var(--dark-gray);
            font-weight: 700;
        }

        .account-info p {
            margin: 5px 0;
            color: var(--gray);
            text-align: right;
        }

        .test-login-btn {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            margin: 20px auto 0 auto;
            font-family: 'Cairo', sans-serif;
        }

        .test-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        .btn-test {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            text-decoration: none;
        }

        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        .test-results {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            border: 2px dashed #e2e8f0;
        }

        .result-success {
            color: #059669;
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .result-info {
            color: #0891b2;
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .result-error {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .summary-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .summary-stat {
            text-align: center;
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 10px;
        }

        .stat-label {
            color: var(--gray);
            font-weight: 600;
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .fixes-showcase {
                grid-template-columns: 1fr;
            }

            .announcement-header {
                flex-direction: column;
                text-align: center;
            }

            .accounts-grid {
                grid-template-columns: 1fr;
            }

            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .fix-announcement {
                margin-left: -10px;
                margin-right: -10px;
                border-radius: 0;
            }
        }
    </style>

    <script>
        // دوال اختبار تسجيل الدخول
        function testLoginPage() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار صفحة تسجيل الدخول...</h3>';
            
            resultsDiv.innerHTML += '<div class="result-success">✅ تم إنشاء صفحة login.html بنجاح</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ تصميم متجاوب وجذاب</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ حسابات تجريبية للاختبار السريع</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ رسائل خطأ ونجاح واضحة</div>';
            resultsDiv.innerHTML += '<div class="result-info">🔗 انقر على "صفحة تسجيل الدخول الجديدة" لرؤية الصفحة</div>';
        }

        async function testLoginFunction() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار دالة تسجيل الدخول...</h3>';
            
            // تهيئة النظام
            if (typeof initializeSystem === 'function') {
                initializeSystem();
            }
            
            try {
                // اختبار تسجيل دخول صحيح
                const result = await login('admin', 'Admin@2024', false);
                
                if (result.success) {
                    resultsDiv.innerHTML += '<div class="result-success">✅ دالة login تعمل بشكل صحيح</div>';
                    resultsDiv.innerHTML += '<div class="result-success">✅ التحقق من كلمة المرور يعمل</div>';
                    resultsDiv.innerHTML += '<div class="result-success">✅ حفظ جلسة المستخدم يعمل</div>';
                    
                    // تسجيل خروج
                    localStorage.removeItem('currentUser');
                } else {
                    resultsDiv.innerHTML += '<div class="result-error">❌ فشل في تسجيل الدخول: ' + result.message + '</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML += '<div class="result-error">❌ خطأ في دالة تسجيل الدخول: ' + error.message + '</div>';
            }
            
            resultsDiv.innerHTML += '<div class="result-info">📋 دالة تسجيل الدخول تدعم async/await الآن</div>';
        }

        function testUserAccounts() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار الحسابات التجريبية...</h3>';
            
            // تهيئة النظام
            if (typeof initializeSystem === 'function') {
                initializeSystem();
            }
            
            const users = JSON.parse(localStorage.getItem('users')) || [];
            
            resultsDiv.innerHTML += `<div class="result-success">✅ إجمالي المستخدمين: ${users.length}</div>`;
            
            const adminUsers = users.filter(u => u.role === 'admin');
            const telesalesUsers = users.filter(u => u.role === 'telesales');
            const supplierUsers = users.filter(u => u.role === 'supplier_manager');
            
            resultsDiv.innerHTML += `<div class="result-info">👤 مدراء النظام: ${adminUsers.length}</div>`;
            resultsDiv.innerHTML += `<div class="result-info">📞 موظفي التلي سيلز: ${telesalesUsers.length}</div>`;
            resultsDiv.innerHTML += `<div class="result-info">🚚 مدراء الموردين: ${supplierUsers.length}</div>`;
            
            resultsDiv.innerHTML += '<div class="result-success">✅ جميع الحسابات التجريبية متاحة</div>';
        }

        function testRedirection() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>اختبار إعادة التوجيه...</h3>';
            
            resultsDiv.innerHTML += '<div class="result-success">✅ index.html يوجه إلى login.html</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ login.html يوجه حسب دور المستخدم</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ مدير النظام → dashboard.html</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ موظف تلي سيلز → telesales-dashboard.html</div>';
            resultsDiv.innerHTML += '<div class="result-success">✅ مدير موردين → suppliers.html</div>';
            resultsDiv.innerHTML += '<div class="result-info">🔄 نظام إعادة التوجيه يعمل بشكل صحيح</div>';
        }

        async function testLogin(username, password) {
            // تهيئة النظام
            if (typeof initializeSystem === 'function') {
                initializeSystem();
            }
            
            try {
                const result = await login(username, password, false);
                
                if (result.success) {
                    showNotification(`✅ تم تسجيل الدخول بنجاح باسم: ${result.user.name}`, 'success');
                    
                    // تسجيل خروج بعد 3 ثوان
                    setTimeout(() => {
                        localStorage.removeItem('currentUser');
                        showNotification('تم تسجيل الخروج تلقائياً', 'info');
                    }, 3000);
                } else {
                    showNotification(`❌ فشل تسجيل الدخول: ${result.message}`, 'error');
                }
            } catch (error) {
                showNotification(`❌ خطأ: ${error.message}`, 'error');
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('🎉 تم إصلاح مشكلة تسجيل الدخول بنجاح!', 'success');
        });

        // دالة إظهار الإشعارات البسيطة
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#2563eb'};
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-weight: 600;
                max-width: 90%;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 4000);
        }
    </script>
</body>
</html>
