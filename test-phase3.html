<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المرحلة الثالثة - نظام عروض الأسعار المتكامل</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div style="padding: 20px; max-width: 1200px; margin: 0 auto;">
        <h1 style="text-align: center; color: var(--primary-blue); margin-bottom: 30px;">
            <i class="fas fa-file-invoice-dollar"></i>
            اختبار المرحلة الثالثة - نظام عروض الأسعار المتكامل
        </h1>
        
        <!-- قسم اختبار إنشاء عروض الأسعار -->
        <div class="test-section">
            <h2><i class="fas fa-plus-circle"></i> اختبار إنشاء عروض الأسعار</h2>
            <div class="test-buttons">
                <button class="btn-primary" onclick="testCreateQuotation()">
                    <i class="fas fa-file-plus"></i>
                    إنشاء عرض سعر جديد
                </button>
                <button class="btn-secondary" onclick="testViewQuotation()">
                    <i class="fas fa-eye"></i>
                    عرض تفاصيل عرض سعر
                </button>
                <button class="btn-export" onclick="testEditQuotation()">
                    <i class="fas fa-edit"></i>
                    تعديل عرض سعر
                </button>
                <button class="btn-export" onclick="testPrintQuotation()">
                    <i class="fas fa-print"></i>
                    طباعة عرض سعر
                </button>
            </div>
            <div class="test-description">
                <p><strong>الميزات الجديدة لإنشاء عروض الأسعار:</strong></p>
                <ul>
                    <li>نموذج شامل لإنشاء عروض الأسعار مع جميع التفاصيل المطلوبة</li>
                    <li>اختيار العميل من قائمة العملاء المسجلين</li>
                    <li>إدارة بنود العرض (إضافة، تعديل، حذف البنود)</li>
                    <li>حساب تلقائي للمجاميع والضرائب والخصومات</li>
                    <li>إضافة الملاحظات والشروط والأحكام</li>
                    <li>حفظ العرض كمسودة أو إرساله مباشرة</li>
                    <li>توليد رقم عرض سعر تلقائي ومتسلسل</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار إدارة البنود -->
        <div class="test-section">
            <h2><i class="fas fa-list"></i> اختبار إدارة بنود العرض</h2>
            <div class="test-buttons">
                <button class="btn-primary" onclick="testAddItems()">
                    <i class="fas fa-plus"></i>
                    إضافة بنود متعددة
                </button>
                <button class="btn-secondary" onclick="testCalculations()">
                    <i class="fas fa-calculator"></i>
                    اختبار الحسابات
                </button>
                <button class="btn-export" onclick="testItemValidation()">
                    <i class="fas fa-check-circle"></i>
                    التحقق من البنود
                </button>
            </div>
            <div class="test-description">
                <p><strong>ميزات إدارة البنود:</strong></p>
                <ul>
                    <li>إضافة بنود غير محدودة للعرض</li>
                    <li>وصف تفصيلي لكل بند مع الكمية وسعر الوحدة</li>
                    <li>حساب تلقائي لمجموع كل بند</li>
                    <li>حساب المجموع الفرعي لجميع البنود</li>
                    <li>تطبيق الخصومات والضرائب</li>
                    <li>حساب المبلغ الإجمالي النهائي</li>
                    <li>حذف البنود غير المرغوب فيها</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار التصدير والطباعة -->
        <div class="test-section">
            <h2><i class="fas fa-download"></i> اختبار التصدير والطباعة</h2>
            <div class="test-buttons">
                <button class="btn-export excel-btn" onclick="testExportExcel()">
                    <i class="fas fa-file-excel"></i>
                    تصدير Excel
                </button>
                <button class="btn-export pdf-btn" onclick="testExportPDF()">
                    <i class="fas fa-file-pdf"></i>
                    تصدير PDF
                </button>
                <button class="btn-export" onclick="testExportCSV()">
                    <i class="fas fa-file-csv"></i>
                    تصدير CSV
                </button>
                <button class="btn-export" onclick="testEmailQuotation()">
                    <i class="fas fa-envelope"></i>
                    إرسال بالبريد
                </button>
            </div>
            <div class="test-description">
                <p><strong>ميزات التصدير والمشاركة:</strong></p>
                <ul>
                    <li>تصدير عروض الأسعار إلى Excel مع تنسيق احترافي</li>
                    <li>تصدير تقارير PDF مع جداول منسقة</li>
                    <li>تصدير بيانات CSV للتحليل</li>
                    <li>طباعة عروض الأسعار بتصميم احترافي</li>
                    <li>إرسال عروض الأسعار بالبريد الإلكتروني</li>
                    <li>معاينة العرض قبل الطباعة أو الإرسال</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار الإحصائيات والتقارير -->
        <div class="test-section">
            <h2><i class="fas fa-chart-bar"></i> اختبار الإحصائيات والتقارير</h2>
            <div class="test-buttons">
                <button class="btn-secondary" onclick="testQuotationStats()">
                    <i class="fas fa-chart-pie"></i>
                    إحصائيات العروض
                </button>
                <button class="btn-export" onclick="testStatusTracking()">
                    <i class="fas fa-tasks"></i>
                    تتبع الحالات
                </button>
                <button class="btn-primary" onclick="testFinancialReports()">
                    <i class="fas fa-dollar-sign"></i>
                    التقارير المالية
                </button>
            </div>
            <div class="test-description">
                <p><strong>ميزات الإحصائيات والتقارير:</strong></p>
                <ul>
                    <li>إحصائيات شاملة لعروض الأسعار (إجمالي، معلقة، مقبولة)</li>
                    <li>تتبع حالات العروض (مسودة، مرسل، مقبول، مرفوض، منتهي)</li>
                    <li>حساب القيمة الإجمالية لجميع العروض</li>
                    <li>تقارير مالية مفصلة</li>
                    <li>فلترة العروض حسب الحالة والعميل</li>
                    <li>البحث في محتوى العروض والبنود</li>
                </ul>
            </div>
        </div>

        <!-- قسم معاينة نموذج عرض السعر -->
        <div class="test-section">
            <h2><i class="fas fa-eye"></i> معاينة نموذج عرض السعر</h2>
            
            <!-- نموذج تجريبي لعرض السعر -->
            <div class="form-preview quotation-preview">
                <h4>نموذج عرض السعر</h4>
                
                <!-- معلومات العرض -->
                <div class="form-section">
                    <h4><i class="fas fa-info-circle"></i> معلومات العرض</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>رقم العرض</label>
                            <input type="text" value="Q-2024-001" readonly>
                        </div>
                        <div class="form-group">
                            <label>العميل</label>
                            <select disabled>
                                <option>شركة التقنية المتقدمة</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- بند تجريبي -->
                <div class="form-section">
                    <h4><i class="fas fa-list"></i> بنود العرض</h4>
                    <div class="item-row">
                        <div class="item-number">1</div>
                        <div class="item-fields">
                            <div class="form-group">
                                <label>وصف البند</label>
                                <input type="text" value="تطوير موقع إلكتروني متجاوب" readonly>
                            </div>
                            <div class="form-group">
                                <label>الكمية</label>
                                <input type="number" value="1" readonly>
                            </div>
                            <div class="form-group">
                                <label>سعر الوحدة</label>
                                <input type="number" value="15000" readonly>
                            </div>
                            <div class="form-group">
                                <label>المجموع</label>
                                <input type="number" value="15000" readonly>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الحسابات المالية -->
                <div class="form-section">
                    <h4><i class="fas fa-calculator"></i> الحسابات المالية</h4>
                    <div class="financial-calculations">
                        <div class="form-row">
                            <div class="form-group">
                                <label>المجموع الفرعي</label>
                                <input type="number" value="15000" readonly>
                            </div>
                            <div class="form-group">
                                <label>الخصم</label>
                                <input type="number" value="0" readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>نسبة الضريبة (%)</label>
                                <input type="number" value="15" readonly>
                            </div>
                            <div class="form-group">
                                <label>مبلغ الضريبة</label>
                                <input type="number" value="2250" readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group total-amount">
                                <label>المبلغ الإجمالي</label>
                                <input type="number" value="17250" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط التنقل -->
        <div class="test-section">
            <h2><i class="fas fa-link"></i> روابط التنقل</h2>
            <div class="test-buttons">
                <a href="quotations.html" class="btn-primary">
                    <i class="fas fa-file-invoice-dollar"></i>
                    صفحة عروض الأسعار
                </a>
                <a href="test-phase2.html" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    المرحلة الثانية
                </a>
                <a href="test-phase1.html" class="btn-export">
                    <i class="fas fa-arrow-left"></i>
                    المرحلة الأولى
                </a>
                <a href="index.html" class="btn-export">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/quotations.js"></script>

    <script>
        // إعداد مستخدم تجريبي
        if (!localStorage.getItem('currentUser')) {
            const testUser = {
                username: 'test_admin',
                name: 'أحمد محمد المدير',
                role: 'admin',
                email: '<EMAIL>',
                phone: '0501234567',
                jobTitle: 'مدير النظام',
                loginTime: new Date().toISOString(),
                joinDate: '2024-01-01',
                avatar: null
            };
            localStorage.setItem('currentUser', JSON.stringify(testUser));
        }

        // إعداد عملاء تجريبيين
        if (!localStorage.getItem('customers')) {
            const testCustomers = [
                {
                    id: 'customer_1',
                    name: 'شركة التقنية المتقدمة',
                    email: '<EMAIL>',
                    phone: '0112345678',
                    city: 'الرياض',
                    status: 'active'
                },
                {
                    id: 'customer_2',
                    name: 'مؤسسة الأعمال الذكية',
                    email: '<EMAIL>',
                    phone: '0112345679',
                    city: 'جدة',
                    status: 'active'
                }
            ];
            localStorage.setItem('customers', JSON.stringify(testCustomers));
        }

        // اختبار إنشاء عرض سعر
        function testCreateQuotation() {
            if (typeof showAddQuotationModal === 'function') {
                showAddQuotationModal();
            } else {
                alert('يجب تحميل quotations.js أولاً');
            }
        }

        // اختبار عرض تفاصيل عرض سعر
        function testViewQuotation() {
            if (typeof viewQuotation === 'function') {
                // استخدام أول عرض سعر من البيانات التجريبية
                const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
                if (quotations.length > 0) {
                    viewQuotation(quotations[0].id);
                } else {
                    alert('لا توجد عروض أسعار للعرض. قم بإنشاء عرض سعر أولاً.');
                }
            } else {
                alert('يجب تحميل quotations.js أولاً');
            }
        }

        // اختبار تعديل عرض سعر
        function testEditQuotation() {
            if (typeof editQuotation === 'function') {
                const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
                if (quotations.length > 0) {
                    editQuotation(quotations[0].id);
                } else {
                    alert('لا توجد عروض أسعار للتعديل. قم بإنشاء عرض سعر أولاً.');
                }
            } else {
                alert('يجب تحميل quotations.js أولاً');
            }
        }

        // اختبار طباعة عرض سعر
        function testPrintQuotation() {
            if (typeof printQuotation === 'function') {
                const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
                if (quotations.length > 0) {
                    printQuotation(quotations[0].id);
                } else {
                    alert('لا توجد عروض أسعار للطباعة. قم بإنشاء عرض سعر أولاً.');
                }
            } else {
                alert('يجب تحميل quotations.js أولاً');
            }
        }

        // اختبار إضافة بنود
        function testAddItems() {
            showNotification('اختبار إضافة البنود - يمكن إضافة بنود غير محدودة لكل عرض سعر', 'info');
            setTimeout(() => showNotification('كل بند يحتوي على وصف، كمية، سعر وحدة، ومجموع تلقائي', 'success'), 1500);
        }

        // اختبار الحسابات
        function testCalculations() {
            showNotification('اختبار الحسابات - جميع الحسابات تتم تلقائياً', 'info');
            setTimeout(() => showNotification('المجموع الفرعي + الضريبة - الخصم = المبلغ الإجمالي', 'success'), 1500);
        }

        // اختبار التحقق من البنود
        function testItemValidation() {
            showNotification('التحقق من البنود - جميع الحقول المطلوبة محمية', 'info');
            setTimeout(() => showNotification('لا يمكن حفظ عرض سعر بدون بنود صحيحة', 'warning'), 1500);
        }

        // اختبار تصدير Excel
        function testExportExcel() {
            if (typeof exportQuotationsExcel === 'function') {
                exportQuotationsExcel();
            } else {
                showNotification('تصدير Excel متاح في صفحة عروض الأسعار', 'info');
            }
        }

        // اختبار تصدير PDF
        function testExportPDF() {
            if (typeof exportQuotationsPDF === 'function') {
                exportQuotationsPDF();
            } else {
                showNotification('تصدير PDF متاح في صفحة عروض الأسعار', 'info');
            }
        }

        // اختبار تصدير CSV
        function testExportCSV() {
            if (typeof exportQuotations === 'function') {
                exportQuotations('csv');
            } else {
                showNotification('تصدير CSV متاح في صفحة عروض الأسعار', 'info');
            }
        }

        // اختبار إرسال بالبريد
        function testEmailQuotation() {
            if (typeof emailQuotation === 'function') {
                const quotations = JSON.parse(localStorage.getItem('quotations')) || [];
                if (quotations.length > 0) {
                    emailQuotation(quotations[0].id);
                } else {
                    showNotification('لا توجد عروض أسعار للإرسال', 'warning');
                }
            } else {
                showNotification('إرسال البريد متاح في صفحة عروض الأسعار', 'info');
            }
        }

        // اختبار إحصائيات العروض
        function testQuotationStats() {
            showNotification('إحصائيات العروض - عرض شامل لجميع الإحصائيات', 'info');
            setTimeout(() => showNotification('إجمالي العروض، المعلقة، المقبولة، والقيمة الإجمالية', 'success'), 1500);
        }

        // اختبار تتبع الحالات
        function testStatusTracking() {
            showNotification('تتبع الحالات - 5 حالات مختلفة للعروض', 'info');
            setTimeout(() => showNotification('مسودة، مرسل، مقبول، مرفوض، منتهي الصلاحية', 'success'), 1500);
        }

        // اختبار التقارير المالية
        function testFinancialReports() {
            showNotification('التقارير المالية - حسابات دقيقة ومفصلة', 'info');
            setTimeout(() => showNotification('تتبع الإيرادات المتوقعة والمحققة', 'success'), 1500);
        }
    </script>

    <style>
        .test-section {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 8px var(--shadow);
            border-left: 4px solid var(--primary-blue);
        }

        .test-section h2 {
            color: var(--primary-blue);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .test-description {
            background: var(--light-gray);
            padding: 20px;
            border-radius: var(--border-radius);
            border-right: 4px solid var(--secondary-blue);
        }

        .test-description ul {
            margin: 10px 0;
            padding-right: 20px;
        }

        .test-description li {
            margin-bottom: 8px;
            color: var(--dark-gray);
        }

        .quotation-preview {
            background: var(--white);
            border: 2px solid #e2e8f0;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 20px;
        }

        .quotation-preview h4 {
            color: var(--primary-blue);
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .test-buttons {
                flex-direction: column;
            }
            
            .test-buttons .btn-primary,
            .test-buttons .btn-secondary,
            .test-buttons .btn-export {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</body>
</html>
