// ===== إدارة العملاء =====

let customers = [];
let filteredCustomers = [];
let currentPage = 1;
let itemsPerPage = 10;
let sortBy = 'name';
let sortOrder = 'asc';

// تهيئة صفحة العملاء
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_customers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // تهيئة البيانات
    initializeCustomers();
    
    // ربط الأحداث
    bindEvents();
    
    // تحديث العرض
    updateCustomersDisplay();
    
    // تسجيل النشاط
    logActivity('customers_view', 'عرض صفحة إدارة العملاء');
});

// تهيئة بيانات العملاء
function initializeCustomers() {
    // تحميل العملاء من التخزين المحلي
    customers = JSON.parse(localStorage.getItem('customers')) || [];
    
    // إضافة بيانات تجريبية إذا لم توجد بيانات
    if (customers.length === 0) {
        customers = [
            {
                id: generateId(),
                name: 'أحمد محمد علي',
                phone: '0501234567',
                email: '<EMAIL>',
                city: 'الرياض',
                activityType: 'تجارة إلكترونية',
                lastContact: '2024-01-15',
                notes: 'عميل مهتم بالخدمات الرقمية',
                status: 'active',
                createdAt: '2024-01-10',
                createdBy: 'admin'
            },
            {
                id: generateId(),
                name: 'فاطمة أحمد السالم',
                phone: '0507654321',
                email: '<EMAIL>',
                city: 'جدة',
                activityType: 'مطاعم',
                lastContact: '2024-01-14',
                notes: 'تحتاج إلى نظام إدارة المطاعم',
                status: 'potential',
                createdAt: '2024-01-08',
                createdBy: 'supervisor'
            },
            {
                id: generateId(),
                name: 'محمد عبدالله الخالد',
                phone: '0551234567',
                email: '<EMAIL>',
                city: 'الدمام',
                activityType: 'خدمات طبية',
                lastContact: '2024-01-12',
                notes: 'مهتم بأنظمة إدارة العيادات',
                status: 'active',
                createdAt: '2024-01-05',
                createdBy: 'telesales'
            }
        ];
        
        saveCustomers();
    }
    
    filteredCustomers = [...customers];
}

// حفظ العملاء في التخزين المحلي
function saveCustomers() {
    localStorage.setItem('customers', JSON.stringify(customers));
}

// ربط الأحداث
function bindEvents() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
    
    // الفلاتر
    const filters = ['cityFilter', 'activityFilter', 'statusFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', handleFilter);
        }
    });
    
    // الترتيب
    const sortBy = document.getElementById('sortBy');
    if (sortBy) {
        sortBy.addEventListener('change', handleSort);
    }
}

// معالج البحث
function handleSearch(event) {
    const searchTerm = event.target.value.trim();
    
    if (searchTerm === '') {
        filteredCustomers = [...customers];
    } else {
        filteredCustomers = searchData(customers, searchTerm, [
            'name', 'phone', 'email', 'city', 'activityType', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateCustomersDisplay();
}

// معالج الفلاتر
function handleFilter() {
    const cityFilter = document.getElementById('cityFilter').value;
    const activityFilter = document.getElementById('activityFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredCustomers = filterData(customers, {
        city: cityFilter,
        activityType: activityFilter,
        status: statusFilter
    });
    
    // تطبيق البحث إذا كان موجوداً
    const searchTerm = document.getElementById('searchInput').value.trim();
    if (searchTerm) {
        filteredCustomers = searchData(filteredCustomers, searchTerm, [
            'name', 'phone', 'email', 'city', 'activityType', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateCustomersDisplay();
}

// معالج الترتيب
function handleSort() {
    sortBy = document.getElementById('sortBy').value;
    filteredCustomers = sortData(filteredCustomers, sortBy, sortOrder);
    updateCustomersDisplay();
}

// تبديل ترتيب الفرز
function toggleSortOrder() {
    sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    
    const sortOrderBtn = document.getElementById('sortOrderBtn');
    if (sortOrderBtn) {
        const icon = sortOrderBtn.querySelector('i');
        icon.className = sortOrder === 'asc' ? 'fas fa-sort-amount-down' : 'fas fa-sort-amount-up';
    }
    
    filteredCustomers = sortData(filteredCustomers, sortBy, sortOrder);
    updateCustomersDisplay();
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('cityFilter').value = 'all';
    document.getElementById('activityFilter').value = 'all';
    document.getElementById('statusFilter').value = 'all';
    
    filteredCustomers = [...customers];
    currentPage = 1;
    updateCustomersDisplay();
}

// تحديث عرض العملاء
function updateCustomersDisplay() {
    updateCustomersStats();
    updateFiltersOptions();
    updateCustomersTable();
    updatePagination();
}

// تحديث إحصائيات العملاء
function updateCustomersStats() {
    const totalCustomers = customers.length;
    const activeCustomers = customers.filter(c => c.status === 'active').length;
    const potentialCustomers = customers.filter(c => c.status === 'potential').length;
    
    // حساب التواصل اليوم
    const today = new Date().toDateString();
    const todayContacts = customers.filter(c => 
        new Date(c.lastContact).toDateString() === today
    ).length;
    
    // تحديث العدادات
    updateCounter('totalCustomersCount', totalCustomers);
    updateCounter('activeCustomersCount', activeCustomers);
    updateCounter('potentialCustomersCount', potentialCustomers);
    updateCounter('todayContactsCount', todayContacts);
    
    // تحديث شارة التنقل
    updateNavBadge('customersBadge', totalCustomers);
}

// تحديث خيارات الفلاتر
function updateFiltersOptions() {
    // تحديث فلتر المدن
    const cities = [...new Set(customers.map(c => c.city))].sort();
    updateFilterOptions('cityFilter', cities);
    
    // تحديث فلتر الأنشطة
    const activities = [...new Set(customers.map(c => c.activityType))].sort();
    updateFilterOptions('activityFilter', activities);
}

// تحديث خيارات فلتر معين
function updateFilterOptions(filterId, options) {
    const filter = document.getElementById(filterId);
    if (!filter) return;
    
    const currentValue = filter.value;
    const firstOption = filter.querySelector('option[value="all"]');
    
    // مسح الخيارات الحالية (عدا الخيار الأول)
    filter.innerHTML = '';
    filter.appendChild(firstOption);
    
    // إضافة الخيارات الجديدة
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option;
        optionElement.textContent = option;
        filter.appendChild(optionElement);
    });
    
    // استعادة القيمة المحددة
    filter.value = currentValue;
}

// تحديث جدول العملاء
function updateCustomersTable() {
    const tbody = document.getElementById('customersTableBody');
    if (!tbody) return;
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageCustomers = filteredCustomers.slice(startIndex, endIndex);
    
    if (pageCustomers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 40px; color: var(--gray);">
                    <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p data-ar="لا توجد عملاء" data-en="No customers found">لا توجد عملاء</p>
                </td>
            </tr>
        `;
        updateLanguage();
        return;
    }
    
    tbody.innerHTML = pageCustomers.map(customer => `
        <tr>
            <td><strong>${customer.name}</strong></td>
            <td>${customer.phone}</td>
            <td>${customer.email}</td>
            <td>${customer.city}</td>
            <td>${customer.activityType}</td>
            <td>${formatDate(customer.lastContact)}</td>
            <td>
                <span class="status-badge status-${customer.status}">
                    ${getStatusDisplayName(customer.status)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewCustomer('${customer.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${hasPermission('edit_customers') || hasPermission('all') ? `
                        <button class="action-btn edit" onclick="editCustomer('${customer.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : ''}
                    ${hasPermission('all') ? `
                        <button class="action-btn delete" onclick="deleteCustomer('${customer.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// الحصول على اسم الحالة للعرض
function getStatusDisplayName(status) {
    const statuses = {
        'active': currentLanguage === 'ar' ? 'نشط' : 'Active',
        'inactive': currentLanguage === 'ar' ? 'غير نشط' : 'Inactive',
        'potential': currentLanguage === 'ar' ? 'محتمل' : 'Potential'
    };
    
    return statuses[status] || status;
}

// تحديث ترقيم الصفحات
function updatePagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;
    
    const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = `
        <button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // إضافة أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-dots">...</span>';
        }
    }
    
    paginationHTML += `
        <button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="pagination-info">
            ${currentPage} من ${totalPages} (${filteredCustomers.length} عميل)
        </div>
    `;
    
    pagination.innerHTML = paginationHTML;
}

// تغيير الصفحة
function changePage(page) {
    const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);
    
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    updateCustomersTable();
    updatePagination();
    
    // التمرير إلى أعلى الجدول
    document.querySelector('.table-section').scrollIntoView({ behavior: 'smooth' });
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تصدير العملاء
function exportCustomers(format) {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'email', label: 'البريد الإلكتروني' },
        { key: 'city', label: 'المدينة' },
        { key: 'activityType', label: 'نوع النشاط' },
        { key: 'lastContact', label: 'آخر تواصل' },
        { key: 'status', label: 'الحالة' },
        { key: 'notes', label: 'الملاحظات' }
    ];
    
    if (format === 'csv') {
        exportToCSV(filteredCustomers, 'customers', headers);
    } else if (format === 'json') {
        exportToJSON(filteredCustomers, 'customers');
    }
    
    logActivity('export_customers', `تصدير بيانات العملاء بصيغة ${format.toUpperCase()}`);
}

// طباعة العملاء
function printCustomers() {
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'email', label: 'البريد الإلكتروني' },
        { key: 'city', label: 'المدينة' },
        { key: 'activityType', label: 'نوع النشاط' },
        { key: 'lastContact', label: 'آخر تواصل' },
        { key: 'status', label: 'الحالة' }
    ];
    
    printData('قائمة العملاء', filteredCustomers, headers);
    logActivity('print_customers', 'طباعة قائمة العملاء');
}

// عرض نافذة إضافة عميل جديد
function showAddCustomerModal() {
    if (!hasPermission('add_customers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لإضافة عملاء جدد');
        return;
    }
    
    // سيتم تنفيذها في الجزء التالي
    alert('نافذة إضافة عميل جديد قيد التطوير');
}

// عرض تفاصيل العميل
function viewCustomer(customerId) {
    // سيتم تنفيذها في الجزء التالي
    alert('عرض تفاصيل العميل قيد التطوير');
}

// تعديل العميل
function editCustomer(customerId) {
    if (!hasPermission('edit_customers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتعديل العملاء');
        return;
    }
    
    // سيتم تنفيذها في الجزء التالي
    alert('تعديل العميل قيد التطوير');
}

// حذف العميل
function deleteCustomer(customerId) {
    if (!hasPermission('all')) {
        alert('ليس لديك صلاحية لحذف العملاء');
        return;
    }
    
    // سيتم تنفيذها في الجزء التالي
    alert('حذف العميل قيد التطوير');
}
