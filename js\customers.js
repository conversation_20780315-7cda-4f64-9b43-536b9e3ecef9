// ===== إدارة العملاء =====

let customers = [];
let filteredCustomers = [];
let currentPage = 1;
let itemsPerPage = 10;
let sortBy = 'name';
let sortOrder = 'asc';

// تهيئة صفحة العملاء
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول والصلاحيات
    if (!requireAuth()) return;
    
    if (!hasPermission('view_customers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لعرض هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // تهيئة البيانات
    initializeCustomers();
    
    // ربط الأحداث
    bindEvents();
    
    // تحديث العرض
    updateCustomersDisplay();
    
    // تسجيل النشاط
    logActivity('customers_view', 'عرض صفحة إدارة العملاء');
});

// تهيئة بيانات العملاء
function initializeCustomers() {
    // تحميل العملاء من التخزين المحلي
    customers = JSON.parse(localStorage.getItem('customers')) || [];
    
    // إضافة بيانات تجريبية إذا لم توجد بيانات
    if (customers.length === 0) {
        customers = [
            {
                id: generateId(),
                name: 'أحمد محمد علي',
                phone: '0501234567',
                email: '<EMAIL>',
                city: 'الرياض',
                activityType: 'تجارة إلكترونية',
                lastContact: '2024-01-15',
                notes: 'عميل مهتم بالخدمات الرقمية',
                status: 'active',
                createdAt: '2024-01-10',
                createdBy: 'admin'
            },
            {
                id: generateId(),
                name: 'فاطمة أحمد السالم',
                phone: '0507654321',
                email: '<EMAIL>',
                city: 'جدة',
                activityType: 'مطاعم',
                lastContact: '2024-01-14',
                notes: 'تحتاج إلى نظام إدارة المطاعم',
                status: 'potential',
                createdAt: '2024-01-08',
                createdBy: 'supervisor'
            },
            {
                id: generateId(),
                name: 'محمد عبدالله الخالد',
                phone: '0551234567',
                email: '<EMAIL>',
                city: 'الدمام',
                activityType: 'خدمات طبية',
                lastContact: '2024-01-12',
                notes: 'مهتم بأنظمة إدارة العيادات',
                status: 'active',
                createdAt: '2024-01-05',
                createdBy: 'telesales'
            }
        ];
        
        saveCustomers();
    }
    
    filteredCustomers = [...customers];
}

// حفظ العملاء في التخزين المحلي
function saveCustomers() {
    localStorage.setItem('customers', JSON.stringify(customers));
}

// ربط الأحداث
function bindEvents() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
    
    // الفلاتر
    const filters = ['cityFilter', 'activityFilter', 'statusFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', handleFilter);
        }
    });
    
    // الترتيب
    const sortBy = document.getElementById('sortBy');
    if (sortBy) {
        sortBy.addEventListener('change', handleSort);
    }
}

// معالج البحث
function handleSearch(event) {
    const searchTerm = event.target.value.trim();
    
    if (searchTerm === '') {
        filteredCustomers = [...customers];
    } else {
        filteredCustomers = searchData(customers, searchTerm, [
            'name', 'phone', 'email', 'city', 'activityType', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateCustomersDisplay();
}

// معالج الفلاتر
function handleFilter() {
    const cityFilter = document.getElementById('cityFilter').value;
    const activityFilter = document.getElementById('activityFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredCustomers = filterData(customers, {
        city: cityFilter,
        activityType: activityFilter,
        status: statusFilter
    });
    
    // تطبيق البحث إذا كان موجوداً
    const searchTerm = document.getElementById('searchInput').value.trim();
    if (searchTerm) {
        filteredCustomers = searchData(filteredCustomers, searchTerm, [
            'name', 'phone', 'email', 'city', 'activityType', 'notes'
        ]);
    }
    
    currentPage = 1;
    updateCustomersDisplay();
}

// معالج الترتيب
function handleSort() {
    sortBy = document.getElementById('sortBy').value;
    filteredCustomers = sortData(filteredCustomers, sortBy, sortOrder);
    updateCustomersDisplay();
}

// تبديل ترتيب الفرز
function toggleSortOrder() {
    sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    
    const sortOrderBtn = document.getElementById('sortOrderBtn');
    if (sortOrderBtn) {
        const icon = sortOrderBtn.querySelector('i');
        icon.className = sortOrder === 'asc' ? 'fas fa-sort-amount-down' : 'fas fa-sort-amount-up';
    }
    
    filteredCustomers = sortData(filteredCustomers, sortBy, sortOrder);
    updateCustomersDisplay();
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('cityFilter').value = 'all';
    document.getElementById('activityFilter').value = 'all';
    document.getElementById('statusFilter').value = 'all';
    
    filteredCustomers = [...customers];
    currentPage = 1;
    updateCustomersDisplay();
}

// تحديث عرض العملاء
function updateCustomersDisplay() {
    updateCustomersStats();
    updateFiltersOptions();
    updateCustomersTable();
    updatePagination();
}

// تحديث إحصائيات العملاء
function updateCustomersStats() {
    const totalCustomers = customers.length;
    const activeCustomers = customers.filter(c => c.status === 'active').length;
    const potentialCustomers = customers.filter(c => c.status === 'potential').length;
    
    // حساب التواصل اليوم
    const today = new Date().toDateString();
    const todayContacts = customers.filter(c => 
        new Date(c.lastContact).toDateString() === today
    ).length;
    
    // تحديث العدادات
    updateCounter('totalCustomersCount', totalCustomers);
    updateCounter('activeCustomersCount', activeCustomers);
    updateCounter('potentialCustomersCount', potentialCustomers);
    updateCounter('todayContactsCount', todayContacts);
    
    // تحديث شارة التنقل
    updateNavBadge('customersBadge', totalCustomers);
}

// تحديث خيارات الفلاتر
function updateFiltersOptions() {
    // تحديث فلتر المدن
    const cities = [...new Set(customers.map(c => c.city))].sort();
    updateFilterOptions('cityFilter', cities);
    
    // تحديث فلتر الأنشطة
    const activities = [...new Set(customers.map(c => c.activityType))].sort();
    updateFilterOptions('activityFilter', activities);
}

// تحديث خيارات فلتر معين
function updateFilterOptions(filterId, options) {
    const filter = document.getElementById(filterId);
    if (!filter) return;
    
    const currentValue = filter.value;
    const firstOption = filter.querySelector('option[value="all"]');
    
    // مسح الخيارات الحالية (عدا الخيار الأول)
    filter.innerHTML = '';
    filter.appendChild(firstOption);
    
    // إضافة الخيارات الجديدة
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option;
        optionElement.textContent = option;
        filter.appendChild(optionElement);
    });
    
    // استعادة القيمة المحددة
    filter.value = currentValue;
}

// تحديث جدول العملاء
function updateCustomersTable() {
    const tbody = document.getElementById('customersTableBody');
    if (!tbody) return;
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageCustomers = filteredCustomers.slice(startIndex, endIndex);
    
    if (pageCustomers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 40px; color: var(--gray);">
                    <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p data-ar="لا توجد عملاء" data-en="No customers found">لا توجد عملاء</p>
                </td>
            </tr>
        `;
        updateLanguage();
        return;
    }
    
    tbody.innerHTML = pageCustomers.map(customer => `
        <tr>
            <td><strong>${customer.name}</strong></td>
            <td>${customer.phone}</td>
            <td>${customer.email}</td>
            <td>${customer.city}</td>
            <td>${customer.activityType}</td>
            <td>${formatDate(customer.lastContact)}</td>
            <td>
                <span class="status-badge status-${customer.status}">
                    ${getStatusDisplayName(customer.status)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewCustomer('${customer.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${hasPermission('edit_customers') || hasPermission('all') ? `
                        <button class="action-btn edit" onclick="editCustomer('${customer.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : ''}
                    ${hasPermission('all') ? `
                        <button class="action-btn delete" onclick="deleteCustomer('${customer.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// الحصول على اسم الحالة للعرض
function getStatusDisplayName(status) {
    const statuses = {
        'active': currentLanguage === 'ar' ? 'نشط' : 'Active',
        'inactive': currentLanguage === 'ar' ? 'غير نشط' : 'Inactive',
        'potential': currentLanguage === 'ar' ? 'محتمل' : 'Potential'
    };
    
    return statuses[status] || status;
}

// تحديث ترقيم الصفحات
function updatePagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;
    
    const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = `
        <button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // إضافة أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-dots">...</span>';
        }
    }
    
    paginationHTML += `
        <button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="pagination-info">
            ${currentPage} من ${totalPages} (${filteredCustomers.length} عميل)
        </div>
    `;
    
    pagination.innerHTML = paginationHTML;
}

// تغيير الصفحة
function changePage(page) {
    const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);
    
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    updateCustomersTable();
    updatePagination();
    
    // التمرير إلى أعلى الجدول
    document.querySelector('.table-section').scrollIntoView({ behavior: 'smooth' });
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تصدير العملاء
function exportCustomers(format) {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }
    
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'email', label: 'البريد الإلكتروني' },
        { key: 'city', label: 'المدينة' },
        { key: 'activityType', label: 'نوع النشاط' },
        { key: 'lastContact', label: 'آخر تواصل' },
        { key: 'status', label: 'الحالة' },
        { key: 'notes', label: 'الملاحظات' }
    ];
    
    if (format === 'csv') {
        exportToCSV(filteredCustomers, 'customers', headers);
    } else if (format === 'json') {
        exportToJSON(filteredCustomers, 'customers');
    }
    
    logActivity('export_customers', `تصدير بيانات العملاء بصيغة ${format.toUpperCase()}`);
}

// طباعة العملاء
function printCustomers() {
    const headers = [
        { key: 'name', label: 'الاسم' },
        { key: 'phone', label: 'رقم الجوال' },
        { key: 'email', label: 'البريد الإلكتروني' },
        { key: 'city', label: 'المدينة' },
        { key: 'activityType', label: 'نوع النشاط' },
        { key: 'lastContact', label: 'آخر تواصل' },
        { key: 'status', label: 'الحالة' }
    ];

    printData('قائمة العملاء', filteredCustomers, headers);
    logActivity('print_customers', 'طباعة قائمة العملاء');
}

// استيراد العملاء
function importCustomers() {
    if (!hasPermission('add_customers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لاستيراد البيانات');
        return;
    }

    const fileInput = document.getElementById('importFileInput');
    fileInput.click();
}

// معالجة ملف الاستيراد
function handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    const fileName = file.name.toLowerCase();

    if (fileName.endsWith('.csv')) {
        importFromCSV(file);
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        alert('استيراد ملفات Excel قيد التطوير. يرجى استخدام ملف CSV حالياً.');
    } else {
        alert('نوع الملف غير مدعوم. يرجى اختيار ملف CSV أو Excel.');
    }

    // مسح قيمة input
    event.target.value = '';
}

// استيراد من CSV
function importFromCSV(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

            const importedCustomers = [];

            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;

                const values = line.split(',').map(v => v.trim().replace(/"/g, ''));

                if (values.length >= 4) {
                    const customer = {
                        id: generateId(),
                        name: values[0] || '',
                        phone: values[1] || '',
                        email: values[2] || '',
                        city: values[3] || '',
                        activityType: values[4] || 'أخرى',
                        status: values[5] || 'potential',
                        lastContact: values[6] || new Date().toISOString().split('T')[0],
                        notes: values[7] || '',
                        createdAt: new Date().toISOString(),
                        createdBy: getCurrentUser().username
                    };

                    // التحقق من صحة البيانات الأساسية
                    if (customer.name && customer.phone && customer.city) {
                        // التحقق من عدم تكرار رقم الجوال
                        const existingCustomer = customers.find(c => c.phone === customer.phone);
                        if (!existingCustomer) {
                            importedCustomers.push(customer);
                        }
                    }
                }
            }

            if (importedCustomers.length > 0) {
                customers.unshift(...importedCustomers);
                saveCustomers();
                filteredCustomers = [...customers];
                updateCustomersDisplay();

                alert(`تم استيراد ${importedCustomers.length} عميل بنجاح`);
                logActivity('import_customers', `استيراد ${importedCustomers.length} عميل من ملف CSV`);
            } else {
                alert('لم يتم العثور على بيانات صالحة في الملف أو جميع العملاء موجودون مسبقاً');
            }

        } catch (error) {
            console.error('خطأ في استيراد الملف:', error);
            alert('حدث خطأ أثناء استيراد الملف. يرجى التأكد من تنسيق الملف.');
        }
    };

    reader.readAsText(file, 'UTF-8');
}

// عرض نافذة إضافة عميل جديد
function showAddCustomerModal() {
    if (!hasPermission('add_customers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لإضافة عملاء جدد');
        return;
    }

    showCustomerModal();
}

// عرض نافذة العميل (إضافة أو تعديل)
function showCustomerModal(customerId = null) {
    const isEdit = customerId !== null;
    const customer = isEdit ? customers.find(c => c.id === customerId) : null;

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content customer-modal">
            <div class="modal-header">
                <h3 data-ar="${isEdit ? 'تعديل العميل' : 'إضافة عميل جديد'}" data-en="${isEdit ? 'Edit Customer' : 'Add New Customer'}">${isEdit ? 'تعديل العميل' : 'إضافة عميل جديد'}</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="customerForm" class="customer-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerName" data-ar="الاسم *" data-en="Name *">الاسم *</label>
                            <input type="text" id="customerName" name="name" required value="${customer?.name || ''}" placeholder="أدخل اسم العميل">
                        </div>
                        <div class="form-group">
                            <label for="customerPhone" data-ar="رقم الجوال *" data-en="Phone *">رقم الجوال *</label>
                            <input type="tel" id="customerPhone" name="phone" required value="${customer?.phone || ''}" placeholder="05xxxxxxxx">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerEmail" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</label>
                            <input type="email" id="customerEmail" name="email" value="${customer?.email || ''}" placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="customerCity" data-ar="المدينة *" data-en="City *">المدينة *</label>
                            <select id="customerCity" name="city" required>
                                <option value="">اختر المدينة</option>
                                <option value="الرياض" ${customer?.city === 'الرياض' ? 'selected' : ''}>الرياض</option>
                                <option value="جدة" ${customer?.city === 'جدة' ? 'selected' : ''}>جدة</option>
                                <option value="الدمام" ${customer?.city === 'الدمام' ? 'selected' : ''}>الدمام</option>
                                <option value="مكة المكرمة" ${customer?.city === 'مكة المكرمة' ? 'selected' : ''}>مكة المكرمة</option>
                                <option value="المدينة المنورة" ${customer?.city === 'المدينة المنورة' ? 'selected' : ''}>المدينة المنورة</option>
                                <option value="الطائف" ${customer?.city === 'الطائف' ? 'selected' : ''}>الطائف</option>
                                <option value="تبوك" ${customer?.city === 'تبوك' ? 'selected' : ''}>تبوك</option>
                                <option value="أبها" ${customer?.city === 'أبها' ? 'selected' : ''}>أبها</option>
                                <option value="حائل" ${customer?.city === 'حائل' ? 'selected' : ''}>حائل</option>
                                <option value="الخبر" ${customer?.city === 'الخبر' ? 'selected' : ''}>الخبر</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerActivity" data-ar="نوع النشاط *" data-en="Activity Type *">نوع النشاط *</label>
                            <select id="customerActivity" name="activityType" required>
                                <option value="">اختر نوع النشاط</option>
                                <option value="تجارة إلكترونية" ${customer?.activityType === 'تجارة إلكترونية' ? 'selected' : ''}>تجارة إلكترونية</option>
                                <option value="مطاعم" ${customer?.activityType === 'مطاعم' ? 'selected' : ''}>مطاعم</option>
                                <option value="خدمات طبية" ${customer?.activityType === 'خدمات طبية' ? 'selected' : ''}>خدمات طبية</option>
                                <option value="تعليم" ${customer?.activityType === 'تعليم' ? 'selected' : ''}>تعليم</option>
                                <option value="عقارات" ${customer?.activityType === 'عقارات' ? 'selected' : ''}>عقارات</option>
                                <option value="خدمات مالية" ${customer?.activityType === 'خدمات مالية' ? 'selected' : ''}>خدمات مالية</option>
                                <option value="تصنيع" ${customer?.activityType === 'تصنيع' ? 'selected' : ''}>تصنيع</option>
                                <option value="خدمات لوجستية" ${customer?.activityType === 'خدمات لوجستية' ? 'selected' : ''}>خدمات لوجستية</option>
                                <option value="تقنية معلومات" ${customer?.activityType === 'تقنية معلومات' ? 'selected' : ''}>تقنية معلومات</option>
                                <option value="أخرى" ${customer?.activityType === 'أخرى' ? 'selected' : ''}>أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="customerStatus" data-ar="حالة العميل *" data-en="Customer Status *">حالة العميل *</label>
                            <select id="customerStatus" name="status" required>
                                <option value="active" ${customer?.status === 'active' ? 'selected' : ''}>نشط</option>
                                <option value="inactive" ${customer?.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                <option value="potential" ${customer?.status === 'potential' ? 'selected' : ''}>محتمل</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="customerLastContact" data-ar="تاريخ آخر تواصل" data-en="Last Contact Date">تاريخ آخر تواصل</label>
                        <input type="date" id="customerLastContact" name="lastContact" value="${customer?.lastContact || new Date().toISOString().split('T')[0]}">
                    </div>

                    <div class="form-group">
                        <label for="customerNotes" data-ar="الملاحظات" data-en="Notes">الملاحظات</label>
                        <textarea id="customerNotes" name="notes" rows="4" placeholder="أدخل أي ملاحظات إضافية...">${customer?.notes || ''}</textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                    <span data-ar="إلغاء" data-en="Cancel">إلغاء</span>
                </button>
                <button type="button" class="btn-primary" onclick="saveCustomer('${customerId || ''}')">
                    <i class="fas fa-save"></i>
                    <span data-ar="${isEdit ? 'حفظ التغييرات' : 'إضافة العميل'}" data-en="${isEdit ? 'Save Changes' : 'Add Customer'}">${isEdit ? 'حفظ التغييرات' : 'إضافة العميل'}</span>
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    updateLanguage();

    // التركيز على أول حقل
    setTimeout(() => {
        document.getElementById('customerName').focus();
    }, 100);
}

// حفظ العميل
function saveCustomer(customerId) {
    const form = document.getElementById('customerForm');
    const formData = new FormData(form);

    // التحقق من صحة البيانات
    const name = formData.get('name').trim();
    const phone = formData.get('phone').trim();
    const email = formData.get('email').trim();
    const city = formData.get('city');
    const activityType = formData.get('activityType');

    if (!name || !phone || !city || !activityType) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // التحقق من صحة رقم الجوال
    if (!validatePhone(phone)) {
        alert('رقم الجوال غير صحيح');
        return;
    }

    // التحقق من صحة البريد الإلكتروني
    if (email && !validateEmail(email)) {
        alert('البريد الإلكتروني غير صحيح');
        return;
    }

    // التحقق من عدم تكرار رقم الجوال
    const existingCustomer = customers.find(c => c.phone === phone && c.id !== customerId);
    if (existingCustomer) {
        alert('رقم الجوال مسجل مسبقاً لعميل آخر');
        return;
    }

    const customerData = {
        name: sanitizeText(name),
        phone: sanitizeText(phone),
        email: sanitizeText(email),
        city: city,
        activityType: activityType,
        status: formData.get('status'),
        lastContact: formData.get('lastContact') || new Date().toISOString().split('T')[0],
        notes: sanitizeText(formData.get('notes'))
    };

    if (customerId) {
        // تعديل عميل موجود
        const customerIndex = customers.findIndex(c => c.id === customerId);
        if (customerIndex !== -1) {
            customers[customerIndex] = {
                ...customers[customerIndex],
                ...customerData,
                updatedAt: new Date().toISOString(),
                updatedBy: getCurrentUser().username
            };

            logActivity('customer_edit', `تعديل بيانات العميل: ${customerData.name}`);
            alert('تم تحديث بيانات العميل بنجاح');
        }
    } else {
        // إضافة عميل جديد
        const newCustomer = {
            id: generateId(),
            ...customerData,
            createdAt: new Date().toISOString(),
            createdBy: getCurrentUser().username
        };

        customers.unshift(newCustomer);
        logActivity('customer_add', `إضافة عميل جديد: ${customerData.name}`);
        alert('تم إضافة العميل بنجاح');
    }

    // حفظ البيانات وتحديث العرض
    saveCustomers();
    filteredCustomers = [...customers];
    updateCustomersDisplay();

    // إغلاق النافذة
    document.querySelector('.modal-overlay').remove();
}

// عرض تفاصيل العميل
function viewCustomer(customerId) {
    const customer = customers.find(c => c.id === customerId);
    if (!customer) {
        alert('العميل غير موجود');
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content customer-details">
            <div class="modal-header">
                <h3 data-ar="تفاصيل العميل" data-en="Customer Details">تفاصيل العميل</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="customer-info">
                    <div class="customer-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="customer-details-grid">
                        <div class="detail-item">
                            <label data-ar="الاسم:" data-en="Name:">الاسم:</label>
                            <span>${customer.name}</span>
                        </div>
                        <div class="detail-item">
                            <label data-ar="رقم الجوال:" data-en="Phone:">رقم الجوال:</label>
                            <span>${customer.phone}</span>
                        </div>
                        <div class="detail-item">
                            <label data-ar="البريد الإلكتروني:" data-en="Email:">البريد الإلكتروني:</label>
                            <span>${customer.email || 'غير محدد'}</span>
                        </div>
                        <div class="detail-item">
                            <label data-ar="المدينة:" data-en="City:">المدينة:</label>
                            <span>${customer.city}</span>
                        </div>
                        <div class="detail-item">
                            <label data-ar="نوع النشاط:" data-en="Activity Type:">نوع النشاط:</label>
                            <span>${customer.activityType}</span>
                        </div>
                        <div class="detail-item">
                            <label data-ar="الحالة:" data-en="Status:">الحالة:</label>
                            <span class="status-badge status-${customer.status}">${getStatusDisplayName(customer.status)}</span>
                        </div>
                        <div class="detail-item">
                            <label data-ar="آخر تواصل:" data-en="Last Contact:">آخر تواصل:</label>
                            <span>${formatDate(customer.lastContact)}</span>
                        </div>
                        <div class="detail-item">
                            <label data-ar="تاريخ الإضافة:" data-en="Created Date:">تاريخ الإضافة:</label>
                            <span>${formatDate(customer.createdAt, true)}</span>
                        </div>
                        ${customer.notes ? `
                            <div class="detail-item full-width">
                                <label data-ar="الملاحظات:" data-en="Notes:">الملاحظات:</label>
                                <span>${customer.notes}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                    <span data-ar="إغلاق" data-en="Close">إغلاق</span>
                </button>
                ${hasPermission('edit_customers') || hasPermission('all') ? `
                    <button type="button" class="btn-primary" onclick="this.closest('.modal-overlay').remove(); editCustomer('${customer.id}')">
                        <i class="fas fa-edit"></i>
                        <span data-ar="تعديل" data-en="Edit">تعديل</span>
                    </button>
                ` : ''}
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    updateLanguage();
}

// تعديل العميل
function editCustomer(customerId) {
    if (!hasPermission('edit_customers') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتعديل العملاء');
        return;
    }

    showCustomerModal(customerId);
}

// حذف العميل
function deleteCustomer(customerId) {
    if (!hasPermission('all')) {
        alert('ليس لديك صلاحية لحذف العملاء');
        return;
    }

    const customer = customers.find(c => c.id === customerId);
    if (!customer) {
        alert('العميل غير موجود');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        const customerIndex = customers.findIndex(c => c.id === customerId);
        if (customerIndex !== -1) {
            customers.splice(customerIndex, 1);
            saveCustomers();
            filteredCustomers = [...customers];
            updateCustomersDisplay();

            logActivity('customer_delete', `حذف العميل: ${customer.name}`);
            alert('تم حذف العميل بنجاح');
        }
    }
}

// تصدير العملاء إلى Excel
async function exportCustomersExcel() {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }

    const customers = getFilteredCustomers();

    if (customers.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    // إظهار مؤشر التحميل
    const exportBtn = document.querySelector('.excel-btn');
    const originalContent = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري التصدير...</span>';
    exportBtn.disabled = true;

    try {
        const headers = [
            { key: 'name', label: 'اسم العميل' },
            { key: 'phone', label: 'رقم الجوال' },
            { key: 'email', label: 'البريد الإلكتروني' },
            { key: 'city', label: 'المدينة' },
            { key: 'activityType', label: 'نوع النشاط' },
            { key: 'lastContact', label: 'آخر تواصل' },
            { key: 'status', label: 'الحالة' },
            { key: 'notes', label: 'الملاحظات' },
            { key: 'createdAt', label: 'تاريخ الإضافة' }
        ];

        // تحضير البيانات مع تنسيق التواريخ
        const formattedCustomers = customers.map(customer => ({
            ...customer,
            lastContact: customer.lastContact ? formatDate(customer.lastContact, true) : 'لا يوجد',
            createdAt: customer.createdAt ? formatDate(customer.createdAt, true) : 'غير محدد',
            status: getStatusDisplayName(customer.status),
            email: customer.email || 'غير محدد',
            notes: customer.notes || 'لا توجد ملاحظات'
        }));

        const success = await exportToExcel(
            formattedCustomers,
            'بيانات_العملاء',
            headers,
            {
                sheetName: 'العملاء',
                title: 'تقرير العملاء'
            }
        );

        if (success) {
            showNotification('تم تصدير بيانات العملاء بنجاح', 'success');
        }

    } catch (error) {
        console.error('خطأ في تصدير Excel:', error);
        showNotification('حدث خطأ أثناء تصدير البيانات', 'error');
    } finally {
        // إعادة تعيين الزر
        exportBtn.innerHTML = originalContent;
        exportBtn.disabled = false;
    }
}

// تصدير العملاء إلى PDF
async function exportCustomersPDF() {
    if (!hasPermission('export_data') && !hasPermission('all')) {
        alert('ليس لديك صلاحية لتصدير البيانات');
        return;
    }

    const customers = getFilteredCustomers();

    if (customers.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    // إظهار مؤشر التحميل
    const exportBtn = document.querySelector('.pdf-btn');
    const originalContent = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري التصدير...</span>';
    exportBtn.disabled = true;

    try {
        const headers = [
            { key: 'name', label: 'الاسم' },
            { key: 'phone', label: 'الجوال' },
            { key: 'city', label: 'المدينة' },
            { key: 'activityType', label: 'النشاط' },
            { key: 'status', label: 'الحالة' }
        ];

        // تحضير البيانات مع تنسيق مبسط للـ PDF
        const formattedCustomers = customers.map(customer => ({
            ...customer,
            status: getStatusDisplayName(customer.status)
        }));

        const success = await exportToPDF(
            formattedCustomers,
            'تقرير_العملاء',
            headers,
            {
                title: 'تقرير العملاء'
            }
        );

        if (success) {
            showNotification('تم تصدير تقرير العملاء بنجاح', 'success');
        }

    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        showNotification('حدث خطأ أثناء تصدير التقرير', 'error');
    } finally {
        // إعادة تعيين الزر
        exportBtn.innerHTML = originalContent;
        exportBtn.disabled = false;
    }
}

// الحصول على العملاء المفلترين
function getFilteredCustomers() {
    return filteredCustomers;
}

// عرض الإشعارات (إذا لم تكن موجودة)
function showNotification(message, type = 'info') {
    // التحقق من وجود الدالة في dashboard.js
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }

    // إنشاء إشعار بسيط
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#2563eb'};
    `;

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"
               style="color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#2563eb'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}
