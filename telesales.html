<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة فريق التلي سيلز - نظام إدارة العملاء والموردين</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- شريط التنقل العلوي -->
    <nav class="top-navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="menu-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="nav-title" data-ar="إدارة فريق التلي سيلز" data-en="Telesales Team Management">إدارة فريق التلي سيلز</h1>
            </div>
            
            <div class="nav-right">
                <button class="lang-toggle" onclick="toggleLanguage()">
                    <i class="fas fa-globe"></i>
                    <span id="langText">English</span>
                </button>
                
                <div class="user-menu">
                    <button class="user-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-user-circle"></i>
                        <span id="userName">المستخدم</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" onclick="showProfile()">
                            <i class="fas fa-user"></i>
                            <span data-ar="الملف الشخصي" data-en="Profile">الملف الشخصي</span>
                        </a>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span data-ar="الإعدادات" data-en="Settings">الإعدادات</span>
                        </a>
                        <hr>
                        <a href="#" onclick="logout()" class="logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            <span data-ar="تسجيل الخروج" data-en="Logout">تسجيل الخروج</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-users-cog"></i>
            <span data-ar="نظام الإدارة" data-en="Management System">نظام الإدارة</span>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span data-ar="لوحة التحكم" data-en="Dashboard">لوحة التحكم</span>
            </a>
            
            <a href="customers.html" class="nav-item">
                <i class="fas fa-users"></i>
                <span data-ar="إدارة العملاء" data-en="Customer Management">إدارة العملاء</span>
                <span class="nav-badge" id="customersBadge">0</span>
            </a>
            
            <a href="suppliers.html" class="nav-item">
                <i class="fas fa-truck"></i>
                <span data-ar="إدارة الموردين" data-en="Supplier Management">إدارة الموردين</span>
                <span class="nav-badge" id="suppliersBadge">0</span>
            </a>
            
            <a href="telesales.html" class="nav-item active">
                <i class="fas fa-headset"></i>
                <span data-ar="فريق التلي سيلز" data-en="Telesales Team">فريق التلي سيلز</span>
                <span class="nav-badge" id="telesalesBadge">0</span>
            </a>
            
            <div class="nav-divider"></div>
            
            <a href="reports.html" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span data-ar="التقارير" data-en="Reports">التقارير</span>
            </a>
            
            <a href="activities.html" class="nav-item">
                <i class="fas fa-history"></i>
                <span data-ar="سجل الأنشطة" data-en="Activity Log">سجل الأنشطة</span>
            </a>
        </nav>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="content-container">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <div class="page-title">
                    <h2 data-ar="إدارة فريق التلي سيلز" data-en="Telesales Team Management">إدارة فريق التلي سيلز</h2>
                    <p data-ar="إدارة الموظفين والمهام والأداء" data-en="Manage employees, tasks and performance">إدارة الموظفين والمهام والأداء</p>
                </div>
                
                <div class="page-actions">
                    <button class="btn-primary" onclick="showAddEmployeeModal()" id="addEmployeeBtn">
                        <i class="fas fa-user-plus"></i>
                        <span data-ar="إضافة موظف جديد" data-en="Add New Employee">إضافة موظف جديد</span>
                    </button>
                </div>
            </div>

            <!-- أدوات البحث والفلترة -->
            <div class="filters-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="البحث في الموظفين..." data-ar-placeholder="البحث في الموظفين..." data-en-placeholder="Search employees...">
                </div>
                
                <div class="filters-row">
                    <select id="departmentFilter" class="filter-select">
                        <option value="all" data-ar="جميع الأقسام" data-en="All Departments">جميع الأقسام</option>
                        <option value="telesales" data-ar="تلي سيلز" data-en="Telesales">تلي سيلز</option>
                        <option value="customer_service" data-ar="خدمة العملاء" data-en="Customer Service">خدمة العملاء</option>
                        <option value="sales" data-ar="المبيعات" data-en="Sales">المبيعات</option>
                    </select>
                    
                    <select id="statusFilter" class="filter-select">
                        <option value="all" data-ar="جميع الحالات" data-en="All Status">جميع الحالات</option>
                        <option value="active" data-ar="نشط" data-en="Active">نشط</option>
                        <option value="inactive" data-ar="غير نشط" data-en="Inactive">غير نشط</option>
                        <option value="vacation" data-ar="في إجازة" data-en="On Vacation">في إجازة</option>
                    </select>
                    
                    <select id="performanceFilter" class="filter-select">
                        <option value="all" data-ar="جميع مستويات الأداء" data-en="All Performance Levels">جميع مستويات الأداء</option>
                        <option value="excellent" data-ar="ممتاز" data-en="Excellent">ممتاز</option>
                        <option value="good" data-ar="جيد" data-en="Good">جيد</option>
                        <option value="average" data-ar="متوسط" data-en="Average">متوسط</option>
                        <option value="poor" data-ar="ضعيف" data-en="Poor">ضعيف</option>
                    </select>
                    
                    <button class="btn-secondary" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                        <span data-ar="مسح الفلاتر" data-en="Clear Filters">مسح الفلاتر</span>
                    </button>
                </div>
                
                <div class="export-actions">
                    <button class="btn-import" onclick="importEmployees()">
                        <i class="fas fa-file-import"></i>
                        <span data-ar="استيراد Excel" data-en="Import Excel">استيراد Excel</span>
                    </button>
                    
                    <button class="btn-export" onclick="exportEmployees('csv')">
                        <i class="fas fa-file-csv"></i>
                        <span data-ar="تصدير CSV" data-en="Export CSV">تصدير CSV</span>
                    </button>
                    
                    <button class="btn-export" onclick="exportEmployees('json')">
                        <i class="fas fa-file-code"></i>
                        <span data-ar="تصدير JSON" data-en="Export JSON">تصدير JSON</span>
                    </button>
                    
                    <button class="btn-export" onclick="printEmployees()">
                        <i class="fas fa-print"></i>
                        <span data-ar="طباعة" data-en="Print">طباعة</span>
                    </button>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-row">
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <div class="stat-content">
                        <h3 id="totalEmployeesCount">0</h3>
                        <p data-ar="إجمالي الموظفين" data-en="Total Employees">إجمالي الموظفين</p>
                    </div>
                </div>
                
                <div class="stat-item">
                    <i class="fas fa-user-check"></i>
                    <div class="stat-content">
                        <h3 id="activeEmployeesCount">0</h3>
                        <p data-ar="موظفين نشطون" data-en="Active Employees">موظفين نشطون</p>
                    </div>
                </div>
                
                <div class="stat-item">
                    <i class="fas fa-tasks"></i>
                    <div class="stat-content">
                        <h3 id="todayTasksCount">0</h3>
                        <p data-ar="مهام اليوم" data-en="Today's Tasks">مهام اليوم</p>
                    </div>
                </div>
                
                <div class="stat-item">
                    <i class="fas fa-chart-line"></i>
                    <div class="stat-content">
                        <h3 id="avgPerformanceScore">0%</h3>
                        <p data-ar="متوسط الأداء" data-en="Average Performance">متوسط الأداء</p>
                    </div>
                </div>
            </div>

            <!-- جدول الموظفين -->
            <div class="table-section">
                <div class="table-header">
                    <h3 data-ar="قائمة الموظفين" data-en="Employee List">قائمة الموظفين</h3>
                    <div class="table-controls">
                        <select id="sortBy" class="sort-select">
                            <option value="name" data-ar="ترتيب حسب الاسم" data-en="Sort by Name">ترتيب حسب الاسم</option>
                            <option value="department" data-ar="ترتيب حسب القسم" data-en="Sort by Department">ترتيب حسب القسم</option>
                            <option value="performance" data-ar="ترتيب حسب الأداء" data-en="Sort by Performance">ترتيب حسب الأداء</option>
                            <option value="joinDate" data-ar="ترتيب حسب تاريخ الانضمام" data-en="Sort by Join Date">ترتيب حسب تاريخ الانضمام</option>
                        </select>
                        
                        <button class="sort-order-btn" onclick="toggleSortOrder()" id="sortOrderBtn">
                            <i class="fas fa-sort-amount-down"></i>
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="customers-table" id="employeesTable">
                        <thead>
                            <tr>
                                <th data-ar="الاسم" data-en="Name">الاسم</th>
                                <th data-ar="القسم" data-en="Department">القسم</th>
                                <th data-ar="رقم الجوال" data-en="Phone">رقم الجوال</th>
                                <th data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</th>
                                <th data-ar="تاريخ الانضمام" data-en="Join Date">تاريخ الانضمام</th>
                                <th data-ar="الأداء" data-en="Performance">الأداء</th>
                                <th data-ar="الحالة" data-en="Status">الحالة</th>
                                <th data-ar="الإجراءات" data-en="Actions">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="employeesTableBody">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
                
                <div class="table-pagination" id="pagination">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- قسم المهام اليومية -->
            <div class="dashboard-section" id="tasksSection">
                <div class="section-header">
                    <h3 data-ar="المهام اليومية" data-en="Daily Tasks">المهام اليومية</h3>
                    <button class="btn-secondary" onclick="showAddTaskModal()">
                        <i class="fas fa-plus"></i>
                        <span data-ar="إضافة مهمة" data-en="Add Task">إضافة مهمة</span>
                    </button>
                </div>
                
                <div class="tasks-grid" id="tasksGrid">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
    </main>

    <!-- ملف الاستيراد المخفي -->
    <input type="file" id="importFileInput" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleFileImport(event)">

    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/telesales.js"></script>
</body>
</html>
