<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المرحلة الخامسة - نظام إدارة المهام والمتابعة المتقدم</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/customers.css">
    <link rel="stylesheet" href="css/tasks.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: var(--primary-blue); margin-bottom: 30px;">
            <i class="fas fa-tasks"></i>
            اختبار المرحلة الخامسة - نظام إدارة المهام والمتابعة المتقدم
        </h1>

        <!-- إشعار المرحلة الجديدة -->
        <div class="phase-announcement">
            <div class="announcement-header">
                <i class="fas fa-rocket"></i>
                <h2>🚀 المرحلة الخامسة: نظام إدارة المهام والمتابعة المتقدم</h2>
            </div>
            <div class="announcement-content">
                <p><strong>تم تطوير نظام شامل لإدارة المهام والمتابعة يتضمن:</strong></p>
                <div class="features-showcase">
                    <div class="feature-highlight">
                        <i class="fas fa-chart-bar"></i>
                        <h4>إحصائيات المهام التفاعلية</h4>
                        <p>بطاقات ملونة تعرض إجمالي المهام، المعلقة، المكتملة، والمتأخرة مع شرائط تقدم</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-filter"></i>
                        <h4>فلاتر ذكية متقدمة</h4>
                        <p>فلترة حسب الحالة، الأولوية، المسؤول، تاريخ الاستحقاق مع بحث شامل</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-table"></i>
                        <h4>جدول تفاعلي محسن</h4>
                        <p>جدول متطور مع ترتيب، تحديد متعدد، وإجراءات مجمعة</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-eye"></i>
                        <h4>عروض متعددة</h4>
                        <p>عرض قائمة، كانبان، وتقويم للمهام حسب التفضيل</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-users"></i>
                        <h4>إدارة المسؤولين</h4>
                        <p>تعيين المهام للموظفين مع تتبع الأداء والإنجاز</p>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-download"></i>
                        <h4>تصدير وتقارير</h4>
                        <p>تصدير المهام بصيغ متعددة مع تقارير تفصيلية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم اختبار إحصائيات المهام -->
        <div class="test-section tasks-stats-section">
            <h2><i class="fas fa-chart-bar"></i> اختبار إحصائيات المهام التفاعلية</h2>
            <div class="test-buttons">
                <button class="btn-total-tasks" onclick="testTotalTasks()">
                    <i class="fas fa-tasks"></i>
                    إجمالي المهام
                </button>
                <button class="btn-pending-tasks" onclick="testPendingTasks()">
                    <i class="fas fa-clock"></i>
                    المهام المعلقة
                </button>
                <button class="btn-completed-tasks" onclick="testCompletedTasks()">
                    <i class="fas fa-check-circle"></i>
                    المهام المكتملة
                </button>
                <button class="btn-overdue-tasks" onclick="testOverdueTasks()">
                    <i class="fas fa-exclamation-triangle"></i>
                    المهام المتأخرة
                </button>
            </div>
            <div class="test-description">
                <p><strong>إحصائيات المهام التفاعلية تتضمن:</strong></p>
                <ul>
                    <li>📊 <strong>بطاقات ملونة:</strong> كل نوع مهمة له لون مميز</li>
                    <li>📈 <strong>شرائط التقدم:</strong> مؤشرات بصرية للنسب والمعدلات</li>
                    <li>🎯 <strong>أرقام ديناميكية:</strong> تحديث فوري للإحصائيات</li>
                    <li>⚡ <strong>تأثيرات تفاعلية:</strong> حركة سلسة عند التمرير</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار الفلاتر المتقدمة -->
        <div class="test-section filters-section">
            <h2><i class="fas fa-filter"></i> اختبار الفلاتر والبحث المتقدم</h2>
            <div class="test-buttons">
                <button class="btn-search-filter" onclick="testSearchFilter()">
                    <i class="fas fa-search"></i>
                    البحث الذكي
                </button>
                <button class="btn-status-filter" onclick="testStatusFilter()">
                    <i class="fas fa-list"></i>
                    فلتر الحالة
                </button>
                <button class="btn-priority-filter" onclick="testPriorityFilter()">
                    <i class="fas fa-flag"></i>
                    فلتر الأولوية
                </button>
                <button class="btn-date-filter" onclick="testDateFilter()">
                    <i class="fas fa-calendar"></i>
                    فلتر التاريخ
                </button>
            </div>
            <div class="test-description">
                <p><strong>نظام الفلاتر المتقدم يوفر:</strong></p>
                <ul>
                    <li>🔍 <strong>بحث شامل:</strong> في العنوان، الوصف، المسؤول، والعميل</li>
                    <li>📋 <strong>فلترة الحالة:</strong> معلقة، قيد التنفيذ، مكتملة، ملغية</li>
                    <li>🚩 <strong>فلترة الأولوية:</strong> عالية، متوسطة، منخفضة</li>
                    <li>📅 <strong>فلترة التاريخ:</strong> اليوم، غداً، هذا الأسبوع، متأخرة</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار الجدول التفاعلي -->
        <div class="test-section table-section">
            <h2><i class="fas fa-table"></i> اختبار الجدول التفاعلي المحسن</h2>
            <div class="test-buttons">
                <button class="btn-table-sorting" onclick="testTableSorting()">
                    <i class="fas fa-sort"></i>
                    ترتيب الجدول
                </button>
                <button class="btn-multi-select" onclick="testMultiSelect()">
                    <i class="fas fa-check-square"></i>
                    التحديد المتعدد
                </button>
                <button class="btn-bulk-actions" onclick="testBulkActions()">
                    <i class="fas fa-list-check"></i>
                    الإجراءات المجمعة
                </button>
                <button class="btn-pagination" onclick="testPagination()">
                    <i class="fas fa-chevron-left"></i>
                    الترقيم
                </button>
            </div>
            <div class="test-description">
                <p><strong>الجدول التفاعلي المحسن يتضمن:</strong></p>
                <ul>
                    <li>🔄 <strong>ترتيب ديناميكي:</strong> ترتيب حسب أي عمود بنقرة واحدة</li>
                    <li>☑️ <strong>تحديد متعدد:</strong> تحديد مهام متعددة للإجراءات المجمعة</li>
                    <li>⚡ <strong>إجراءات سريعة:</strong> عرض، تعديل، إكمال، حذف</li>
                    <li>📄 <strong>ترقيم ذكي:</strong> تنقل سهل بين الصفحات</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار العروض المتعددة -->
        <div class="test-section views-section">
            <h2><i class="fas fa-eye"></i> اختبار العروض المتعددة</h2>
            <div class="test-buttons">
                <button class="btn-list-view" onclick="testListView()">
                    <i class="fas fa-list"></i>
                    عرض القائمة
                </button>
                <button class="btn-kanban-view" onclick="testKanbanView()">
                    <i class="fas fa-columns"></i>
                    عرض كانبان
                </button>
                <button class="btn-calendar-view" onclick="testCalendarView()">
                    <i class="fas fa-calendar"></i>
                    عرض التقويم
                </button>
                <button class="btn-view-toggle" onclick="testViewToggle()">
                    <i class="fas fa-exchange-alt"></i>
                    تبديل العروض
                </button>
            </div>
            <div class="test-description">
                <p><strong>العروض المتعددة تشمل:</strong></p>
                <ul>
                    <li>📋 <strong>عرض القائمة:</strong> جدول تفصيلي مع جميع المعلومات</li>
                    <li>📊 <strong>عرض كانبان:</strong> بطاقات مرتبة حسب الحالة</li>
                    <li>📅 <strong>عرض التقويم:</strong> المهام موزعة على التقويم</li>
                    <li>🔄 <strong>تبديل سلس:</strong> انتقال سهل بين العروض</li>
                </ul>
            </div>
        </div>

        <!-- قسم اختبار إدارة المهام -->
        <div class="test-section management-section">
            <h2><i class="fas fa-cogs"></i> اختبار إدارة المهام</h2>
            <div class="test-buttons">
                <button class="btn-add-task" onclick="testAddTask()">
                    <i class="fas fa-plus"></i>
                    إضافة مهمة
                </button>
                <button class="btn-edit-task" onclick="testEditTask()">
                    <i class="fas fa-edit"></i>
                    تعديل مهمة
                </button>
                <button class="btn-complete-task" onclick="testCompleteTask()">
                    <i class="fas fa-check"></i>
                    إكمال مهمة
                </button>
                <button class="btn-delete-task" onclick="testDeleteTask()">
                    <i class="fas fa-trash"></i>
                    حذف مهمة
                </button>
            </div>
            <div class="test-description">
                <p><strong>إدارة المهام تتضمن:</strong></p>
                <ul>
                    <li>➕ <strong>إضافة مهام:</strong> نموذج شامل لإنشاء مهام جديدة</li>
                    <li>✏️ <strong>تعديل المهام:</strong> تحديث جميع تفاصيل المهمة</li>
                    <li>✅ <strong>إكمال المهام:</strong> تمييز المهام كمكتملة</li>
                    <li>🗑️ <strong>حذف المهام:</strong> إزالة المهام مع تأكيد الأمان</li>
                </ul>
            </div>
        </div>

        <!-- قسم الروابط السريعة -->
        <div class="test-section quick-links-section">
            <h2><i class="fas fa-link"></i> الروابط السريعة</h2>
            <div class="test-buttons">
                <a href="tasks.html" class="btn-primary">
                    <i class="fas fa-tasks"></i>
                    صفحة إدارة المهام
                </a>
                <a href="dashboard.html" class="btn-secondary">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="reports.html" class="btn-info">
                    <i class="fas fa-chart-bar"></i>
                    التقارير والتحليلات
                </a>
                <a href="customers.html" class="btn-success">
                    <i class="fas fa-users"></i>
                    إدارة العملاء
                </a>
            </div>
            <div class="test-description">
                <p><strong>انتقل إلى الصفحات المختلفة لاختبار:</strong></p>
                <ul>
                    <li>📋 <strong>إدارة المهام:</strong> النظام الجديد لإدارة المهام والمتابعة</li>
                    <li>🏠 <strong>لوحة التحكم:</strong> نظرة عامة على النظام</li>
                    <li>📊 <strong>التقارير:</strong> تقارير وتحليلات شاملة</li>
                    <li>👥 <strong>إدارة العملاء:</strong> قاعدة بيانات العملاء</li>
                </ul>
            </div>
        </div>

        <!-- ملخص الإنجازات -->
        <div class="achievements-summary">
            <h2><i class="fas fa-trophy"></i> ملخص إنجازات المرحلة الخامسة</h2>
            <div class="achievements-grid">
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>نظام إدارة المهام المتقدم</h4>
                    <p>تم تطوير نظام شامل لإدارة المهام مع واجهة احترافية</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>إحصائيات تفاعلية</h4>
                    <p>تم إضافة بطاقات إحصائيات ملونة مع شرائط تقدم</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>فلاتر ذكية متقدمة</h4>
                    <p>تم تطوير نظام فلترة شامل مع بحث متقدم</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>جدول تفاعلي محسن</h4>
                    <p>تم إنشاء جدول متطور مع ترتيب وإجراءات مجمعة</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>عروض متعددة</h4>
                    <p>تم تصميم عروض قائمة، كانبان، وتقويم</p>
                </div>
                <div class="achievement-item completed">
                    <i class="fas fa-check-circle"></i>
                    <h4>تصميم متجاوب</h4>
                    <p>تم تحسين التصميم ليعمل على جميع الأجهزة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>

    <style>
        /* أنماط خاصة بصفحة اختبار المرحلة الخامسة */
        .phase-announcement {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .announcement-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
        }

        .announcement-header i {
            font-size: 3rem;
            color: #fbbf24;
        }

        .announcement-header h2 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .features-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .feature-highlight {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .feature-highlight:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .feature-highlight i {
            font-size: 2.5rem;
            color: #fbbf24;
            margin-bottom: 15px;
        }

        .feature-highlight h4 {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .feature-highlight p {
            margin: 0;
            opacity: 0.9;
            line-height: 1.6;
        }

        /* أنماط أقسام الاختبار المتخصصة */
        .tasks-stats-section {
            border-left: 5px solid #2563eb;
            background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
        }

        .filters-section {
            border-left: 5px solid #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
        }

        .table-section {
            border-left: 5px solid #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
        }

        .views-section {
            border-left: 5px solid #8b5cf6;
            background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
        }

        .management-section {
            border-left: 5px solid #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }

        .quick-links-section {
            border-left: 5px solid #06b6d4;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }

        /* أزرار متخصصة للمهام */
        .btn-total-tasks,
        .btn-pending-tasks,
        .btn-completed-tasks,
        .btn-overdue-tasks {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
        }

        .btn-total-tasks {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }

        .btn-pending-tasks {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-completed-tasks {
            background: linear-gradient(135deg, #10b981, #059669);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .btn-overdue-tasks {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-total-tasks:hover,
        .btn-pending-tasks:hover,
        .btn-completed-tasks:hover,
        .btn-overdue-tasks:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        /* أزرار الفلاتر */
        .btn-search-filter,
        .btn-status-filter,
        .btn-priority-filter,
        .btn-date-filter {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-search-filter:hover,
        .btn-status-filter:hover,
        .btn-priority-filter:hover,
        .btn-date-filter:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
        }

        /* أزرار الجدول */
        .btn-table-sorting,
        .btn-multi-select,
        .btn-bulk-actions,
        .btn-pagination {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .btn-table-sorting:hover,
        .btn-multi-select:hover,
        .btn-bulk-actions:hover,
        .btn-pagination:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        /* أزرار العروض */
        .btn-list-view,
        .btn-kanban-view,
        .btn-calendar-view,
        .btn-view-toggle {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }

        .btn-list-view:hover,
        .btn-kanban-view:hover,
        .btn-calendar-view:hover,
        .btn-view-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
        }

        /* أزرار الإدارة */
        .btn-add-task,
        .btn-edit-task,
        .btn-complete-task,
        .btn-delete-task {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
        }

        .btn-add-task {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }

        .btn-edit-task {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-complete-task {
            background: linear-gradient(135deg, #10b981, #059669);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .btn-delete-task {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-add-task:hover,
        .btn-edit-task:hover,
        .btn-complete-task:hover,
        .btn-delete-task:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        /* ملخص الإنجازات */
        .achievements-summary {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            border: 2px solid #e2e8f0;
        }

        .achievements-summary h2 {
            color: var(--primary-blue);
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .achievement-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #10b981;
            transition: var(--transition);
        }

        .achievement-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .achievement-item.completed i {
            color: #10b981;
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .achievement-item h4 {
            color: var(--primary-blue);
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .achievement-item p {
            color: var(--gray);
            margin: 0;
            line-height: 1.6;
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .features-showcase {
                grid-template-columns: 1fr;
            }

            .announcement-header {
                flex-direction: column;
                text-align: center;
            }

            .announcement-header h2 {
                font-size: 1.5rem;
            }

            .achievements-grid {
                grid-template-columns: 1fr;
            }

            .phase-announcement {
                margin-left: -10px;
                margin-right: -10px;
                border-radius: 0;
            }
        }
    </style>

    <script>
        // دوال اختبار إحصائيات المهام
        function testTotalTasks() {
            showNotification('📊 إجمالي المهام - عرض العدد الكلي للمهام مع شريط تقدم', 'success');
        }

        function testPendingTasks() {
            showNotification('⏰ المهام المعلقة - المهام التي تحتاج إلى متابعة وإنجاز', 'info');
        }

        function testCompletedTasks() {
            showNotification('✅ المهام المكتملة - المهام التي تم إنجازها بنجاح', 'success');
        }

        function testOverdueTasks() {
            showNotification('⚠️ المهام المتأخرة - المهام التي تجاوزت تاريخ الاستحقاق', 'warning');
        }

        // دوال اختبار الفلاتر
        function testSearchFilter() {
            showNotification('🔍 البحث الذكي - بحث شامل في العنوان والوصف والمسؤول', 'success');
        }

        function testStatusFilter() {
            showNotification('📋 فلتر الحالة - فلترة المهام حسب حالة التنفيذ', 'info');
        }

        function testPriorityFilter() {
            showNotification('🚩 فلتر الأولوية - فلترة المهام حسب مستوى الأولوية', 'info');
        }

        function testDateFilter() {
            showNotification('📅 فلتر التاريخ - فلترة المهام حسب تاريخ الاستحقاق', 'info');
        }

        // دوال اختبار الجدول
        function testTableSorting() {
            showNotification('🔄 ترتيب الجدول - ترتيب المهام حسب أي عمود', 'success');
        }

        function testMultiSelect() {
            showNotification('☑️ التحديد المتعدد - تحديد عدة مهام للإجراءات المجمعة', 'success');
        }

        function testBulkActions() {
            showNotification('⚡ الإجراءات المجمعة - تطبيق إجراءات على مهام متعددة', 'info');
        }

        function testPagination() {
            showNotification('📄 الترقيم - تنقل سهل بين صفحات المهام', 'info');
        }

        // دوال اختبار العروض
        function testListView() {
            showNotification('📋 عرض القائمة - جدول تفصيلي مع جميع معلومات المهام', 'success');
        }

        function testKanbanView() {
            showNotification('📊 عرض كانبان - بطاقات مرتبة حسب حالة المهمة', 'info');
        }

        function testCalendarView() {
            showNotification('📅 عرض التقويم - المهام موزعة على التقويم الشهري', 'info');
        }

        function testViewToggle() {
            showNotification('🔄 تبديل العروض - انتقال سلس بين أنواع العروض المختلفة', 'success');
        }

        // دوال اختبار الإدارة
        function testAddTask() {
            showNotification('➕ إضافة مهمة - نموذج شامل لإنشاء مهام جديدة', 'success');
        }

        function testEditTask() {
            showNotification('✏️ تعديل مهمة - تحديث جميع تفاصيل المهمة', 'info');
        }

        function testCompleteTask() {
            showNotification('✅ إكمال مهمة - تمييز المهام كمكتملة مع تحديث التقدم', 'success');
        }

        function testDeleteTask() {
            showNotification('🗑️ حذف مهمة - إزالة المهام مع تأكيد الأمان', 'warning');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('🚀 مرحباً بك في المرحلة الخامسة - نظام إدارة المهام والمتابعة المتقدم!', 'success');
        });
    </script>
</body>
</html>